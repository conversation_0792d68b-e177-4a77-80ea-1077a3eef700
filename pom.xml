<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
    <groupId>com.dib</groupId>
    <artifactId>idp-ms</artifactId>
    <version>3.8.2</version>

    <name>idp-ms</name>
    <description>DataIntegratedPlatform</description>
    
    <properties>
        <rversion>3.8.2</rversion>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <maven-compiler-plugin.version>3.1</maven-compiler-plugin.version>
        <druid.version>1.2.23</druid.version>
        <bitwalker.version>1.21</bitwalker.version>

        <kaptcha.version>2.3.2</kaptcha.version>
        <mybatis-spring-boot.version>3.5.6</mybatis-spring-boot.version>
        <pagehelper.boot.version>2.1.0</pagehelper.boot.version>
        <fastjson.version>1.2.80</fastjson.version>
        <oshi.version>6.1.2</oshi.version>
        <jna.version>5.10.0</jna.version>
        <commons.io.version>2.11.0</commons.io.version>
        <commons.fileupload.version>1.4</commons.fileupload.version>
        <commons.collections.version>3.2.2</commons.collections.version>
        <poi.version>4.1.2</poi.version>
        <velocity.version>2.3</velocity.version>
        <jwt.version>0.9.1</jwt.version>
        <spring-boot.version>3.1.9</spring-boot.version>
        <spring.cloud.version>2022.0.3</spring.cloud.version>
        <spring.cloud.alibaba.version>2022.0.0.0</spring.cloud.alibaba.version>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.test.skip>true</maven.test.skip>
        <commons-lang3.version>3.3.2</commons-lang3.version>
        <slf4j-api.version>1.7.28</slf4j-api.version>
        <logback-classic.version>1.2.3</logback-classic.version>
        <commons-io.version>2.4</commons-io.version>
        <junit.version>4.12</junit.version>
        <hutool.version>5.8.35</hutool.version>
        <postgresql.version>42.2.5</postgresql.version>
        <mysql-connector.version>8.0.33</mysql-connector.version>
        <oracle-connector.version>********</oracle-connector.version>
        <postgresql.version>42.6.1</postgresql.version>
        <mariadb.version>3.1.4</mariadb.version>
        <groovy.version>3.0.21</groovy.version>
        <mybatisplus.version>3.5.6</mybatisplus.version>
        <swagger-models.version>1.5.21</swagger-models.version>
        <swagger-bootstrap-ui.version>1.9.6</swagger-bootstrap-ui.version>
        <jjwt.version>0.9.0</jjwt.version>
        <netty.version>4.1.43.Final</netty.version>
        <hessian.version>4.0.63</hessian.version>
        <hadoop.version>2.7.3</hadoop.version>
        <hive.jdbc.version>2.1.0</hive.jdbc.version>
        <hbase.version>1.3.0</hbase.version>
        <mongo-java-driver.version>3.4.2</mongo-java-driver.version>
        <phoenix.version>5.0.0-HBase-2.0</phoenix.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.test.skip>false</maven.test.skip>
        <downloadSources>true</downloadSources>
        <java_source_version>17</java_source_version>
        <java_target_version>17</java_target_version>
        <file_encoding>UTF-8</file_encoding>
        <flink_streaming_version>1.4.0.RELEASE</flink_streaming_version>
        <flink.version>1.16.2</flink.version>
        <scala.binary.version>2.12</scala.binary.version>
        <sa-token.version>1.37.0</sa-token.version>
    </properties>
	
    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.21</version>
            </dependency>

            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-core</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna</artifactId>
                <version>${jna.version}</version>
            </dependency>

            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna-platform</artifactId>
                <version>${jna.version}</version>
            </dependency>

            <!-- SpringDoc OpenAPI -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>2.5.0</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- 文件上传工具类 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- collections工具类 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons.collections.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- Sa-Token 权限认证框架 -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <!-- Sa-Token 整合 Redis （使用jackson序列化方式） -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-jackson</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <!-- Sa-Token 整合 Redis （使用 jackson 序列化方式） -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-alone-redis</artifactId>
                <version>${sa-token.version}</version>
            </dependency>

            <!-- 定时任务-->
            <dependency>
                <groupId>com.dib</groupId>
                <artifactId>idp-quartz</artifactId>
                <version>${rversion}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>com.dib</groupId>
                <artifactId>idp-generator</artifactId>
                <version>${rversion}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>com.dib</groupId>
                <artifactId>idp-framework</artifactId>
                <version>${rversion}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.dib</groupId>
                <artifactId>idp-system</artifactId>
                <version>${rversion}</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>com.dib</groupId>
                <artifactId>idp-common</artifactId>
                <version>${rversion}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <modules>
        <module>idp-admin</module>
        <module>idp-framework</module>
        <module>idp-system</module>
        <module>idp-quartz</module>
        <module>idp-generator</module>
        <module>idp-common</module>
        <module>idp-core</module>
        <module>idp-rpc</module>
        <module>idp-datax-executor</module>
        <module>idp-data-integration</module>
        <module>idp-data-market</module>
        <module>idp-data-property</module>
        <module>idp-index-manage</module>
        <module>idp-model-manage</module>
        <module>idp-data-analysis</module>
    </modules>
    <packaging>pom</packaging>


    <dependencies>

    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>