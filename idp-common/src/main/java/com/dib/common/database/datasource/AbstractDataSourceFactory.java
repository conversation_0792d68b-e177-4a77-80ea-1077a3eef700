package com.dib.common.database.datasource;

import com.dib.common.database.query.*;
import com.dib.common.database.service.DataSourceFactory;
import com.dib.common.database.service.DbDialect;
import com.dib.common.database.service.DbQuery;
import com.dib.common.database.DialectFactory;
import com.dib.common.database.constants.DbQueryProperty;
import com.dib.common.database.constants.DbType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

@Component
@Slf4j
public  class AbstractDataSourceFactory implements DataSourceFactory {

    static {
        for (DbType value : DbType.values()) {
            if(StringUtils.isNotBlank(value.getJdbc())){
                try {
                    Class.forName(value.getJdbc());
                }catch (Exception e) {
                    log.error("数据库驱动{}加载失败",value.getJdbc(),e);
                }
            }
        }
    }

    @Autowired
    private CacheDataSourceFactoryBean cacheDataSourceFactoryBean;

    public DbQuery createDbQuery(DbQueryProperty property) {
        DbType dbType = property.getDbType();

        property.viald(dbType);

        JdbcTemplate jdbcTemplate = cacheDataSourceFactoryBean.createAndAddDataSource(property);
        DbQuery dbQuery = createDbQuery(jdbcTemplate, dbType);
        return dbQuery;
    }

    public static DbQuery createDbQuery(JdbcTemplate jdbcTemplate, DbType dbType) {
        DbDialect dbDialect = DialectFactory.getDialect(dbType);
        if(dbDialect == null){
            throw new RuntimeException("该数据库类型正在开发中");
        }
        AbstractDbQueryFactory dbQuery = null;
        if (dbType == DbType.CLICKHOUSE){
            dbQuery = new CacheDbQueryFactoryBean();
        }else if (dbType == DbType.DM8){
            dbQuery = new DmMetadataQueryFactoryBean();
        }else if (dbType == DbType.HIVE){
            dbQuery = new HiveMetadataQueryProvider();
        }else if (dbType == DbType.MYSQL || dbType == DbType.MYSQL8){
            dbQuery = new MysqlMetadataQueryProvider();
        }else if (dbType == DbType.ORACLE || dbType == DbType.ORACLE_12C){
            dbQuery = new SqlserverMetadataQueryProvider();
        }else if (dbType == DbType.POSTGRE_SQL){
            dbQuery = new PostgresMetadataQueryProvider();
        }else if (dbType == DbType.SQL_SERVER2008 || dbType == DbType.SQL_SERVER){
            dbQuery = new SqlserverMetadataQueryProvider();
        }else if (dbType == DbType.DORIS){
            dbQuery = new DorisMetadataQueryProvider();
        }else {
            dbQuery = new CacheDbQueryFactoryBean();
        }

        dbQuery.setJdbcTemplate(jdbcTemplate);
        dbQuery.setDbDialect(dbDialect);
        return dbQuery;
    }

}
