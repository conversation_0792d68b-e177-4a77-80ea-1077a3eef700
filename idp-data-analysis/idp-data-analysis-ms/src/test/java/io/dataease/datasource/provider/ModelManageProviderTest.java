package io.dataease.datasource.provider;

import io.dataease.extensions.datasource.dto.DatasourceRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * ModelManageProvider单元测试
 * 
 * @Author: AI Assistant
 */
@ExtendWith(MockitoExtension.class)
class ModelManageProviderTest {

    @Mock
    private ModelManageClient modelManageClient;

    @InjectMocks
    private ModelManageProvider modelManageProvider;

    private DatasourceRequest datasourceRequest;
    private List<SqlConsoleVo> mockSqlConsoleVos;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        datasourceRequest = new DatasourceRequest();
        datasourceRequest.setQuery("SELECT field1,field2 FROM model_table WHERE model_no = '12345'");

        // 模拟SqlConsoleVo返回数据
        SqlConsoleVo sqlConsoleVo = new SqlConsoleVo();
        sqlConsoleVo.setSuccess(true);
        sqlConsoleVo.setSql("SELECT field1,field2 FROM model_table");
        sqlConsoleVo.setTime(1000L);
        sqlConsoleVo.setCount(2);
        sqlConsoleVo.setColumnList(Arrays.asList("field1", "field2"));
        
        List<Map<String, Object>> dataList = new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("field1", "value1");
        row1.put("field2", "value2");
        dataList.add(row1);
        
        Map<String, Object> row2 = new HashMap<>();
        row2.put("field1", "value3");
        row2.put("field2", "value4");
        dataList.add(row2);
        
        sqlConsoleVo.setDataList(dataList);
        
        mockSqlConsoleVos = Arrays.asList(sqlConsoleVo);
    }

    @Test
    void testFetchResultField_Success() throws Exception {
        // 模拟ModelManageClient返回数据
        when(modelManageClient.selectModelData(any(), anyString()))
                .thenReturn(mockSqlConsoleVos);

        // 执行测试
        Map<String, Object> result = modelManageProvider.fetchResultField(datasourceRequest);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.containsKey("fields"));
        assertTrue(result.containsKey("data"));

        @SuppressWarnings("unchecked")
        List<Object> fields = (List<Object>) result.get("fields");
        @SuppressWarnings("unchecked")
        List<String[]> data = (List<String[]>) result.get("data");

        assertEquals(2, fields.size());
        assertEquals(2, data.size());
        assertEquals("value1", data.get(0)[0]);
        assertEquals("value2", data.get(0)[1]);
        assertEquals("value3", data.get(1)[0]);
        assertEquals("value4", data.get(1)[1]);
    }

    @Test
    void testFetchResultField_EmptyResult() throws Exception {
        // 模拟返回空结果
        when(modelManageClient.selectModelData(any(), anyString()))
                .thenReturn(new ArrayList<>());

        // 执行测试
        Map<String, Object> result = modelManageProvider.fetchResultField(datasourceRequest);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.containsKey("fields"));
        assertTrue(result.containsKey("data"));

        @SuppressWarnings("unchecked")
        List<Object> fields = (List<Object>) result.get("fields");
        @SuppressWarnings("unchecked")
        List<String[]> data = (List<String[]>) result.get("data");

        assertTrue(fields.isEmpty());
        assertTrue(data.isEmpty());
    }

    @Test
    void testFetchResultField_QueryFailed() throws Exception {
        // 模拟查询失败
        SqlConsoleVo failedResult = new SqlConsoleVo();
        failedResult.setSuccess(false);
        failedResult.setSql("SELECT * FROM model_table");
        
        when(modelManageClient.selectModelData(any(), anyString()))
                .thenReturn(Arrays.asList(failedResult));

        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            modelManageProvider.fetchResultField(datasourceRequest);
        });
    }

    @Test
    void testCheckStatus_Success() throws Exception {
        // 模拟服务正常
        AjaxResult mockResult = AjaxResult.success();
        when(modelManageClient.modelTableByModelNo()).thenReturn(mockResult);

        // 执行测试
        String status = modelManageProvider.checkStatus(datasourceRequest);

        // 验证结果
        assertEquals("Success", status);
    }

    @Test
    void testCheckStatus_Failed() throws Exception {
        // 模拟服务异常
        when(modelManageClient.modelTableByModelNo())
                .thenThrow(new RuntimeException("Service unavailable"));

        // 执行测试
        String status = modelManageProvider.checkStatus(datasourceRequest);

        // 验证结果
        assertTrue(status.startsWith("Error:"));
    }

    @Test
    void testGetSchema() {
        // 测试getSchema方法
        List<String> schemas = modelManageProvider.getSchema(datasourceRequest);
        assertNotNull(schemas);
        assertTrue(schemas.isEmpty());
    }

    @Test
    void testGetTables() {
        // 测试getTables方法
        var tables = modelManageProvider.getTables(datasourceRequest);
        assertNotNull(tables);
        assertTrue(tables.isEmpty());
    }

    @Test
    void testFetchTableField() {
        // 测试fetchTableField方法
        var fields = modelManageProvider.fetchTableField(datasourceRequest);
        assertNotNull(fields);
        assertTrue(fields.isEmpty());
    }
}
