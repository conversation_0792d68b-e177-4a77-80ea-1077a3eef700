<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="io.dataease.visualization.dao.ext.mapper.ExtVisualizationOuterParamsMapper">

    <resultMap id="BaseResultMapParams" type="io.dataease.visualization.dao.auto.entity.VisualizationOuterParams">
        <id column="params_id" jdbcType="VARCHAR" property="paramsId" />
        <result column="visualization_id" jdbcType="VARCHAR" property="visualizationId" />
        <result column="checked" jdbcType="BIT" property="checked" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="copy_from" jdbcType="VARCHAR" property="copyFrom" />
        <result column="copy_id" jdbcType="VARCHAR" property="copyId" />
    </resultMap>

    <resultMap id="BaseResultMapParamsInfo" type="io.dataease.visualization.dao.auto.entity.VisualizationOuterParamsInfo">
        <id column="params_info_id" jdbcType="VARCHAR" property="paramsInfoId" />
        <result column="params_id" jdbcType="VARCHAR" property="paramsId" />
        <result column="param_name" jdbcType="VARCHAR" property="paramName" />
        <result column="checked" jdbcType="BIT" property="checked" />
        <result column="required" jdbcType="BIT" property="required" />
        <result column="default_value" jdbcType="VARCHAR" property="defaultValue" />
        <result column="enabled_default" jdbcType="BIT" property="enabledDefault" />
        <result column="copy_from" jdbcType="VARCHAR" property="copyFrom" />
        <result column="copy_id" jdbcType="VARCHAR" property="copyId" />
    </resultMap>

  <resultMap id="BaseResultMapDTOSnapshot" type="io.dataease.api.visualization.dto.VisualizationOuterParamsDTO"
             extends="BaseResultMapParams">
    <collection property="outerParamsInfoArray" ofType="io.dataease.api.visualization.dto.VisualizationOuterParamsInfoDTO"
                column="{visualization_id=visualization_id}"
                select="getOuterParamsInfoSnapshot">
    </collection>
  </resultMap>

  <resultMap id="BaseDsResultMapDTO" type="io.dataease.api.dataset.vo.CoreDatasetGroupVO">
    <collection property="datasetFields" ofType="io.dataease.api.dataset.vo.CoreDatasetTableFieldVO"
                column="{dataset_group_id=id}"
                select="getDsFieldInfo">
    </collection>
    <collection property="datasetViews" ofType="io.dataease.api.chart.vo.ChartBaseVO"
                column="{dataset_group_id=id,visualizationId = visualizationId}"
                select="getViewInfo">
    </collection>
  </resultMap>

  <resultMap id="OuterParamsInfoMap" type="io.dataease.api.visualization.dto.VisualizationOuterParamsInfoDTO" extends="BaseResultMapParamsInfo">
    <collection property="targetViewInfoList" ofType="io.dataease.visualization.dao.auto.entity.VisualizationOuterParamsTargetViewInfo">
      <result column="target_view_id" jdbcType="VARCHAR" property="targetViewId"/>
      <result column="target_ds_id" jdbcType="VARCHAR" property="targetDsId"/>
      <result column="target_field_id" jdbcType="VARCHAR" property="targetFieldId"/>
    </collection>
  </resultMap>

    <resultMap id="AllOuterParamsMap" type="io.dataease.api.visualization.dto.VisualizationOuterParamsInfoDTO">
        <result column="sourceInfo" jdbcType="VARCHAR" property="sourceInfo"/>
        <result column="required" jdbcType="VARCHAR" property="required"/>
        <result column="default_value" jdbcType="VARCHAR" property="defaultValue"/>
        <result column="enabled_default" jdbcType="VARCHAR" property="enabledDefault"/>
        <collection property="targetInfoList" ofType="String">
            <result column="targetInfo" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

  <select id="getOuterParamsInfoSnapshot" resultMap="OuterParamsInfoMap">
        SELECT
            pop.visualization_id,
            popi.params_info_id,
            popi.param_name,
            popi.enabled_default,
            popi.required,
            popi.default_value,
            ifnull( popi.checked, 0 ) AS checked,
            poptvi.target_view_id,
            poptvi.target_ds_id,
            poptvi.target_field_id
        FROM
            snapshot_visualization_outer_params pop
            LEFT JOIN snapshot_visualization_outer_params_info popi ON pop.params_id = popi.params_id
            LEFT JOIN snapshot_visualization_outer_params_target_view_info poptvi ON popi.params_info_id = poptvi.params_info_id
        WHERE
            pop.visualization_id = #{visualization_id}
        ORDER BY
            ifnull( popi.checked, 0 ) DESC
    </select>

  <select id="queryWithVisualizationIdSnapshot"  resultMap="BaseResultMapDTOSnapshot">
      SELECT
          #{visualizationId} as visualization_id,
          ifnull( vop.checked, 0 ) AS checked
      FROM
          snapshot_data_visualization_info dvi
              LEFT JOIN snapshot_visualization_outer_params vop ON dvi.id = vop.visualization_id
      WHERE
          dvi.id = #{visualizationId}
  </select>

    <delete id="deleteOuterParamsTargetWithVisualizationId" >
        DELETE FROM
            visualization_outer_params_target_view_info poptvi
        WHERE
            poptvi.params_info_id IN (
               SELECT params_info_id FROM
                (
                    SELECT poptvi.params_info_id FROM
                        visualization_outer_params_target_view_info poptvi
                            INNER JOIN visualization_outer_params_info popi ON poptvi.params_info_id = popi.params_info_id
                            INNER JOIN visualization_outer_params pop ON popi.params_id = pop.params_id
                    WHERE pop.visualization_id = #{visualizationId}
                ) tmp
            )
    </delete>

    <delete id="deleteOuterParamsInfoWithVisualizationId" >
        DELETE FROM
            visualization_outer_params_info popi
        WHERE
            popi.params_id IN (
                SELECT params_id FROM
                    (
                        SELECT popi.params_id FROM
                            visualization_outer_params_info popi
                                INNER JOIN visualization_outer_params pop ON popi.params_id = pop.params_id
                        WHERE pop.visualization_id = #{visualizationId}
                    ) tmp
            )
    </delete>

    <delete id="deleteOuterParamsWithVisualizationId" >
        DELETE pop
        FROM
            visualization_outer_params pop
        WHERE
            pop.visualization_id = #{visualizationId}
	</delete>


    <delete id="deleteOuterParamsTargetWithVisualizationIdSnapshot" >
        DELETE FROM
            snapshot_visualization_outer_params_target_view_info poptvi
        WHERE
            poptvi.params_info_id IN (
               SELECT params_info_id FROM
                (
                    SELECT poptvi.params_info_id FROM
                        snapshot_visualization_outer_params_target_view_info poptvi
                            INNER JOIN snapshot_visualization_outer_params_info popi ON poptvi.params_info_id = popi.params_info_id
                            INNER JOIN snapshot_visualization_outer_params pop ON popi.params_id = pop.params_id
                    WHERE pop.visualization_id = #{visualizationId}
                ) tmp
            )
    </delete>

    <delete id="deleteOuterParamsInfoWithVisualizationIdSnapshot" >
        DELETE FROM
            snapshot_visualization_outer_params_info popi
        WHERE
            popi.params_id IN (
                SELECT params_id FROM
                    (
                        SELECT popi.params_id FROM
                            snapshot_visualization_outer_params_info popi
                                INNER JOIN snapshot_visualization_outer_params pop ON popi.params_id = pop.params_id
                        WHERE pop.visualization_id = #{visualizationId}
                    ) tmp
            )
    </delete>

    <delete id="deleteOuterParamsWithVisualizationIdSnapshot" >
        DELETE pop
        FROM
            snapshot_visualization_outer_params pop
        WHERE
            pop.visualization_id = #{visualizationId}
	</delete>

    <select id="getVisualizationOuterParamsInfo" resultMap="AllOuterParamsMap">
        SELECT DISTINCT
            popi.param_name AS sourceInfo,
            popi.required AS required,
            popi.default_value AS default_value,
            popi.enabled_default AS enabled_default,
            CONCAT( poptvi.target_view_id, '#', poptvi.target_field_id ) AS targetInfo
        FROM
            visualization_outer_params pop
            LEFT JOIN visualization_outer_params_info popi ON pop.params_id = popi.params_id
            LEFT JOIN visualization_outer_params_target_view_info poptvi ON popi.params_info_id = poptvi.params_info_id
        WHERE
            pop.visualization_id = #{visualizationId} and pop.checked=1
            and popi.checked=1
    </select>

        <select id="getVisualizationOuterParamsInfoBase" resultType="io.dataease.visualization.dao.auto.entity.SnapshotVisualizationOuterParamsInfo">
           SELECT
           	vopi.param_name,
           	vopi.params_info_id
           FROM
           	snapshot_visualization_outer_params_info vopi
           	INNER JOIN snapshot_visualization_outer_params vop ON vop.params_id = vopi.params_id
           WHERE
           	vop.visualization_id = #{visualizationId}
        </select>

      <select id="queryDsWithVisualizationId"  resultMap="BaseDsResultMapDTO">
          SELECT DISTINCT
          	cdg.*,#{visualizationId} as visualizationId
          FROM
          	core_dataset_group cdg
          	INNER JOIN snapshot_core_chart_view ccv ON cdg.id = ccv.table_id and ccv.type != 'VQuery'
          	INNER JOIN snapshot_data_visualization_info dvi ON ccv.scene_id = dvi.id
          WHERE
          	ccv.scene_id = #{visualizationId}
          	AND dvi.id = #{visualizationId}
          	AND LOCATE(ccv.id, dvi.component_data)
      </select>

       <select id="getDsFieldInfo"  resultType="io.dataease.api.dataset.vo.CoreDatasetTableFieldVO">
          select cdtf.*,cdtf.id as attachId from core_dataset_table_field cdtf where cdtf.dataset_group_id = #{dataset_group_id} order by cdtf.de_type, cdtf.origin_name
      </select>

      <select id="getViewInfo"  resultType="io.dataease.api.chart.vo.ChartBaseVO">
           SELECT DISTINCT
          	ccv.id as chartId,ccv.title as chartName,ccv.type as chartType
          FROM
          	snapshot_core_chart_view ccv
          	INNER JOIN snapshot_data_visualization_info dvi ON ccv.scene_id = dvi.id
          WHERE
          	ccv.table_id = #{dataset_group_id}
          	AND ccv.type != 'VQuery'
          	AND dvi.id = #{visualizationId}
          	AND LOCATE(ccv.id, dvi.component_data)
      </select>

</mapper>
