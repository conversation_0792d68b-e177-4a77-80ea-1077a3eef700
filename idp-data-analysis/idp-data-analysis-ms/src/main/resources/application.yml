server:
  max-http-request-header-size: 500KB
  tomcat:
    connection-timeout: 70000
spring:
  messages:
    encoding: UTF-8
    basename: i18n/core,i18n/permissions,i18n/xpack,i18n/sync
  main:
    allow-circular-references: true
  jackson:
    parser:
      allow-numeric-leading-zeros: true
management:
  health:
    redis:
      enabled: false
mybatis:
  configuration:
    map-underscore-to-camel-case: true

# springdoc-openapi项目配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    #operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
# knife4j的增强配置，不需要增强可以不配
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-swagger-models: false

dibase:
  login-exclude-url:

sa-token:
  token-name: Authorization
  timeout: 2592000
  maxLoginCount: -1
  active-timeout: 14400  # 4小时无操作失效
  is-concurrent: true
  is-share: false
  token-style: simple-uuid
  is-log: true
  token-prefix: Bearer
  is-read-cookie: false
  # 配置Sa-Token单独使用的rendis连接
  alone-redis :
    host: *************
    password: Dib123#@!
    port: 6379
    database: 0
    timeout: 10s
