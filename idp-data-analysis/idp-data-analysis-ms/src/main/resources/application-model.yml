# 模型管理服务配置
model-manage:
  service:
    # 模型管理服务地址，可以根据实际部署情况修改
    url: http://localhost:8091
    # 连接超时时间（毫秒）
    connect-timeout: 5000
    # 读取超时时间（毫秒）
    read-timeout: 10000

# Feign配置
feign:
  client:
    config:
      idp-model-manage:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic
  compression:
    request:
      enabled: true
    response:
      enabled: true

# 日志配置
logging:
  level:
    io.dataease.datasource.provider: DEBUG
    io.dataease.chart.manage: DEBUG
