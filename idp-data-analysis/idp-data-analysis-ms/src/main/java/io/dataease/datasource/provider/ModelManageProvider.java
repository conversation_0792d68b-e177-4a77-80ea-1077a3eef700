package io.dataease.datasource.provider;

import io.dataease.exception.DEException;
import io.dataease.extensions.datasource.dto.DatasourceRequest;
import io.dataease.extensions.datasource.dto.DatasourceDTO;
import io.dataease.extensions.datasource.dto.DatasetTableDTO;
import io.dataease.extensions.datasource.dto.TableField;
import io.dataease.extensions.datasource.model.ConnectionObj;
import io.dataease.extensions.datasource.provider.Provider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 模型管理Provider - 用于调用idp-model-manage服务获取模型数据
 * 
 * @Author: AI Assistant
 */
@Service("modelManageProvider")
public class ModelManageProvider extends Provider {
    
    private static final Logger logger = LoggerFactory.getLogger(ModelManageProvider.class);
    
    @Autowired
    private ModelManageClient modelManageClient;
    
    @Override
    public List<String> getSchema(DatasourceRequest datasourceRequest) {
        // 模型管理不需要schema概念，返回空列表
        return new ArrayList<>();
    }
    
    @Override
    public List<DatasetTableDTO> getTables(DatasourceRequest datasourceRequest) {
        // 模型管理不需要表概念，返回空列表
        return new ArrayList<>();
    }
    
    @Override
    public ConnectionObj getConnection(DatasourceDTO coreDatasource) throws Exception {
        // 模型管理使用HTTP接口，不需要数据库连接
        return new ConnectionObj();
    }
    
    @Override
    public String checkStatus(DatasourceRequest datasourceRequest) throws Exception {
        try {
            // 通过调用模型管理服务检查状态
            modelManageClient.modelTableByModelNo();
            return "Success";
        } catch (Exception e) {
            logger.error("检查模型管理服务状态失败", e);
            return "Error: " + e.getMessage();
        }
    }
    
    @Override
    public List<TableField> fetchTableField(DatasourceRequest datasourceRequest) throws DEException {
        // 模型管理的字段信息通过其他接口获取
        return new ArrayList<>();
    }
    
    @Override
    public void hidePW(DatasourceDTO datasourceDTO) {
        // 模型管理不涉及密码，无需处理
    }
    
    /**
     * 核心方法：获取模型数据
     * 根据模型库、模型表以及字段去查询模型表中的数据
     */
    @Override
    public Map<String, Object> fetchResultField(DatasourceRequest datasourceRequest) throws DEException {
        Map<String, Object> result = new HashMap<>();
        List<TableField> fieldList = new ArrayList<>();
        List<String[]> dataList = new ArrayList<>();
        
        try {
            // 从请求中解析参数
            ModelQueryParams params = parseModelQueryParams(datasourceRequest);
            
            logger.info("调用模型管理服务查询数据，模型编号: {}, 字段列表: {}", 
                       params.getModelNo(), params.getIndicatorAttrs());
            
            // 调用模型管理服务的selectModelData接口
            List<SqlConsoleVo> sqlConsoleVos = modelManageClient.selectModelData(
                params.getIndicatorAttrs(), 
                params.getModelNo()
            );
            
            if (sqlConsoleVos != null && !sqlConsoleVos.isEmpty()) {
                SqlConsoleVo sqlConsoleVo = sqlConsoleVos.get(0);
                
                if (sqlConsoleVo.getSuccess()) {
                    // 构建字段信息
                    if (sqlConsoleVo.getColumnList() != null) {
                        for (String columnName : sqlConsoleVo.getColumnList()) {
                            TableField field = new TableField();
                            field.setName(columnName);
                            field.setOriginName(columnName);
                            field.setType("VARCHAR"); // 默认类型，可根据实际情况调整
                            field.setFieldType("VARCHAR");
                            fieldList.add(field);
                        }
                    }
                    
                    // 构建数据列表
                    if (sqlConsoleVo.getDataList() != null) {
                        for (Map<String, Object> dataMap : sqlConsoleVo.getDataList()) {
                            String[] row = new String[fieldList.size()];
                            for (int i = 0; i < fieldList.size(); i++) {
                                String columnName = fieldList.get(i).getName();
                                Object value = dataMap.get(columnName);
                                row[i] = value != null ? value.toString() : "";
                            }
                            dataList.add(row);
                        }
                    }
                    
                    logger.info("成功获取模型数据，字段数: {}, 数据行数: {}", 
                               fieldList.size(), dataList.size());
                } else {
                    logger.error("模型数据查询失败: {}", sqlConsoleVo.getSql());
                    throw new DEException("模型数据查询失败");
                }
            } else {
                logger.warn("模型数据查询返回空结果");
            }
            
        } catch (Exception e) {
            logger.error("调用模型管理服务失败", e);
            throw new DEException("调用模型管理服务失败: " + e.getMessage());
        }
        
        result.put("fields", fieldList);
        result.put("data", dataList);
        return result;
    }
    
    /**
     * 解析模型查询参数
     */
    private ModelQueryParams parseModelQueryParams(DatasourceRequest datasourceRequest) {
        ModelQueryParams params = new ModelQueryParams();
        
        // 从查询SQL中解析模型编号和字段信息
        String query = datasourceRequest.getQuery();
        if (query != null) {
            // 这里需要根据实际的SQL格式来解析
            // 假设SQL格式为: SELECT field1,field2 FROM model_table WHERE model_no = 'xxx'
            // 或者通过其他方式传递参数
            
            // 简单的解析逻辑，实际可能需要更复杂的SQL解析
            if (query.contains("model_no")) {
                // 提取模型编号
                String modelNo = extractModelNo(query);
                params.setModelNo(modelNo);
            }
            
            // 提取字段列表
            List<String> fields = extractFields(query);
            params.setIndicatorAttrs(fields);
        }
        
        // 如果无法从SQL解析，可以从其他地方获取参数
        // 比如从datasourceRequest的其他属性中获取
        
        return params;
    }
    
    /**
     * 从SQL中提取模型编号
     */
    private String extractModelNo(String sql) {
        // 简单的正则表达式提取，实际可能需要更复杂的逻辑
        // 这里假设SQL中包含 model_no = 'value' 的格式
        try {
            String pattern = "model_no\\s*=\\s*['\"]([^'\"]+)['\"]";
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern, java.util.regex.Pattern.CASE_INSENSITIVE);
            java.util.regex.Matcher m = p.matcher(sql);
            if (m.find()) {
                return m.group(1);
            }
        } catch (Exception e) {
            logger.warn("无法从SQL中提取模型编号: {}", sql);
        }
        return null;
    }
    
    /**
     * 从SQL中提取字段列表
     */
    private List<String> extractFields(String sql) {
        List<String> fields = new ArrayList<>();
        try {
            // 简单的SELECT字段提取
            String upperSql = sql.toUpperCase();
            int selectIndex = upperSql.indexOf("SELECT");
            int fromIndex = upperSql.indexOf("FROM");
            
            if (selectIndex >= 0 && fromIndex > selectIndex) {
                String fieldsPart = sql.substring(selectIndex + 6, fromIndex).trim();
                if (!"*".equals(fieldsPart.trim())) {
                    String[] fieldArray = fieldsPart.split(",");
                    for (String field : fieldArray) {
                        fields.add(field.trim());
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("无法从SQL中提取字段列表: {}", sql);
        }
        return fields;
    }
    
    /**
     * 模型查询参数内部类
     */
    private static class ModelQueryParams {
        private String modelNo;
        private List<String> indicatorAttrs;
        
        public String getModelNo() {
            return modelNo;
        }
        
        public void setModelNo(String modelNo) {
            this.modelNo = modelNo;
        }
        
        public List<String> getIndicatorAttrs() {
            return indicatorAttrs;
        }
        
        public void setIndicatorAttrs(List<String> indicatorAttrs) {
            this.indicatorAttrs = indicatorAttrs;
        }
    }
}
