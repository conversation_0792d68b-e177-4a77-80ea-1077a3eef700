package io.dataease.datasource.provider;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * SQL控制台查询结果VO
 * 
 * @Author: AI Assistant
 */
@Data
public class SqlConsoleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 执行的SQL语句
     */
    private String sql;
    
    /**
     * 执行时间（毫秒）
     */
    private Long time;
    
    /**
     * 执行是否成功
     */
    private Boolean success;
    
    /**
     * 查询结果行数
     */
    private Integer count;
    
    /**
     * 列名列表
     */
    private List<String> columnList;
    
    /**
     * 数据列表，每个Map代表一行数据，key为列名，value为列值
     */
    private List<Map<String, Object>> dataList;
    
    /**
     * 列名与表名的映射关系
     */
    private Map<String, String> columnTableMap;
}
