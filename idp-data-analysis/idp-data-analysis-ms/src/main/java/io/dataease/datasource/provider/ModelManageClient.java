package io.dataease.datasource.provider;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 模型管理服务Feign客户端
 * 用于调用idp-model-manage服务的接口
 * 
 * @Author: AI Assistant
 */
@FeignClient(name = "idp-model-manage", url = "${model-manage.service.url:http://localhost:8091}")
public interface ModelManageClient {
    
    /**
     * 查询模型实例化表后的数据
     * 
     * @param indictorAttrs 指标属性列表（字段列表）
     * @param modelNo 模型编号
     * @return 查询结果
     */
    @PostMapping("/model-base/selectModelData")
    List<SqlConsoleVo> selectModelData(@RequestParam List<String> indictorAttrs, 
                                      @RequestParam String modelNo);
    
    /**
     * 查询指定模型下的指标和维度列表信息
     * 
     * @param modelNo 模型编号
     * @return 指标和维度信息
     */
    @PostMapping("/model-base/selectIndictorDimByModelId/{modelNo}")
    IndictorDimVo selectIndictorDimByModelId(@RequestParam String modelNo);
    
    /**
     * 查询模型管理模块中所有的表包含（数据源id,表id,字段列表）
     * 
     * @return 模型表信息
     */
    @PostMapping("/model-base/modelTableByModelNo")
    AjaxResult modelTableByModelNo();
}
