# ModelManageProvider 使用说明

## 概述

ModelManageProvider 是一个自定义的数据源Provider，用于在DataEase图表渲染中调用idp-model-manage服务的接口，根据模型库、模型表以及字段去查询模型表中的数据。

## 功能特性

- 继承DataEase的Provider抽象类，完全兼容现有的图表渲染框架
- 通过Feign客户端调用idp-model-manage服务的REST API
- 支持根据模型编号和字段列表查询模型数据
- 自动将模型数据转换为DataEase图表组件需要的格式

## 核心组件

### 1. ModelManageProvider
- **位置**: `io.dataease.datasource.provider.ModelManageProvider`
- **功能**: 核心Provider实现，负责调用模型管理服务获取数据
- **关键方法**: `fetchResultField()` - 获取模型数据的核心方法

### 2. ModelManageClient
- **位置**: `io.dataease.datasource.provider.ModelManageClient`
- **功能**: Feign客户端，定义了调用idp-model-manage服务的接口
- **主要接口**:
  - `selectModelData()` - 查询模型实例化表数据
  - `selectIndictorDimByModelId()` - 查询指标和维度信息
  - `modelTableByModelNo()` - 查询模型表信息

### 3. 数据传输对象 (VO)
- `SqlConsoleVo` - SQL查询结果封装
- `IndictorDimVo` - 指标和维度信息封装
- `AjaxResult` - 通用响应结果封装

### 4. 配置类
- `ModelManageConfig` - 启用Feign客户端的配置
- `application-model.yml` - 模型管理服务相关配置

## 使用方法

### 1. 配置模型管理服务地址

在 `application.yml` 或 `application-model.yml` 中配置：

```yaml
model-manage:
  service:
    url: http://localhost:8091  # 根据实际部署地址修改
```

### 2. 在ChartDataManage中集成

有两种集成方式：

#### 方式一：使用ModelChartDataManage（推荐）

```java
@Autowired
private ModelChartDataManage modelChartDataManage;

public ChartViewDTO calcData(ChartViewDTO view) throws Exception {
    // 检查是否为模型数据源
    if (modelChartDataManage.isModelDataSource(view.getTableId())) {
        // 提取模型编号和字段列表
        String modelNo = modelChartDataManage.extractModelNoFromTableId(view.getTableId());
        List<String> indicatorAttrs = extractIndicatorAttrs(view);
        
        // 使用模型管理Provider获取数据
        return modelChartDataManage.calcDataFromModel(view, modelNo, indicatorAttrs);
    } else {
        // 使用原有逻辑
        return originalCalcData(view);
    }
}
```

#### 方式二：直接在calcData方法中集成

在原有的 `ChartDataManage.calcData()` 方法中，修改Provider获取逻辑：

```java
// 原有代码
Provider provider;
if (crossDs) {
    provider = ProviderFactory.getDefaultProvider();
} else {
    provider = ProviderFactory.getProvider(dsMap.entrySet().iterator().next().getValue().getType());
}

// 修改为
Provider provider;
if (crossDs) {
    provider = ProviderFactory.getDefaultProvider();
} else {
    String dsType = dsMap.entrySet().iterator().next().getValue().getType();
    // 检查是否为模型数据源
    if (isModelDataSource(view.getTableId())) {
        provider = ProviderFactory.getProvider("model-manage");
        // 重新构建请求参数
        datasourceRequest = buildModelDatasourceRequest(view);
    } else {
        provider = ProviderFactory.getProvider(dsType);
    }
}
```

### 3. 模型数据源标识

目前支持两种方式标识模型数据源：

1. **表ID前缀**: 表ID以 "MODEL_" 开头，如 "MODEL_12345"
2. **自定义逻辑**: 可以在 `isModelDataSource()` 方法中实现自定义的判断逻辑

### 4. 字段映射

Provider会自动从图表配置中提取需要查询的字段：
- X轴字段 (`view.getXAxis()`)
- Y轴字段 (`view.getYAxis()`)
- 扩展轴字段 (`view.getXAxisExt()`)

## API接口说明

### selectModelData接口

**请求参数**:
- `indictorAttrs`: List<String> - 指标属性列表（字段名列表）
- `modelNo`: String - 模型编号

**返回结果**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "sql": "SELECT field1,field2 FROM model_table",
      "time": **********,
      "success": true,
      "count": 100,
      "columnList": ["field1", "field2"],
      "dataList": [
        {"field1": "value1", "field2": "value2"},
        {"field1": "value3", "field2": "value4"}
      ]
    }
  ]
}
```

## 配置说明

### Feign配置

```yaml
feign:
  client:
    config:
      idp-model-manage:
        connectTimeout: 5000
        readTimeout: 10000
        loggerLevel: basic
```

### 日志配置

```yaml
logging:
  level:
    io.dataease.datasource.provider: DEBUG
    io.dataease.chart.manage: DEBUG
```

## 注意事项

1. **服务依赖**: 确保idp-model-manage服务正常运行且可访问
2. **网络配置**: 确保网络连接正常，防火墙允许访问
3. **超时设置**: 根据模型数据量大小调整超时时间
4. **错误处理**: Provider会自动处理网络异常和数据格式异常
5. **性能考虑**: 大数据量查询时建议在模型管理服务端进行分页处理

## 扩展开发

如需扩展功能，可以：

1. **添加新的API接口**: 在 `ModelManageClient` 中添加新的Feign接口
2. **自定义数据处理**: 重写 `processModelData()` 方法
3. **添加缓存**: 在Provider中添加数据缓存逻辑
4. **支持更多图表类型**: 在数据转换逻辑中添加对特定图表类型的支持

## 故障排查

1. **连接失败**: 检查服务地址配置和网络连通性
2. **数据格式错误**: 检查模型管理服务返回的数据格式
3. **字段映射问题**: 检查图表配置中的字段名是否与模型字段匹配
4. **性能问题**: 检查查询的数据量和网络延迟
