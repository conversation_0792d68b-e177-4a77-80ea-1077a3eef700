package io.dataease.datasource.provider;

import lombok.Data;

import java.io.Serializable;

/**
 * Ajax请求结果封装类
 * 
 * @Author: AI Assistant
 */
@Data
public class AjaxResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private int code;

    /**
     * 返回消息
     */
    private String msg;

    /**
     * 返回数据
     */
    private Object data;

    /**
     * 成功状态码
     */
    public static final int SUCCESS = 200;

    /**
     * 失败状态码
     */
    public static final int ERROR = 500;

    /**
     * 构造方法
     */
    public AjaxResult() {
    }

    public AjaxResult(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public AjaxResult(int code, String msg, Object data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /**
     * 返回成功消息
     */
    public static AjaxResult success() {
        return new AjaxResult(SUCCESS, "操作成功");
    }

    /**
     * 返回成功数据
     */
    public static AjaxResult success(Object data) {
        return new AjaxResult(SUCCESS, "操作成功", data);
    }

    /**
     * 返回成功消息
     */
    public static AjaxResult success(String msg) {
        return new AjaxResult(SUCCESS, msg);
    }

    /**
     * 返回成功消息
     */
    public static AjaxResult success(String msg, Object data) {
        return new AjaxResult(SUCCESS, msg, data);
    }

    /**
     * 返回错误消息
     */
    public static AjaxResult error() {
        return new AjaxResult(ERROR, "操作失败");
    }

    /**
     * 返回错误消息
     */
    public static AjaxResult error(String msg) {
        return new AjaxResult(ERROR, msg);
    }

    /**
     * 返回错误消息
     */
    public static AjaxResult error(int code, String msg) {
        return new AjaxResult(code, msg);
    }

    /**
     * 是否为成功
     */
    public boolean isSuccess() {
        return SUCCESS == code;
    }
}
