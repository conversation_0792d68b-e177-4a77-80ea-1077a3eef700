package io.dataease.datasource.provider;

import lombok.Data;

import java.util.List;

/**
 * 指标和维度信息VO
 * 
 * @Author: AI Assistant
 */
@Data
public class IndictorDimVo {

    /**
     * 维度资产列表
     */
    private List<DimAssetVo> dimAssetVoList;

    /**
     * 指标资产列表
     */
    private List<IndictorAssetVo> indictorAssetVoList;
    
    /**
     * 维度资产VO
     */
    @Data
    public static class DimAssetVo {
        private String id;
        private String name;
        private String type;
        private String description;
    }
    
    /**
     * 指标资产VO
     */
    @Data
    public static class IndictorAssetVo {
        private String id;
        private String name;
        private String type;
        private String description;
        private String aggregateFunction;
    }
}
