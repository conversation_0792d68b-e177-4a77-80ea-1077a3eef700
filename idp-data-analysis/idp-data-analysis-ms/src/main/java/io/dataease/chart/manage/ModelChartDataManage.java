package io.dataease.chart.manage;

import io.dataease.extensions.datasource.dto.DatasourceRequest;
import io.dataease.extensions.datasource.dto.DatasourceSchemaDTO;
import io.dataease.extensions.datasource.factory.ProviderFactory;
import io.dataease.extensions.datasource.provider.Provider;
import io.dataease.extensions.view.dto.ChartViewDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 模型图表数据管理类
 * 扩展ChartDataManage，支持调用模型管理服务获取数据
 * 
 * @Author: AI Assistant
 */
@Component
public class ModelChartDataManage {
    
    private static final Logger logger = LoggerFactory.getLogger(ModelChartDataManage.class);
    
    /**
     * 使用模型管理Provider计算图表数据
     * 
     * @param view 图表视图配置
     * @param modelNo 模型编号
     * @param indicatorAttrs 指标属性列表（字段列表）
     * @return 图表数据
     */
    public ChartViewDTO calcDataFromModel(ChartViewDTO view, String modelNo, List<String> indicatorAttrs) throws Exception {
        logger.info("开始使用模型管理服务计算图表数据，模型编号: {}, 字段: {}", modelNo, indicatorAttrs);
        
        try {
            // 获取模型管理Provider
            Provider provider = ProviderFactory.getProvider("model-manage");
            
            // 构建数据源请求
            DatasourceRequest datasourceRequest = buildModelDatasourceRequest(modelNo, indicatorAttrs);
            
            // 调用Provider获取数据
            Map<String, Object> result = provider.fetchResultField(datasourceRequest);
            
            // 处理返回的数据，转换为ChartViewDTO需要的格式
            ChartViewDTO resultView = processModelData(view, result);
            
            logger.info("成功获取模型数据并转换为图表格式");
            return resultView;
            
        } catch (Exception e) {
            logger.error("使用模型管理服务计算图表数据失败", e);
            throw e;
        }
    }
    
    /**
     * 构建模型数据源请求
     * 
     * @param modelNo 模型编号
     * @param indicatorAttrs 指标属性列表
     * @return 数据源请求对象
     */
    private DatasourceRequest buildModelDatasourceRequest(String modelNo, List<String> indicatorAttrs) {
        DatasourceRequest request = new DatasourceRequest();
        
        // 构建查询SQL，包含模型编号和字段信息
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT ");
        
        if (indicatorAttrs != null && !indicatorAttrs.isEmpty()) {
            sqlBuilder.append(String.join(",", indicatorAttrs));
        } else {
            sqlBuilder.append("*");
        }
        
        sqlBuilder.append(" FROM model_table WHERE model_no = '").append(modelNo).append("'");
        
        request.setQuery(sqlBuilder.toString());
        
        // 设置虚拟数据源信息（模型管理不需要真实的数据源连接）
        Map<Long, DatasourceSchemaDTO> dsMap = new HashMap<>();
        DatasourceSchemaDTO modelDs = new DatasourceSchemaDTO();
        modelDs.setType("model-manage");
        modelDs.setName("模型管理数据源");
        dsMap.put(1L, modelDs);
        request.setDsList(dsMap);
        
        return request;
    }
    
    /**
     * 处理模型数据，转换为图表需要的格式
     * 
     * @param originalView 原始图表视图
     * @param modelData 模型数据
     * @return 处理后的图表视图
     */
    private ChartViewDTO processModelData(ChartViewDTO originalView, Map<String, Object> modelData) {
        ChartViewDTO resultView = new ChartViewDTO();
        
        // 复制原始视图的基本信息
        resultView.setId(originalView.getId());
        resultView.setTitle(originalView.getTitle());
        resultView.setType(originalView.getType());
        resultView.setRender(originalView.getRender());
        
        // 处理数据字段信息
        @SuppressWarnings("unchecked")
        List<Object> fields = (List<Object>) modelData.get("fields");
        
        // 处理数据内容
        @SuppressWarnings("unchecked")
        List<String[]> data = (List<String[]>) modelData.get("data");
        
        // 这里可以根据需要进一步处理数据格式
        // 比如转换为图表组件需要的特定格式
        
        logger.info("模型数据处理完成，字段数: {}, 数据行数: {}", 
                   fields != null ? fields.size() : 0, 
                   data != null ? data.size() : 0);
        
        return resultView;
    }
    
    /**
     * 检查是否为模型数据源
     * 
     * @param tableId 表ID
     * @return 是否为模型数据源
     */
    public boolean isModelDataSource(Long tableId) {
        // 这里可以根据实际业务逻辑判断
        // 比如通过表ID的特定前缀或者查询数据库来判断
        return tableId != null && tableId.toString().startsWith("MODEL_");
    }
    
    /**
     * 从表ID中提取模型编号
     * 
     * @param tableId 表ID
     * @return 模型编号
     */
    public String extractModelNoFromTableId(Long tableId) {
        if (tableId == null) {
            return null;
        }
        
        String tableIdStr = tableId.toString();
        if (tableIdStr.startsWith("MODEL_")) {
            return tableIdStr.substring(6); // 去掉"MODEL_"前缀
        }
        
        return tableIdStr;
    }
}
