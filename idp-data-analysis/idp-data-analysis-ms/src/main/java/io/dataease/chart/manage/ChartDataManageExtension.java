package io.dataease.chart.manage;

import io.dataease.extensions.datasource.dto.DatasourceRequest;
import io.dataease.extensions.datasource.dto.DatasourceSchemaDTO;
import io.dataease.extensions.datasource.factory.ProviderFactory;
import io.dataease.extensions.datasource.provider.Provider;
import io.dataease.extensions.view.dto.ChartViewDTO;
import io.dataease.extensions.view.dto.ChartViewFieldDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ChartDataManage扩展类
 * 演示如何集成ModelManageProvider到现有的calcData方法中
 * 
 * @Author: AI Assistant
 */
@Component
public class ChartDataManageExtension {
    
    private static final Logger logger = LoggerFactory.getLogger(ChartDataManageExtension.class);
    
    @Autowired
    private ModelChartDataManage modelChartDataManage;
    
    /**
     * 扩展的calcData方法，支持模型管理数据源
     * 这个方法展示了如何在原有的calcData逻辑中集成模型管理Provider
     * 
     * @param view 图表视图配置
     * @return 计算后的图表数据
     */
    public ChartViewDTO calcDataWithModelSupport(ChartViewDTO view) throws Exception {
        logger.info("开始计算图表数据，表ID: {}", view.getTableId());
        
        // 检查是否为模型数据源
        if (modelChartDataManage.isModelDataSource(view.getTableId())) {
            logger.info("检测到模型数据源，使用模型管理Provider");
            return calcDataFromModelManage(view);
        } else {
            logger.info("使用原有的数据源Provider");
            // 这里可以调用原有的ChartDataManage.calcData方法
            // return originalChartDataManage.calcData(view);
            throw new UnsupportedOperationException("请集成到原有的ChartDataManage.calcData方法中");
        }
    }
    
    /**
     * 使用模型管理Provider计算图表数据
     * 
     * @param view 图表视图配置
     * @return 计算后的图表数据
     */
    private ChartViewDTO calcDataFromModelManage(ChartViewDTO view) throws Exception {
        // 提取模型编号
        String modelNo = modelChartDataManage.extractModelNoFromTableId(view.getTableId());
        if (modelNo == null) {
            throw new IllegalArgumentException("无法从表ID中提取模型编号: " + view.getTableId());
        }
        
        // 提取需要查询的字段
        List<String> indicatorAttrs = extractIndicatorAttrs(view);
        
        // 获取模型管理Provider
        Provider provider = ProviderFactory.getProvider("model-manage");
        
        // 构建数据源请求
        DatasourceRequest datasourceRequest = buildModelDatasourceRequest(modelNo, indicatorAttrs);
        
        // 调用Provider获取数据
        Map<String, Object> result = provider.fetchResultField(datasourceRequest);
        
        // 处理返回的数据
        return processModelDataToChart(view, result);
    }
    
    /**
     * 从图表视图中提取指标属性（字段列表）
     * 
     * @param view 图表视图
     * @return 指标属性列表
     */
    private List<String> extractIndicatorAttrs(ChartViewDTO view) {
        List<String> attrs = new ArrayList<>();
        
        // 从X轴字段中提取
        if (view.getXAxis() != null) {
            attrs.addAll(view.getXAxis().stream()
                    .map(ChartViewFieldDTO::getDataeaseName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }
        
        // 从Y轴字段中提取
        if (view.getYAxis() != null) {
            attrs.addAll(view.getYAxis().stream()
                    .map(ChartViewFieldDTO::getDataeaseName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }
        
        // 从扩展轴字段中提取
        if (view.getXAxisExt() != null) {
            attrs.addAll(view.getXAxisExt().stream()
                    .map(ChartViewFieldDTO::getDataeaseName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }
        
        // 去重
        return attrs.stream().distinct().collect(Collectors.toList());
    }
    
    /**
     * 构建模型数据源请求
     * 
     * @param modelNo 模型编号
     * @param indicatorAttrs 指标属性列表
     * @return 数据源请求对象
     */
    private DatasourceRequest buildModelDatasourceRequest(String modelNo, List<String> indicatorAttrs) {
        DatasourceRequest request = new DatasourceRequest();
        
        // 构建查询SQL，包含模型编号和字段信息
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("SELECT ");
        
        if (indicatorAttrs != null && !indicatorAttrs.isEmpty()) {
            sqlBuilder.append(String.join(",", indicatorAttrs));
        } else {
            sqlBuilder.append("*");
        }
        
        sqlBuilder.append(" FROM model_table WHERE model_no = '").append(modelNo).append("'");
        
        request.setQuery(sqlBuilder.toString());
        
        // 设置虚拟数据源信息
        Map<Long, DatasourceSchemaDTO> dsMap = new HashMap<>();
        DatasourceSchemaDTO modelDs = new DatasourceSchemaDTO();
        modelDs.setType("model-manage");
        modelDs.setName("模型管理数据源");
        dsMap.put(1L, modelDs);
        request.setDsList(dsMap);
        
        return request;
    }
    
    /**
     * 处理模型数据，转换为图表格式
     * 
     * @param originalView 原始图表视图
     * @param modelData 模型数据
     * @return 处理后的图表视图
     */
    private ChartViewDTO processModelDataToChart(ChartViewDTO originalView, Map<String, Object> modelData) {
        ChartViewDTO resultView = new ChartViewDTO();
        
        // 复制原始视图的配置
        resultView.setId(originalView.getId());
        resultView.setTitle(originalView.getTitle());
        resultView.setType(originalView.getType());
        resultView.setRender(originalView.getRender());
        resultView.setXAxis(originalView.getXAxis());
        resultView.setYAxis(originalView.getYAxis());
        resultView.setXAxisExt(originalView.getXAxisExt());
        resultView.setYAxisExt(originalView.getYAxisExt());
        resultView.setCustomAttr(originalView.getCustomAttr());
        resultView.setCustomStyle(originalView.getCustomStyle());
        resultView.setCustomFilter(originalView.getCustomFilter());
        
        // 处理数据
        @SuppressWarnings("unchecked")
        List<String[]> data = (List<String[]>) modelData.get("data");
        
        if (data != null && !data.isEmpty()) {
            // 这里可以根据图表类型进行特定的数据处理
            // 比如对于柱状图、折线图等不同类型的数据格式转换
            
            logger.info("成功处理模型数据，数据行数: {}", data.size());
            
            // 将数据设置到结果视图中
            // 注意：这里需要根据实际的ChartViewDTO结构来设置数据
            // resultView.setData(processedData);
        }
        
        return resultView;
    }
    
    /**
     * 集成到原有ChartDataManage的示例方法
     * 展示如何在原有的calcData方法中添加模型管理支持
     */
    public void integrateToOriginalCalcData() {
        /*
         * 在原有的ChartDataManage.calcData方法中，可以在获取Provider的地方添加如下逻辑：
         * 
         * // 原有代码：
         * Provider provider;
         * if (crossDs) {
         *     provider = ProviderFactory.getDefaultProvider();
         * } else {
         *     provider = ProviderFactory.getProvider(dsMap.entrySet().iterator().next().getValue().getType());
         * }
         * 
         * // 修改为：
         * Provider provider;
         * if (crossDs) {
         *     provider = ProviderFactory.getDefaultProvider();
         * } else {
         *     String dsType = dsMap.entrySet().iterator().next().getValue().getType();
         *     // 检查是否为模型数据源
         *     if (modelChartDataManage.isModelDataSource(view.getTableId())) {
         *         provider = ProviderFactory.getProvider("model-manage");
         *         // 重新构建datasourceRequest以包含模型查询参数
         *         String modelNo = modelChartDataManage.extractModelNoFromTableId(view.getTableId());
         *         List<String> indicatorAttrs = extractIndicatorAttrs(view);
         *         datasourceRequest = buildModelDatasourceRequest(modelNo, indicatorAttrs);
         *     } else {
         *         provider = ProviderFactory.getProvider(dsType);
         *     }
         * }
         */
    }
}
