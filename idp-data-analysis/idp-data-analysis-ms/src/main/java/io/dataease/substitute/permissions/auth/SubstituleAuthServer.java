package io.dataease.substitute.permissions.auth;

/*import io.dataease.api.permissions.auth.api.AuthApi;
import io.dataease.api.permissions.dto.AuthDTO;
import io.dataease.api.permissions.request.AuthRequest;
import org.springframework.stereotype.Service;

*//**
 * 替补服务-权限替补
 * 桌面版调用
 * 替补-权限服务不在线情况上
 *//*
@Service
public class SubstituleAuthServer implements AuthApi {
    @Override
    public AuthDTO query(AuthRequest request) {
        AuthDTO authDTO = new AuthDTO();
        authDTO.setModel(0);
        return authDTO;
    }

    @Override
    public AuthDTO queryByUserId(Long userId) {
        AuthDTO authDTO = new AuthDTO();
        authDTO.setModel(0);
        return authDTO;
    }
}*/
