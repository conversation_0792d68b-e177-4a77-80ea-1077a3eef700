package io.dataease.visualization.dao.auto.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

/**
 * <p>
 * 可视化大屏信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@TableName("snapshot_data_visualization_info")
public class SnapshotDataVisualizationInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 父id
     */
    private Long pid;

    /**
     * 所属组织id
     */
    private Long orgId;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 节点类型  folder or panel 目录或者文件夹
     */
    private String nodeType;

    /**
     * 类型
     */
    private String type;

    /**
     * 样式数据
     */
    private String canvasStyleData;

    /**
     * 组件数据
     */
    private String componentData;

    /**
     * 移动端布局0-关闭 1-开启
     */
    private Boolean mobileLayout;

    /**
     * 状态 0-未发布 1-已发布
     */
    private Integer status;

    /**
     * 是否单独打开水印 0-关闭 1-开启
     */
    private Integer selfWatermarkStatus;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 备注
     */
    private String remark;

    /**
     * 数据来源
     */
    private String source;

    /**
     * 删除标志
     */
    private Boolean deleteFlag;

    /**
     * 删除时间
     */
    private Long deleteTime;

    /**
     * 删除人
     */
    private String deleteBy;

    /**
     * 可视化资源版本
     */
    private Integer version;

    /**
     * 内容标识
     */
    private String contentId;

    /**
     * 内容检查标识
     */
    private String checkVersion;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getPid() {
        return pid;
    }

    public void setPid(Long pid) {
        this.pid = pid;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCanvasStyleData() {
        return canvasStyleData;
    }

    public void setCanvasStyleData(String canvasStyleData) {
        this.canvasStyleData = canvasStyleData;
    }

    public String getComponentData() {
        return componentData;
    }

    public void setComponentData(String componentData) {
        this.componentData = componentData;
    }

    public Boolean getMobileLayout() {
        return mobileLayout;
    }

    public void setMobileLayout(Boolean mobileLayout) {
        this.mobileLayout = mobileLayout;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSelfWatermarkStatus() {
        return selfWatermarkStatus;
    }

    public void setSelfWatermarkStatus(Integer selfWatermarkStatus) {
        this.selfWatermarkStatus = selfWatermarkStatus;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Boolean getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Boolean deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Long deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getDeleteBy() {
        return deleteBy;
    }

    public void setDeleteBy(String deleteBy) {
        this.deleteBy = deleteBy;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    public String getCheckVersion() {
        return checkVersion;
    }

    public void setCheckVersion(String checkVersion) {
        this.checkVersion = checkVersion;
    }

    @Override
    public String toString() {
        return "SnapshotDataVisualizationInfo{" +
        "id = " + id +
        ", name = " + name +
        ", pid = " + pid +
        ", orgId = " + orgId +
        ", level = " + level +
        ", nodeType = " + nodeType +
        ", type = " + type +
        ", canvasStyleData = " + canvasStyleData +
        ", componentData = " + componentData +
        ", mobileLayout = " + mobileLayout +
        ", status = " + status +
        ", selfWatermarkStatus = " + selfWatermarkStatus +
        ", sort = " + sort +
        ", createTime = " + createTime +
        ", createBy = " + createBy +
        ", updateTime = " + updateTime +
        ", updateBy = " + updateBy +
        ", remark = " + remark +
        ", source = " + source +
        ", deleteFlag = " + deleteFlag +
        ", deleteTime = " + deleteTime +
        ", deleteBy = " + deleteBy +
        ", version = " + version +
        ", contentId = " + contentId +
        ", checkVersion = " + checkVersion +
        "}";
    }
}
