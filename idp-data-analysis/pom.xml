<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dib</groupId>
        <artifactId>idp-ms</artifactId>
        <version>3.8.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>idp-data-analysis</artifactId>
    <packaging>pom</packaging>
    <description>
        数据分析模块
    </description>
    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <h2.version>2.2.220</h2.version>
        <knife4j.version>4.4.0</knife4j.version>
        <calcite-core.version>1.35.18</calcite-core.version>
        <commons-dbcp2.version>2.6.0</commons-dbcp2.version>
        <antlr.version>3.5.2</antlr.version>
        <java-jwt.version>3.12.1</java-jwt.version>
        <velocity.version>2.3</velocity.version>
        <ehcache.version>3.10.8</ehcache.version>
        <bcprov.version>1.78</bcprov.version>
        <junit.version>4.13.2</junit.version>
        <httpclient.version>4.5.14</httpclient.version>
        <httpcore.version>4.4.16</httpcore.version>
        <easyexcel.version>3.3.4</easyexcel.version>
        <commons-io.version>2.16.1</commons-io.version>
        <flatten-maven.version>1.3.0</flatten-maven.version>
        <maven.exec.version>3.1.0</maven.exec.version>
        <guava.version>33.0.0-jre</guava.version>
        <commons-net.version>3.8.0</commons-net.version>
        <selenium-java.version>4.19.1</selenium-java.version>
        <angus-mail.version>2.0.3</angus-mail.version>
        <mysql-connector-j.version>8.2.0</mysql-connector-j.version>
        <itextpdf.version>8.0.4</itextpdf.version>
        <flexmark.version>0.62.2</flexmark.version>
        <mybatis-spring.version>3.0.3</mybatis-spring.version>
        <commons-compress.version>1.26.2</commons-compress.version>
        <jsch.version>0.1.55</jsch.version>
    </properties>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-parent</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-websocket</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatisplus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-dependencies</artifactId>
                <version>${knife4j.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${java-jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ehcache</groupId>
                <artifactId>ehcache</artifactId>
                <version>${ehcache.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>
            <dependency>
                <artifactId>commons-io</artifactId>
                <groupId>commons-io</groupId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-connector-j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dib</groupId>
                <artifactId>idp-framework</artifactId>
                <version>3.8.2</version>
            </dependency>
        </dependencies>

    </dependencyManagement>


<modules>
    <module>idp-analysis-sdk</module>
    <module>idp-data-analysis-ms</module>
</modules>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/templates/**</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>



</project>