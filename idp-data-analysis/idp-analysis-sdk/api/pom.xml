<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dib</groupId>
        <artifactId>idp-analysis-sdk</artifactId>
        <version>3.8.2</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>api</artifactId>

    <packaging>pom</packaging>
    <modules>
        <module>api-permissions</module>
        <module>api-base</module>
        <module>api-sync</module>
    </modules>

    <dependencies>

        <dependency>
            <groupId>com.dib</groupId>
            <artifactId>common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>io.dataease</groupId>
            <artifactId>dataease-license-sdk</artifactId>
            <version>2.10.7</version>
        </dependency>

    </dependencies>

</project>