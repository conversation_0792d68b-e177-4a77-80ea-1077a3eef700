<?xml version="1.0" encoding="UTF-8"?><transformation><info><name>组织信息同步</name><description>从t_org_info同步数据到t_org_info_copy</description><trans_type>Normal</trans_type><directory>/home/<USER>/directory><parameters></parameters><size_rowset>10000</size_rowset><sleep_time_empty>50</sleep_time_empty><sleep_time_full>50</sleep_time_full><unique_connections>N</unique_connections><feedback_size>50000</feedback_size><using_thread_priorities>Y</using_thread_priorities></info><connection><name>mysql_conn</name><server>************</server><type>MYSQL</type><access>Native</access><database>zsj-eip</database><port>15002</port><username>dev</username><password>J?I5S}pz,%(1]61I</password><attributes><attribute><code>SUPPORTS_BOOLEAN_DATA_TYPE</code><attribute>Y</attribute></attribute><attribute><code>SUPPORTS_TIMESTAMP_DATA_TYPE</code><attribute>Y</attribute></attribute><attribute><code>QUOTE_ALL_FIELDS</code><attribute>N</attribute></attribute><attribute><code>FORCE_IDENTIFIERS_TO_LOWERCASE</code><attribute>N</attribute></attribute><attribute><code>FORCE_IDENTIFIERS_TO_UPPERCASE</code><attribute>N</attribute></attribute></attributes></connection><order><hop><from>table_input</from><to>table_output</to><enabled>Y</enabled></hop></order><step><name>table_input</name><type>TableInput</type><distribute>Y</distribute><copies>1</copies><connection>mysql_conn</connection><sql>SELECT id, org_name, dt_upd_tm, dt_crt_tm, c_crt_usr, c_upd_usr, IFNULL(is_del, 0) as is_del FROM t_org_info WHERE id IS NOT NULL</sql><limit>0</limit><execute_each_row>N</execute_each_row><variables_active>Y</variables_active><lazy_conversion_active>N</lazy_conversion_active><fields><field><name>id</name><type>String</type><length>32</length><precision>-1</precision></field><field><name>org_name</name><type>String</type><length>50</length><precision>-1</precision></field><field><name>dt_upd_tm</name><type>Timestamp</type><length>-1</length><precision>-1</precision></field><field><name>dt_crt_tm</name><type>Timestamp</type><length>-1</length><precision>-1</precision></field><field><name>c_crt_usr</name><type>String</type><length>50</length><precision>-1</precision></field><field><name>c_upd_usr</name><type>String</type><length>50</length><precision>-1</precision></field><field><name>is_del</name><type>Boolean</type><length>-1</length><precision>-1</precision></field></fields><GUI><xloc>200</xloc><yloc>120</yloc><draw>Y</draw></GUI></step><step><name>table_output</name><type>TableOutput</type><distribute>Y</distribute><copies>1</copies><connection>mysql_conn</connection><schema/><table>t_org_info_copy</table><commit>1000</commit><truncate>Y</truncate><ignore_errors>N</ignore_errors><use_batch>Y</use_batch><specify_fields>Y</specify_fields><partitioning_enabled>N</partitioning_enabled><partitioning_field/><partitioning_daily>N</partitioning_daily><partitioning_monthly>N</partitioning_monthly><tablename_in_field>N</tablename_in_field><tablename_field/><tablename_in_table>N</tablename_in_table><return_keys>N</return_keys><return_field/><fields><field><column_name>id</column_name><stream_name>id</stream_name></field><field><column_name>org_name</column_name><stream_name>org_name</stream_name></field><field><column_name>dt_upd_tm</column_name><stream_name>dt_upd_tm</stream_name></field><field><column_name>dt_crt_tm</column_name><stream_name>dt_crt_tm</stream_name></field><field><column_name>c_crt_usr</column_name><stream_name>c_crt_usr</stream_name></field><field><column_name>c_upd_usr</column_name><stream_name>c_upd_usr</stream_name></field><field><column_name>is_del</column_name><stream_name>is_del</stream_name></field></fields><field_database>id</field_database><field_database>org_name</field_database><field_database>dt_upd_tm</field_database><field_database>dt_crt_tm</field_database><field_database>c_crt_usr</field_database><field_database>c_upd_usr</field_database><field_database>is_del</field_database><field_stream>id</field_stream><field_stream>org_name</field_stream><field_stream>dt_upd_tm</field_stream><field_stream>dt_crt_tm</field_stream><field_stream>c_crt_usr</field_stream><field_stream>c_upd_usr</field_stream><field_stream>is_del</field_stream><GUI><xloc>400</xloc><yloc>120</yloc><draw>Y</draw></GUI></step><nodeList><flowInfo><Id>org_info_sync_flow</Id><Name>组织信息同步流程</Name><Remark>从源表同步数据到目标表</Remark></flowInfo><nodeList><id>table_input</id><label>表输入</label><top>120px</top><left>200px</left><icon>iconfont icon-kongjian1</icon><pluginId>TableInput</pluginId><seconPluginType>inout</seconPluginType></nodeList><nodeList><id>table_output</id><label>表输出</label><top>120px</top><left>400px</left><icon>iconfont icon-kongjian1</icon><pluginId>TableOutput</pluginId><seconPluginType>inout</seconPluginType></nodeList><lineList><from>table_input</from><to>table_output</to><id>line1</id><Remark>数据流向</Remark></lineList></nodeList></transformation>