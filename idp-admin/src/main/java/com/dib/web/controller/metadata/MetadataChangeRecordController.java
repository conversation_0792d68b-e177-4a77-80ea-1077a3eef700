package com.dib.web.controller.metadata;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.controller.BaseController;
import com.dib.common.core.domain.AjaxResult;
import com.dib.core.database.core.JsonPage;
import com.dib.metadata.dto.MetadataChangeRecordDto;
import com.dib.metadata.entity.MetadataChangeRecordEntity;
import com.dib.metadata.mapstruct.MetadataChangeRecordMapper;
import com.dib.metadata.query.MetadataChangeRecordQuery;
import com.dib.metadata.service.MetadataChangeRecordService;
import com.dib.metadata.validate.ValidationGroups;
import com.dib.metadata.vo.MetadataChangeRecordVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 元数据变更记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-30
 */
@Tag(name = "元数据变更记录表")
@RestController
@RequestMapping("/changeRecords")
public class MetadataChangeRecordController extends BaseController {

    @Autowired
    private MetadataChangeRecordService metadataChangeRecordService;

    @Autowired
    private MetadataChangeRecordMapper metadataChangeRecordMapper;

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @Parameter(name = "id", description = "ID", required = true)
    @GetMapping("/{id}")
    public AjaxResult getMetadataChangeRecordById(@PathVariable String id) {
        MetadataChangeRecordEntity metadataChangeRecordEntity = metadataChangeRecordService.getMetadataChangeRecordById(id);
        return AjaxResult.success(metadataChangeRecordMapper.toVO(metadataChangeRecordEntity));
    }

    /**
     * 分页查询信息
     *
     * @param metadataChangeRecordQuery
     * @return
     */
    @Operation(summary = "分页查询")
    @Parameters({
            @Parameter(name = "metadataChangeRecordQuery", description = "查询实体metadataChangeRecordQuery", required = true)
    })
    @GetMapping("/page")
    public AjaxResult getMetadataChangeRecordPage(MetadataChangeRecordQuery metadataChangeRecordQuery) {
        QueryWrapper<MetadataChangeRecordEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(metadataChangeRecordQuery.getObjectId()), "r.object_id", metadataChangeRecordQuery.getObjectId());
        queryWrapper.like(StrUtil.isNotBlank(metadataChangeRecordQuery.getFieldName()), "r.field_name", metadataChangeRecordQuery.getFieldName());
        IPage<MetadataChangeRecordEntity> page = metadataChangeRecordService.page(new Page<>(metadataChangeRecordQuery.getPageNum(), metadataChangeRecordQuery.getPageSize()), queryWrapper);
        List<MetadataChangeRecordVo> collect = page.getRecords().stream().map(metadataChangeRecordMapper::toVO).collect(Collectors.toList());
        JsonPage<MetadataChangeRecordVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }

    /**
     * 添加
     * @param metadataChangeRecord
     * @return
     */
    @Operation(summary = "添加信息", description = "根据metadataChangeRecord对象添加信息")
    @Parameter(name = "metadataChangeRecord", description = "详细实体metadataChangeRecord", required = true)
    @PostMapping()
    public AjaxResult saveMetadataChangeRecord(@RequestBody @Validated({ValidationGroups.Insert.class}) MetadataChangeRecordDto metadataChangeRecord) {
        MetadataChangeRecordEntity metadataChangeRecordEntity = metadataChangeRecordService.saveMetadataChangeRecord(metadataChangeRecord);
        return AjaxResult.success(metadataChangeRecordMapper.toVO(metadataChangeRecordEntity));
    }

    /**
     * 修改
     * @param metadataChangeRecord
     * @return
     */
    @Operation(summary = "修改信息", description = "根据url的id来指定修改对象，并根据传过来的信息来修改详细信息")
    @Parameters({
            @Parameter(name = "id", description = "ID", required = true),
            @Parameter(name = "metadataChangeRecord", description = "详细实体metadataChangeRecord", required = true)
    })
    @PutMapping("/{id}")
    public AjaxResult updateMetadataChangeRecord(@PathVariable String id, @RequestBody @Validated({ValidationGroups.Update.class}) MetadataChangeRecordDto metadataChangeRecord) {
        MetadataChangeRecordEntity metadataChangeRecordEntity = metadataChangeRecordService.updateMetadataChangeRecord(metadataChangeRecord);
        return AjaxResult.success(metadataChangeRecordMapper.toVO(metadataChangeRecordEntity));
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Operation(summary = "删除", description = "根据url的id来指定删除对象")
    @Parameter(name = "id", description = "ID", required = true)
    @DeleteMapping("/{id}")
    public AjaxResult deleteMetadataChangeRecordById(@PathVariable String id) {
        metadataChangeRecordService.deleteMetadataChangeRecordById(id);
        return AjaxResult.success();
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @Operation(summary = "批量删除角色", description = "根据url的ids来批量删除对象")
    @Parameter(name = "ids", description = "ID集合", required = true)
    @DeleteMapping("/batch/{ids}")
    public AjaxResult deleteMetadataChangeRecordBatch(@PathVariable List<String> ids) {
        metadataChangeRecordService.deleteMetadataChangeRecordBatch(ids);
        return AjaxResult.success();
    }
}
