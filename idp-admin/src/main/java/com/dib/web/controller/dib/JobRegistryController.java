package com.dib.web.controller.dib;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dib.bigdata.entity.BaseForm;
import com.dib.bigdata.entity.JobRegistry;
import com.dib.bigdata.service.JobRegistryService;
import com.dib.bigdata.util.PageUtils;
import com.dib.common.core.data.R;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * Created by jingwk on 2019/11/17
 */
@RestController
@RequestMapping("/api/jobRegistry")
@Tag(name = "Job注册表控制器")
public class JobRegistryController extends BaseController {

	@Autowired
	private JobRegistryService jobRegistryService;

	/**
	 * 分页查询所有数据
	 *
	 * @return 所有数据
	 */
	@GetMapping
	@Operation(summary = "分页查询所有数据")
	@Parameter(name = "current", description = "当前页", required = true, schema = @Schema(type = "integer", defaultValue = "1"))
	@Parameter(name = "size", description = "一页大小", required = true, schema = @Schema(type = "integer", defaultValue = "10"))
	@Parameter(name = "ifCount", description = "是否查询总数", required = true, schema = @Schema(type = "boolean", defaultValue = "true"))
	@Parameter(name = "ascs", description = "升序字段，多个用逗号分隔")
	@Parameter(name = "descs", description = "降序字段，多个用逗号分隔")
	public R selectAll() {
		BaseForm baseForm = new BaseForm();
		return R.ok(this.jobRegistryService.page(baseForm.getPlusPagingQueryEntity(), pageQueryWrapperCustom(baseForm.getParameters())));
	}

	/**
	 * 自定义查询组装
	 *
	 * @param map
	 * @return
	 */
	protected QueryWrapper<JobRegistry> pageQueryWrapperCustom(Map<String, Object> map) {
		// mybatis plus 分页相关的参数
		Map<String, Object> pageHelperParams = PageUtils.filterPageParams(map);
		//过滤空值，分页查询相关的参数
		Map<String, Object> columnQueryMap = PageUtils.filterColumnQueryParams(map);

		QueryWrapper<JobRegistry> queryWrapper = new QueryWrapper<>();

		//排序 操作
		pageHelperParams.forEach((k, v) -> {
			switch (k) {
				case "ascs":
					queryWrapper.orderByAsc(StrUtil.toUnderlineCase(StrUtil.toString(v)));
					break;
				case "descs":
					queryWrapper.orderByDesc(StrUtil.toUnderlineCase(StrUtil.toString(v)));
					break;
			}
		});

		//遍历进行字段查询条件组装
		columnQueryMap.forEach((k, v) -> {
			switch (k) {
				case "datasourceName":
					queryWrapper.like(StrUtil.toUnderlineCase(k), v);
					break;
				default:
					queryWrapper.eq(StrUtil.toUnderlineCase(k), v);
			}
		});

		return queryWrapper;
	}
}
