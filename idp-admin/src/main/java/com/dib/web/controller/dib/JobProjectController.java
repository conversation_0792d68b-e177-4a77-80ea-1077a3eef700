package com.dib.web.controller.dib;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dib.bigdata.entity.JobProject;
import com.dib.bigdata.service.JobProjectService;
import com.dib.common.core.data.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * project manage controller
 *
 * <AUTHOR> 2022-05-24 16:13:16
 */
@RestController
@RequestMapping("/api/jobProject")
@Tag(name = "项目管理模块")
public class JobProjectController extends BaseController {

    @Autowired
    private JobProjectService jobProjectService;


    /**
     * 分页查询所有数据
     *
     * @return 所有数据
     */
    @GetMapping
    @Operation(summary = "分页查询所有数据")
    @PreAuthorize("@ss.hasPermi('datax:jobProject:list')")
    public R selectAll(@RequestParam(value = "searchVal", required = false) String searchVal,
                       @RequestParam("pageSize") Integer pageSize,
                       @RequestParam("pageNo") Integer pageNo) {

        return R.ok(jobProjectService.getProjectListPaging(pageSize, pageNo, searchVal));
    }

    /**
     * Get all project
     *
     * @return
     */
    @Operation(summary = "获取所有数据")
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermi('datax:jobProject:query')")
    public R selectList() {
        QueryWrapper<JobProject> query = new QueryWrapper();
        query.eq("flag", true);
        return R.ok(jobProjectService.list(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @Operation(summary = "通过主键查询单条数据")
    @GetMapping("{id}")
    @PreAuthorize("@ss.hasPermi('datax:jobProject:query')")
    public R selectOne(@PathVariable Serializable id) {
        return R.ok(this.jobProjectService.getById(id));
    }

    /**
     * 新增数据
     *
     * @param entity 实体对象
     * @return 新增结果
     */
    @Operation(summary = "新增数据")
    @PostMapping
    @PreAuthorize("@ss.hasPermi('datax:jobProject:add')")
    public R insert(HttpServletRequest request, @RequestBody JobProject entity) {
        entity.setUserId(getUserId());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        LambdaUpdateWrapper<JobProject> lqw = new LambdaUpdateWrapper<>();
        lqw.eq(JobProject::getName, entity.getName());
        long count = jobProjectService.count(lqw);
        if (count > 0){
            return R.error("项目已存在");
        }
        return R.ok(this.jobProjectService.save(entity));
    }


    /**
     * 修改数据
     *
     * @param entity 实体对象
     * @return 修改结果
     */
    @PutMapping
    @Operation(summary = "修改数据")
    @PreAuthorize("@ss.hasPermi('datax:jobProject:edit')")
    public R update(@RequestBody JobProject entity) {
        JobProject project = jobProjectService.getById(entity.getId());
        project.setName(entity.getName());
        project.setDescription(entity.getDescription());
        entity.setUpdateTime(new Date());
        return R.ok(this.jobProjectService.updateById(entity));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @Operation(summary = "删除数据")
    @PreAuthorize("@ss.hasPermi('datax:jobProject:remove')")
    public R delete(@RequestParam("idList") List<String> idList) {
        return R.ok(this.jobProjectService.removeByIds(idList));
    }
}