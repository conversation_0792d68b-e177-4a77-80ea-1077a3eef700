package com.dib.web.controller.quality;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.controller.BaseController;
import com.dib.common.core.domain.AjaxResult;
import com.dib.core.database.core.JsonPage;
import com.dib.quality.entity.RuleTypeEntity;
import com.dib.quality.entity.RuleTypeReportEntity;
import com.dib.quality.mapstruct.RuleTypeMapper;
import com.dib.quality.query.RuleTypeQuery;
import com.dib.quality.service.RuleTypeService;
import com.dib.quality.vo.RuleTypeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 规则类型信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-27
 */
@Tag(name = "规则类型信息表")
@RestController
@RequestMapping("/quality/ruleTypes")
public class RuleTypeController extends BaseController {

    @Autowired
    private RuleTypeService ruleTypeService;

    @Autowired
    private RuleTypeMapper ruleTypeMapper;

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @Parameter(name = "id", description = "ID", required = true)
    @GetMapping("/{id}")
    public AjaxResult getRuleTypeById(@PathVariable String id) {
        RuleTypeEntity ruleTypeEntity = ruleTypeService.getRuleTypeById(id);
        return AjaxResult.success(ruleTypeMapper.toVO(ruleTypeEntity));
    }

    @Operation(summary = "获取列表")
    @GetMapping("/list")
    public AjaxResult getRuleTypeList() {
        List<RuleTypeEntity> list = ruleTypeService.list(Wrappers.emptyWrapper());
        return AjaxResult.success(list);
    }

    @Operation(summary = "获取列表")
    @GetMapping("/report/list")
    public AjaxResult getRuleTypeListForReport() {
        List<RuleTypeReportEntity> list = ruleTypeService.getRuleTypeListForReport();
        return AjaxResult.success(list);
    }

    /**
     * 分页查询信息
     *
     * @param ruleTypeQuery
     * @return
     */
    @Operation(summary = "分页查询")
    @GetMapping("/page")
    public AjaxResult getRuleTypePage(RuleTypeQuery ruleTypeQuery) {
        QueryWrapper<RuleTypeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(ruleTypeQuery.getName()), "name", ruleTypeQuery.getName());
        IPage<RuleTypeEntity> page = ruleTypeService.page(new Page<>(ruleTypeQuery.getPageNum(), ruleTypeQuery.getPageSize()), queryWrapper);
        List<RuleTypeVo> collect = page.getRecords().stream().map(ruleTypeMapper::toVO).collect(Collectors.toList());
        JsonPage<RuleTypeVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }
}
