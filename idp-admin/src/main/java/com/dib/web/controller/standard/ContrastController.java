package com.dib.web.controller.standard;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.controller.BaseController;
import com.dib.common.core.domain.AjaxResult;
import com.dib.core.database.core.JsonPage;
import com.dib.metadata.validate.ValidationGroups;
import com.dib.standard.dto.ContrastDto;
import com.dib.standard.entity.ContrastEntity;
import com.dib.standard.mapstruct.ContrastMapper;
import com.dib.standard.query.ContrastQuery;
import com.dib.standard.service.ContrastService;
import com.dib.standard.vo.ContrastStatisticVo;
import com.dib.standard.vo.ContrastTreeVo;
import com.dib.standard.vo.ContrastVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 对照表信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-27
 */
@Tag(name = "对照表信息表")
@RestController
@RequestMapping("/standard/contrasts")
public class ContrastController extends BaseController {

    @Autowired
    private ContrastService contrastService;

    @Autowired
    private ContrastMapper contrastMapper;

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @Parameter(name = "id", description = "ID", required = true)
    @GetMapping("/{id}")
    public AjaxResult getContrastById(@PathVariable String id) {
        ContrastEntity contrastEntity = contrastService.getContrastById(id);
        return AjaxResult.success(contrastMapper.toVO(contrastEntity));
    }

    /**
     * 分页查询信息
     *
     * @param contrastQuery
     * @return
     */
    @Operation(summary = "分页查询")
    @Parameter(name = "contrastQuery", description = "查询实体contrastQuery", required = true)
    @GetMapping("/page")
    public AjaxResult getContrastPage(ContrastQuery contrastQuery) {
        QueryWrapper<ContrastEntity> queryWrapper = new QueryWrapper<>();
        IPage<ContrastEntity> page = contrastService.page(new Page<>(contrastQuery.getPageNum(), contrastQuery.getPageSize()), queryWrapper);
        List<ContrastVo> collect = page.getRecords().stream().map(contrastMapper::toVO).collect(Collectors.toList());
        JsonPage<ContrastVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }

    /**
     * 添加
     * @param contrast
     * @return
     */
    @Operation(summary = "添加信息", description = "根据contrast对象添加信息")
    @Parameter(name = "contrast", description = "详细实体contrast", required = true)
    @PostMapping()
    public AjaxResult saveContrast(@RequestBody @Validated({ValidationGroups.Insert.class}) ContrastDto contrast) {
        ContrastEntity contrastEntity = contrastService.saveContrast(contrast);
        return AjaxResult.success(contrastMapper.toVO(contrastEntity));
    }

    /**
     * 修改
     * @param contrast
     * @return
     */
    @Operation(summary = "修改信息", description = "根据url的id来指定修改对象，并根据传过来的信息来修改详细信息")
    @Parameters({
            @Parameter(name = "id", description = "ID", required = true),
            @Parameter(name = "contrast", description = "详细实体contrast", required = true)
    })
    @PutMapping("/{id}")
    public AjaxResult updateContrast(@PathVariable String id, @RequestBody @Validated({ValidationGroups.Update.class}) ContrastDto contrast) {
        ContrastEntity contrastEntity = contrastService.updateContrast(contrast);
        return AjaxResult.success(contrastMapper.toVO(contrastEntity));
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Operation(summary = "删除", description = "根据url的id来指定删除对象")
    @Parameter(name = "id", description = "ID", required = true)
    @DeleteMapping("/{id}")
    public AjaxResult deleteContrastById(@PathVariable String id) {
        contrastService.deleteContrastById(id);
        return AjaxResult.success();
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @Operation(summary = "批量删除角色", description = "根据url的ids来批量删除对象")
    @Parameter(name = "ids", description = "ID集合", required = true)
    @DeleteMapping("/batch/{ids}")
    public AjaxResult deleteContrastBatch(@PathVariable List<String> ids) {
        contrastService.deleteContrastBatch(ids);
        return AjaxResult.success();
    }

    @GetMapping("/tree")
    public AjaxResult getContrastTree() {
        List<ContrastTreeVo> list = contrastService.getContrastTree();
        return AjaxResult.success(list);
    }

    /**
     * 分页查询统计信息
     *
     * @param contrastQuery
     * @return
     */
    @Operation(summary = "分页查询")
    @Parameter(name = "contrastQuery", description = "查询实体contrastQuery", required = true)
    @GetMapping("/stat")
    public AjaxResult contrastStat(ContrastQuery contrastQuery) {
        QueryWrapper<ContrastEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(contrastQuery.getSourceName()), "c.source_name", contrastQuery.getSourceName());
        queryWrapper.like(StrUtil.isNotBlank(contrastQuery.getTableName()), "c.table_name", contrastQuery.getTableName());
        queryWrapper.like(StrUtil.isNotBlank(contrastQuery.getColumnName()), "c.column_name", contrastQuery.getColumnName());
        IPage<ContrastEntity> page = contrastService.statistic(new Page<>(contrastQuery.getPageNum(), contrastQuery.getPageSize()), queryWrapper);
        List<ContrastStatisticVo> collect = page.getRecords().stream().map(contrastMapper::toSVO).collect(Collectors.toList());
        JsonPage<ContrastStatisticVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }
}
