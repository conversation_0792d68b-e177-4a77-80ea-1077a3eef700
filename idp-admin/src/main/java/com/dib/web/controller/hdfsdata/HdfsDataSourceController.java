package com.dib.web.controller.hdfsdata;

import com.dib.common.core.domain.AjaxResult;
import com.dib.hdfsdata.entity.HdfsDataSource;
import com.dib.hdfsdata.service.HdfsDataSourceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;


@RestController
@RequestMapping("/api/hdfs")
@Tag(name = "HDFS数据源配置")
public class HdfsDataSourceController {

    @Autowired
    private HdfsDataSourceService hdfsService;

    @Operation(summary = "获取所有 HDFS 配置")
    @GetMapping("/list")
    public AjaxResult list() {
        return AjaxResult.success(hdfsService.list());
    }

    @Operation(summary = "添加 HDFS 配置")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody HdfsDataSource config) {
        return AjaxResult.success(hdfsService.save(config));
    }

    @Operation(summary = "更新 HDFS 配置")
    @PutMapping("/update")
    public AjaxResult update(@RequestBody HdfsDataSource config) {
        return AjaxResult.success(hdfsService.updateById(config));
    }

    @Operation(summary = "删除 HDFS 配置")
    @DeleteMapping("/{id}")
    public AjaxResult delete(@PathVariable String id) {
        return AjaxResult.success(hdfsService.removeById(id));
    }

    @Operation(summary = "测试连接")
    @PostMapping("/test")
    public AjaxResult testConnection(@RequestBody HdfsDataSource config) {
        return AjaxResult.success(hdfsService.testConnection(config));
    }

    @Operation(summary = "下载HFTP文件")
    @PostMapping("/downloadHFtpFile")
    public AjaxResult downloadHFtpFile(@RequestBody HdfsDataSource config , HttpServletResponse response) {
        return AjaxResult.success(hdfsService.downloadHFtpFile(config,response));
    }
}
