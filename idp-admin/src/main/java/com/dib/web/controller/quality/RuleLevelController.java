package com.dib.web.controller.quality;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.controller.BaseController;
import com.dib.common.core.domain.AjaxResult;
import com.dib.core.database.core.JsonPage;
import com.dib.quality.entity.RuleLevelEntity;
import com.dib.quality.mapstruct.RuleLevelMapper;
import com.dib.quality.query.RuleLevelQuery;
import com.dib.quality.service.RuleLevelService;
import com.dib.quality.vo.RuleLevelVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 规则级别信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-14
 */
@Tag(name = "规则级别信息表")
@RestController
@RequestMapping("/quality/ruleLevels")
public class RuleLevelController extends BaseController {

    @Autowired
    private RuleLevelService ruleLevelService;

    @Autowired
    private RuleLevelMapper ruleLevelMapper;

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @GetMapping("/{id}")
    public AjaxResult getRuleLevelById(@Parameter(description = "ID", required = true) @PathVariable String id) {
        RuleLevelEntity ruleLevelEntity = ruleLevelService.getRuleLevelById(id);
        return AjaxResult.success(ruleLevelMapper.toVO(ruleLevelEntity));
    }

    @Operation(summary = "获取列表")
    @GetMapping("/list")
    public AjaxResult getRuleTypeList() {
        List<RuleLevelEntity> list = ruleLevelService.list(Wrappers.emptyWrapper());
        return AjaxResult.success(list);
    }

    /**
     * 分页查询信息
     *
     * @param ruleLevelQuery
     * @return
     */
    @Operation(summary = "分页查询")
    @GetMapping("/page")
    public AjaxResult getRuleLevelPage(@Parameter(description = "查询实体ruleLevelQuery", required = true) RuleLevelQuery ruleLevelQuery) {
        QueryWrapper<RuleLevelEntity> queryWrapper = new QueryWrapper<>();
        IPage<RuleLevelEntity> page = ruleLevelService.page(new Page<>(ruleLevelQuery.getPageNum(), ruleLevelQuery.getPageSize()), queryWrapper);
        List<RuleLevelVo> collect = page.getRecords().stream().map(ruleLevelMapper::toVO).collect(Collectors.toList());
        JsonPage<RuleLevelVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }
}
