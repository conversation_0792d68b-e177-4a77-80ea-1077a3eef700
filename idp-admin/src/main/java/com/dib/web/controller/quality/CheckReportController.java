package com.dib.web.controller.quality;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.controller.BaseController;
import com.dib.common.core.domain.AjaxResult;
import com.dib.core.database.core.JsonPage;
import com.dib.quality.entity.CheckReportEntity;
import com.dib.quality.entity.DataReportEntity;
import com.dib.quality.mapstruct.CheckReportMapper;
import com.dib.quality.query.CheckReportQuery;
import com.dib.quality.service.CheckReportService;
import com.dib.quality.vo.CheckReportVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 核查报告信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-27
 */
@Tag(name = "核查报告信息表")
@RestController
@RequestMapping("/quality/checkReports")
public class CheckReportController extends BaseController {

    @Autowired
    private CheckReportService checkReportService;

    @Autowired
    private CheckReportMapper checkReportMapper;

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @Parameter(name = "id", description = "ID", required = true)
    @GetMapping("/{id}")
    public AjaxResult getCheckReportById(@PathVariable String id) {
        CheckReportEntity checkReportEntity = checkReportService.getCheckReportById(id);
        return AjaxResult.success(checkReportMapper.toVO(checkReportEntity));
    }

    /**
     * 分页查询信息
     *
     * @param checkReportQuery
     * @return
     */
    @Operation(summary = "分页查询")
    @Parameters({
            @Parameter(name = "checkReportQuery", description = "查询实体checkReportQuery", required = true)
    })
    @GetMapping("/page")
    public AjaxResult getCheckReportPage(CheckReportQuery checkReportQuery) {
        QueryWrapper<CheckReportEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(checkReportQuery.getRuleTypeId()), "r.rule_type_id", checkReportQuery.getRuleTypeId());
        queryWrapper.like(StrUtil.isNotBlank(checkReportQuery.getRuleName()), "r.rule_name", checkReportQuery.getRuleName());
        queryWrapper.like(StrUtil.isNotBlank(checkReportQuery.getRuleSource()), "r.rule_source", checkReportQuery.getRuleSource());
        queryWrapper.like(StrUtil.isNotBlank(checkReportQuery.getRuleTable()), "r.rule_table", checkReportQuery.getRuleTable());
        queryWrapper.like(StrUtil.isNotBlank(checkReportQuery.getRuleColumn()), "r.rule_column", checkReportQuery.getRuleColumn());
        // 确定唯一核查报告
        queryWrapper.apply("c.check_batch = r.last_check_batch");
        IPage<CheckReportEntity> page = checkReportService.page(new Page<>(checkReportQuery.getPageNum(), checkReportQuery.getPageSize()), queryWrapper);
        List<CheckReportVo> collect = page.getRecords().stream().map(checkReportMapper::toVO).collect(Collectors.toList());
        JsonPage<CheckReportVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }

    @GetMapping("/getReportBySource")
    public AjaxResult getReportBySource(CheckReportQuery checkReportQuery) {
        LocalDate checkDate = checkReportQuery.getCheckDate();
        String date = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(checkDate);
        List<DataReportEntity> list = checkReportService.getReportBySource(date);
        return AjaxResult.success(list);
    }

    @GetMapping("/getReportByType")
    public AjaxResult getReportByType(CheckReportQuery checkReportQuery) {
        LocalDate checkDate = checkReportQuery.getCheckDate();
        String date = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(checkDate);
        List<DataReportEntity> list = checkReportService.getReportByType(date);
        return AjaxResult.success(list);
    }

    @GetMapping("/getReportDetail")
    public AjaxResult getReportDetail(CheckReportQuery checkReportQuery) {
        LocalDate checkDate = checkReportQuery.getCheckDate();
        String date = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(checkDate);
        Map<String, Object> map = checkReportService.getReportDetail(date);
        return AjaxResult.success(map);
    }
}
