package com.dib.web.controller.metadata;

import com.dib.common.core.controller.BaseController;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.domain.AjaxResult;
import com.dib.core.database.core.JsonPage;
import com.dib.metadata.dto.MetadataColumnDto;
import com.dib.metadata.entity.MetadataColumnEntity;
import com.dib.metadata.query.MetadataColumnQuery;
import com.dib.metadata.service.MetadataColumnMapper;
import com.dib.metadata.service.MetadataColumnService;
import com.dib.metadata.validate.ValidationGroups;
import com.dib.metadata.vo.MetadataColumnVo;
import com.dib.metadata.vo.MetadataTreeVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 元数据信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-29
 */
@Tag(name = "元数据信息表")
@RestController
@RequestMapping("/columns")
public class MetadataColumnController extends BaseController {

    @Autowired
    private MetadataColumnService metadataColumnService;

    @Autowired
    private MetadataColumnMapper metadataColumnMapper;

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @Parameter(name = "id", description = "ID", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(type = "string"))
    @GetMapping("/{id}")
    public AjaxResult getDataMetadataColumnById(@PathVariable String id) {
        MetadataColumnEntity metadataColumnEntity = metadataColumnService.getMetadataColumnById(id);
        return AjaxResult.success(metadataColumnMapper.toVO(metadataColumnEntity));
    }

    @Operation(summary = "获取列表", description = "")
    @PostMapping("/list")
    public AjaxResult getDataMetadataColumnList(@RequestBody MetadataColumnQuery metadataColumnQuery) {
        metadataColumnQuery.setPageSize(999);
        List<MetadataColumnEntity> list = metadataColumnService.getDataMetadataColumnList(metadataColumnQuery);
        List<MetadataColumnVo> collect = list.stream().map(metadataColumnMapper::toVO).collect(Collectors.toList());
        return AjaxResult.success(collect);
    }

    /**
     * 分页查询信息
     *
     * @param metadataColumnQuery
     * @return
     */
    @Operation(summary = "分页查询", description = "")
    @Parameter(name = "dataMetadataColumnQuery", description = "查询实体dataMetadataColumnQuery", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = MetadataColumnQuery.class))
    @GetMapping("/page")
    public AjaxResult getDataMetadataColumnPage(MetadataColumnQuery metadataColumnQuery) {
        QueryWrapper<MetadataColumnEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(metadataColumnQuery.getColumnName()), "c.column_name", metadataColumnQuery.getColumnName());
        queryWrapper.eq(StrUtil.isNotBlank(metadataColumnQuery.getSourceId()), "c.source_id", metadataColumnQuery.getSourceId());
        queryWrapper.eq(StrUtil.isNotBlank(metadataColumnQuery.getTableId()), "c.table_id", metadataColumnQuery.getTableId());
        IPage<MetadataColumnEntity> page = metadataColumnService.pageWithAuth(new Page<>(metadataColumnQuery.getPageNum(), metadataColumnQuery.getPageSize()), queryWrapper);
        List<MetadataColumnVo> collect = page.getRecords().stream().map(metadataColumnMapper::toVO).collect(Collectors.toList());
        JsonPage<MetadataColumnVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }

    /**
     * 添加
     * @param dataMetadataColumn
     * @return
     */
    @Operation(summary = "添加信息", description = "根据dataMetadataColumn对象添加信息")
    @Parameter(name = "dataMetadataColumn", description = "详细实体dataMetadataColumn", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = MetadataColumnDto.class))
    @PostMapping()
    public AjaxResult saveDataMetadataColumn(@RequestBody @Validated({ValidationGroups.Insert.class}) MetadataColumnDto dataMetadataColumn) {
        MetadataColumnEntity metadataColumnEntity = metadataColumnService.saveMetadataColumn(dataMetadataColumn);
        return AjaxResult.success(metadataColumnMapper.toVO(metadataColumnEntity));
    }

    /**
     * 修改
     * @param dataMetadataColumn
     * @return
     */
    @Operation(summary = "修改信息", description = "根据url的id来指定修改对象，并根据传过来的信息来修改详细信息")
    @Parameter(name = "id", description = "ID", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(type = "string"))
    @Parameter(name = "dataMetadataColumn", description = "详细实体dataMetadataColumn", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = MetadataColumnDto.class))
    @PutMapping("/{id}")
    public AjaxResult updateDataMetadataColumn(@PathVariable String id, @RequestBody @Validated({ValidationGroups.Update.class}) MetadataColumnDto dataMetadataColumn) {
        MetadataColumnEntity metadataColumnEntity = metadataColumnService.updateMetadataColumn(dataMetadataColumn);
        return AjaxResult.success(metadataColumnMapper.toVO(metadataColumnEntity));
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Operation(summary = "删除", description = "根据url的id来指定删除对象")
    @Parameter(name = "id", description = "ID", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(type = "string"))
    @DeleteMapping("/{id}")
    public AjaxResult deleteDataMetadataColumnById(@PathVariable String id) {
        metadataColumnService.deleteMetadataColumnById(id);
        return AjaxResult.success();
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @Operation(summary = "批量删除角色", description = "根据url的ids来批量删除对象")
    @Parameter(name = "ids", description = "ID集合", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(type = "array"))
    @DeleteMapping("/batch/{ids}")
    public AjaxResult deleteDataMetadataColumnBatch(@PathVariable List<String> ids) {
        metadataColumnService.deleteMetadataColumnBatch(ids);
        return AjaxResult.success();
    }

    /**
     * 获取层级树
     * @param level 层级database、table、column
     * @return
     */
    @Operation(summary = "获取层级树", description = "根据url的层级来获取树对象")
    @Parameter(name = "level", description = "层级", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(type = "string"))
    @Parameter(name = "metadataColumnQuery", description = "查询实体metadataColumnQuery", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = MetadataColumnQuery.class))
    @GetMapping("/tree/{level}")
    public AjaxResult getDataMetadataTree(@PathVariable String level, MetadataColumnQuery metadataColumnQuery) {
        List<MetadataTreeVo> list = metadataColumnService.getDataMetadataTree(level, metadataColumnQuery);
        return AjaxResult.success(list);
    }
}
