package com.dib.web.controller.ftpdata;

import com.dib.common.core.domain.AjaxResult;
import com.dib.ftpdata.entity.FtpConfig;
import com.dib.ftpdata.service.FtpConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;


@RestController
@RequestMapping("/api/ftp-config")
@Tag(name = "FTP配置管理")
public class FtpConfigController {

    @Autowired
    private FtpConfigService ftpConfigService;

    @Operation(summary = "获取所有 FTP 配置")
    @GetMapping("/list")
    public AjaxResult list() {
        return AjaxResult.success(ftpConfigService.list());
    }

    @Operation(summary = "添加 FTP 配置")
    @PostMapping("/add")
    public AjaxResult add(@RequestBody FtpConfig config) {
        return AjaxResult.success(ftpConfigService.save(config));
    }

    @Operation(summary = "更新 FTP 配置")
    @PutMapping("/update")
    public AjaxResult update(@RequestBody FtpConfig config) {
        return AjaxResult.success(ftpConfigService.updateById(config));
    }

    @Operation(summary = "删除 FTP 配置")
    @DeleteMapping("/{id}")
    public AjaxResult delete(@PathVariable String id) {
        return AjaxResult.success(ftpConfigService.removeById(id));
    }

    @Operation(summary = "测试 FTP 连接")
    @PostMapping("/test")
    public AjaxResult testConnection(@RequestBody FtpConfig config) {
        return AjaxResult.success(ftpConfigService.testConnection(config));
    }


    @Operation(summary = "下载FTP文件")
    @PostMapping("/downloadFtpFile")
    public AjaxResult downloadFtpFile(@RequestBody FtpConfig config , HttpServletResponse response) {
        return AjaxResult.success(ftpConfigService.downloadFtpFile(config,response));
    }
}
