package com.dib.web.controller.standard;

import cn.hutool.core.util.StrUtil;
import com.dib.common.core.domain.AjaxResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.controller.BaseController;
import com.dib.core.database.core.JsonPage;
import com.dib.metadata.validate.ValidationGroups;
import com.dib.standard.dto.ContrastDictDto;
import com.dib.standard.entity.ContrastDictEntity;
import com.dib.standard.mapstruct.ContrastDictMapper;
import com.dib.standard.query.ContrastDictQuery;
import com.dib.standard.service.ContrastDictService;
import com.dib.standard.vo.ContrastDictVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 字典对照信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-27
 */
@Tag(name = "字典对照信息表")
@RestController
@RequestMapping("/standard/contrastDicts")
public class ContrastDictController extends BaseController {

    @Autowired
    private ContrastDictService contrastDictService;

    @Autowired
    private ContrastDictMapper contrastDictMapper;

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @Parameter(name = "id", description = "ID", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(type = "string"))
    @GetMapping("/{id}")
    public AjaxResult getContrastDictById(@PathVariable String id) {
        ContrastDictEntity contrastDictEntity = contrastDictService.getContrastDictById(id);
        return AjaxResult.success(contrastDictMapper.toVO(contrastDictEntity));
    }

    /**
     * 分页查询信息
     *
     * @param contrastDictQuery
     * @return
     */
    @Operation(summary = "分页查询", description = "")
    @Parameter(name = "contrastDictQuery", description = "查询实体contrastDictQuery", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ContrastDictQuery.class))
    @GetMapping("/page")
    public AjaxResult getContrastDictPage(ContrastDictQuery contrastDictQuery) {
        QueryWrapper<ContrastDictEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(contrastDictQuery.getContrastId()), "d.contrast_id", contrastDictQuery.getContrastId());
        queryWrapper.like(StrUtil.isNotBlank(contrastDictQuery.getColCode()), "d.col_code", contrastDictQuery.getColCode());
        queryWrapper.like(StrUtil.isNotBlank(contrastDictQuery.getColName()), "d.col_name", contrastDictQuery.getColName());
        IPage<ContrastDictEntity> page = contrastDictService.page(new Page<>(contrastDictQuery.getPageNum(), contrastDictQuery.getPageSize()), queryWrapper);
        List<ContrastDictVo> collect = page.getRecords().stream().map(contrastDictMapper::toVO).collect(Collectors.toList());
        JsonPage<ContrastDictVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }

    /**
     * 添加
     * @param contrastDict
     * @return
     */
    @Operation(summary = "添加信息", description = "根据contrastDict对象添加信息")
    @Parameter(name = "contrastDict", description = "详细实体contrastDict", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ContrastDictDto.class))
    @PostMapping()
    public AjaxResult saveContrastDict(@RequestBody @Validated({ValidationGroups.Insert.class}) ContrastDictDto contrastDict) {
        ContrastDictEntity contrastDictEntity = contrastDictService.saveContrastDict(contrastDict);
        return AjaxResult.success(contrastDictMapper.toVO(contrastDictEntity));
    }

    /**
     * 修改
     * @param contrastDict
     * @return
     */
    @Operation(summary = "修改信息", description = "根据url的id来指定修改对象，并根据传过来的信息来修改详细信息")
    @Parameter(name = "id", description = "ID", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(type = "string"))
    @Parameter(name = "contrastDict", description = "详细实体contrastDict", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ContrastDictDto.class))
    @PutMapping("/{id}")
    public AjaxResult updateContrastDict(@PathVariable String id, @RequestBody @Validated({ValidationGroups.Update.class}) ContrastDictDto contrastDict) {
        ContrastDictEntity contrastDictEntity = contrastDictService.updateContrastDict(contrastDict);
        return AjaxResult.success(contrastDictMapper.toVO(contrastDictEntity));
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Operation(summary = "删除", description = "根据url的id来指定删除对象")
    @Parameter(name = "id", description = "ID", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(type = "string"))
    @DeleteMapping("/{id}")
    public AjaxResult deleteContrastDictById(@PathVariable String id) {
        contrastDictService.deleteContrastDictById(id);
        return AjaxResult.success();
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @Operation(summary = "批量删除角色", description = "根据url的ids来批量删除对象")
    @Parameter(name = "ids", description = "ID集合", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(type = "array"))
    @DeleteMapping("/batch/{ids}")
    public AjaxResult deleteContrastDictBatch(@PathVariable List<String> ids) {
        contrastDictService.deleteContrastDictBatch(ids);
        return AjaxResult.success();
    }
}
