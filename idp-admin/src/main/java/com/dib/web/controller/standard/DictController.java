package com.dib.web.controller.standard;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.controller.BaseController;
import com.dib.core.database.core.JsonPage;
import com.dib.metadata.validate.ValidationGroups;
import com.dib.standard.dto.DictDto;
import com.dib.standard.entity.DictEntity;
import com.dib.standard.mapstruct.DictMapper;
import com.dib.standard.query.DictQuery;
import com.dib.standard.service.DictService;
import com.dib.standard.vo.DictVo;
import com.dib.common.core.domain.AjaxResult;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据标准字典表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-26
 */
@Tag(name = "数据标准字典表")
@RestController
@RequestMapping("/standard/dicts")
public class DictController extends BaseController {

    @Autowired
    private DictService dictService;

    @Autowired
    private DictMapper dictMapper;

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @Parameter(name = "id", description = "ID", required = true, schema = @Schema(type = "string"))
    @GetMapping("/{id}")
    public AjaxResult getDictById(@PathVariable String id) {
        DictEntity dictEntity = dictService.getDictById(id);
        return AjaxResult.success(dictMapper.toVO(dictEntity));
    }

    /**
     * 分页查询信息
     *
     * @param dictQuery
     * @return
     */
    @Operation(summary = "分页查询", description = "")
    @Parameter(name = "dictQuery", description = "查询实体dictQuery", required = true, schema = @Schema(implementation = DictQuery.class))
    @GetMapping("/page")
    public AjaxResult getDictPage(DictQuery dictQuery) {
        QueryWrapper<DictEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(dictQuery.getTypeId()), "d.type_id", dictQuery.getTypeId());
        queryWrapper.like(StrUtil.isNotBlank(dictQuery.getGbCode()), "d.gb_code", dictQuery.getGbCode());
        queryWrapper.like(StrUtil.isNotBlank(dictQuery.getGbName()), "d.gb_name", dictQuery.getGbName());
        IPage<DictEntity> page = dictService.page(new Page<>(dictQuery.getPageNum(), dictQuery.getPageSize()), queryWrapper);
        List<DictVo> collect = page.getRecords().stream().map(dictMapper::toVO).collect(Collectors.toList());
        JsonPage<DictVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }

    /**
     * 添加
     * @param dict
     * @return
     */
    @Operation(summary = "添加信息", description = "根据dict对象添加信息")
    @Parameter(name = "dict", description = "详细实体dict", required = true, schema = @Schema(implementation = DictDto.class))
    @PostMapping()
    public AjaxResult saveDict(@RequestBody @Validated({ValidationGroups.Insert.class}) DictDto dict) {
        DictEntity dictEntity = dictService.saveDict(dict);
        return AjaxResult.success(dictMapper.toVO(dictEntity));
    }

    /**
     * 修改
     * @param dict
     * @return
     */
    @Operation(summary = "修改信息", description = "根据url的id来指定修改对象，并根据传过来的信息来修改详细信息")
    @Parameter(name = "id", description = "ID", required = true, schema = @Schema(type = "string"))
    @Parameter(name = "dict", description = "详细实体dict", required = true, schema = @Schema(implementation = DictDto.class))
    @PutMapping("/{id}")
    public AjaxResult updateDict(@PathVariable String id, @RequestBody @Validated({ValidationGroups.Update.class}) DictDto dict) {
        DictEntity dictEntity = dictService.updateDict(dict);
        return AjaxResult.success(dictMapper.toVO(dictEntity));
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Operation(summary = "删除", description = "根据url的id来指定删除对象")
    @Parameter(name = "id", description = "ID", required = true, schema = @Schema(type = "string"))
    @DeleteMapping("/{id}")
    public AjaxResult deleteDictById(@PathVariable String id) {
        dictService.deleteDictById(id);
        return AjaxResult.success();
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @Operation(summary = "批量删除角色", description = "根据url的ids来批量删除对象")
    @Parameter(name = "ids", description = "ID集合", required = true, schema = @Schema(type = "array"))
    @DeleteMapping("/batch/{ids}")
    public AjaxResult deleteDictBatch(@PathVariable List<String> ids) {
        dictService.deleteDictBatch(ids);
        return AjaxResult.success();
    }

    /**
     * 刷新字典缓存
     *
     * @return
     */
    @GetMapping("/refresh")
    public AjaxResult refreshDict() {
        dictService.refreshDict();
        return AjaxResult.success();
    }
}
