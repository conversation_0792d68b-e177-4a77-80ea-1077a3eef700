package com.dib.web.controller.metadata;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.controller.BaseController;
import com.dib.common.core.domain.AjaxResult;
import com.dib.core.database.core.JsonPage;
import com.dib.metadata.dto.MetadataTableDto;
import com.dib.metadata.entity.MetadataTableEntity;
import com.dib.metadata.mapstruct.MetadataTableMapper;
import com.dib.metadata.query.MetadataTableQuery;
import com.dib.metadata.service.MetadataTableService;
import com.dib.metadata.validate.ValidationGroups;
import com.dib.metadata.vo.MetadataTableVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据库表信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-29
 */
@Tag(name = "数据库表信息表")
@RestController
@RequestMapping("/tables")
public class MetadataTableController extends BaseController {

    @Autowired
    private MetadataTableService metadataTableService;

    @Autowired
    private MetadataTableMapper metadataTableMapper;

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @Parameter(name = "id", description = "ID", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(type = "string"))
    @GetMapping("/{id}")
    public AjaxResult getDataMetadataTableById(@PathVariable String id) {
        MetadataTableEntity metadataTableEntity = metadataTableService.getMetadataTableById(id);
        return AjaxResult.success(metadataTableMapper.toVO(metadataTableEntity));
    }

    @Operation(summary = "获取列表", description = "")
    @GetMapping("/list")
    @PreAuthorize("@ss.hasPermi('metadata:tables:list')")
    public AjaxResult getDataMetadataTableList(MetadataTableQuery metadataTableQuery) {
        metadataTableQuery.setPageSize(999);
        List<MetadataTableEntity> list = metadataTableService.getDataMetadataTableList(metadataTableQuery);
        List<MetadataTableVo> collect = list.stream().map(metadataTableMapper::toVO).collect(Collectors.toList());
        return AjaxResult.success(collect);
    }

    /**
     * 分页查询信息
     *
     * @param metadataTableQuery
     * @return
     */
    @Operation(summary = "分页查询", description = "")
    @Parameter(name = "dataMetadataTableQuery", description = "查询实体dataMetadataTableQuery", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = MetadataTableQuery.class))
    @GetMapping("/page")
    public AjaxResult getDataMetadataTablePage(MetadataTableQuery metadataTableQuery) {
        QueryWrapper<MetadataTableEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(metadataTableQuery.getTableName()), "t.table_name", metadataTableQuery.getTableName());
        queryWrapper.eq(StrUtil.isNotBlank(metadataTableQuery.getSourceId()), "t.source_id", metadataTableQuery.getSourceId());
        IPage<MetadataTableEntity> page = metadataTableService.pageWithAuth(new Page<>(metadataTableQuery.getPageNum(), metadataTableQuery.getPageSize()), queryWrapper);
        List<MetadataTableVo> collect = page.getRecords().stream().map(metadataTableMapper::toVO).collect(Collectors.toList());
        JsonPage<MetadataTableVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }

    /**
     * 添加
     * @param dataMetadataTable
     * @return
     */
    @Operation(summary = "添加信息", description = "根据dataMetadataTable对象添加信息")
    @Parameter(name = "dataMetadataTable", description = "详细实体dataMetadataTable", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = MetadataTableDto.class))
    @PostMapping()
    @PreAuthorize("@ss.hasPermi('metadata:tables:add')")
    public AjaxResult saveDataMetadataTable(@RequestBody @Validated({ValidationGroups.Insert.class}) MetadataTableDto dataMetadataTable) {
        MetadataTableEntity metadataTableEntity = metadataTableService.saveMetadataTable(dataMetadataTable);
        return AjaxResult.success(metadataTableMapper.toVO(metadataTableEntity));
    }

    /**
     * 修改
     * @param dataMetadataTable
     * @return
     */
    @Operation(summary = "修改信息", description = "根据url的id来指定修改对象，并根据传过来的信息来修改详细信息")
    @Parameter(name = "id", description = "ID", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(type = "string"))
    @Parameter(name = "dataMetadataTable", description = "详细实体dataMetadataTable", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = MetadataTableDto.class))
    @PutMapping("/{id}")
    @PreAuthorize("@ss.hasPermi('metadata:tables:edit')")
    public AjaxResult updateDataMetadataTable(@PathVariable String id, @RequestBody @Validated({ValidationGroups.Update.class}) MetadataTableDto dataMetadataTable) {
        MetadataTableEntity metadataTableEntity = metadataTableService.updateMetadataTable(dataMetadataTable);
        return AjaxResult.success(metadataTableMapper.toVO(metadataTableEntity));
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Operation(summary = "删除", description = "根据url的id来指定删除对象")
    @Parameter(name = "id", description = "ID", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(type = "string"))
    @DeleteMapping("/{id}")
    @PreAuthorize("@ss.hasPermi('metadata:tables:remove')")
    public AjaxResult deleteDataMetadataTableById(@PathVariable String id) {
        metadataTableService.deleteMetadataTableById(id);
        return AjaxResult.success();
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @Operation(summary = "批量删除角色", description = "根据url的ids来批量删除对象")
    @Parameter(name = "ids", description = "ID集合", required = true, schema = @io.swagger.v3.oas.annotations.media.Schema(type = "array"))
    @DeleteMapping("/batch/{ids}")
    @PreAuthorize("@ss.hasPermi('metadata:tables:remove')")
    public AjaxResult deleteDataMetadataTableBatch(@PathVariable List<String> ids) {
        metadataTableService.deleteMetadataTableBatch(ids);
        return AjaxResult.success();
    }
}
