package com.dib.web.controller.quality;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.controller.BaseController;
import com.dib.common.core.domain.AjaxResult;
import com.dib.core.database.core.JsonPage;
import com.dib.quality.entity.RuleItemEntity;
import com.dib.quality.mapstruct.RuleItemMapper;
import com.dib.quality.query.RuleItemQuery;
import com.dib.quality.service.RuleItemService;
import com.dib.quality.vo.RuleItemVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 规则核查项信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-15
 */
@Tag(name = "规则核查项信息表")
@RestController
@RequestMapping("/quality/ruleItems")
public class RuleItemController extends BaseController {

    @Autowired
    private RuleItemService ruleItemService;

    @Autowired
    private RuleItemMapper ruleItemMapper;

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @Parameter(name = "id", description = "ID", required = true)
    @GetMapping("/{id}")
    public AjaxResult getRuleItemById(@PathVariable String id) {
        RuleItemEntity ruleItemEntity = ruleItemService.getRuleItemById(id);
        return AjaxResult.success(ruleItemMapper.toVO(ruleItemEntity));
    }

    @Operation(summary = "获取列表")
    @GetMapping("/list")
    public AjaxResult getRuleTypeList(RuleItemQuery ruleItemQuery) {
        QueryWrapper<RuleItemEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(ruleItemQuery.getRuleTypeId()), "rule_type_id", ruleItemQuery.getRuleTypeId());
        List<RuleItemEntity> list = ruleItemService.list(queryWrapper);
        return AjaxResult.success(list);
    }

    /**
     * 分页查询信息
     *
     * @param ruleItemQuery
     * @return
     */
    @Operation(summary = "分页查询")
    @GetMapping("/page")
    public AjaxResult getRuleItemPage(RuleItemQuery ruleItemQuery) {
        QueryWrapper<RuleItemEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(ruleItemQuery.getRuleTypeId()), "rule_type_id", ruleItemQuery.getRuleTypeId());
        IPage<RuleItemEntity> page = ruleItemService.page(new Page<>(ruleItemQuery.getPageNum(), ruleItemQuery.getPageSize()), queryWrapper);
        List<RuleItemVo> collect = page.getRecords().stream().map(ruleItemMapper::toVO).collect(Collectors.toList());
        JsonPage<RuleItemVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }
}
