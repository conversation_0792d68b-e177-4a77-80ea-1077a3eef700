package com.dib.web.controller.quality;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.controller.BaseController;
import com.dib.common.core.domain.AjaxResult;
import com.dib.core.database.core.JsonPage;
import com.dib.metadata.validate.ValidationGroups;
import com.dib.quality.dto.CheckRuleDto;
import com.dib.quality.entity.CheckRuleEntity;
import com.dib.quality.mapstruct.CheckRuleMapper;
import com.dib.quality.query.CheckRuleQuery;
import com.dib.quality.service.CheckRuleService;
import com.dib.quality.vo.CheckRuleVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 核查规则信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-27
 */
@Tag(name = "核查规则信息表")
@RestController
@RequestMapping("/quality/checkRules")
public class CheckRuleController extends BaseController {

    @Autowired
    private CheckRuleService checkRuleService;

    @Autowired
    private CheckRuleMapper checkRuleMapper;

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @Parameter(name = "id", description = "ID", required = true)
    @GetMapping("/{id}")
    public AjaxResult getCheckRuleById(@PathVariable String id) {
        CheckRuleEntity checkRuleEntity = checkRuleService.getCheckRuleById(id);
        return AjaxResult.success(checkRuleMapper.toVO(checkRuleEntity));
    }

    /**
     * 分页查询信息
     *
     * @param checkRuleQuery
     * @return
     */
    @Operation(summary = "分页查询")
    @Parameters({
            @Parameter(name = "checkRuleQuery", description = "查询实体checkRuleQuery", required = true)
    })
    @GetMapping("/page")
    public AjaxResult getCheckRulePage(CheckRuleQuery checkRuleQuery) {
        QueryWrapper<CheckRuleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(checkRuleQuery.getRuleTypeId()), "r.rule_type_id", checkRuleQuery.getRuleTypeId());
        queryWrapper.like(StrUtil.isNotBlank(checkRuleQuery.getRuleName()), "r.rule_name", checkRuleQuery.getRuleName());
        queryWrapper.like(StrUtil.isNotBlank(checkRuleQuery.getRuleSource()), "r.rule_source", checkRuleQuery.getRuleSource());
        queryWrapper.like(StrUtil.isNotBlank(checkRuleQuery.getRuleTable()), "r.rule_table", checkRuleQuery.getRuleTable());
        queryWrapper.like(StrUtil.isNotBlank(checkRuleQuery.getRuleColumn()), "r.rule_column", checkRuleQuery.getRuleColumn());
        IPage<CheckRuleEntity> page = checkRuleService.page(new Page<>(checkRuleQuery.getPageNum(), checkRuleQuery.getPageSize()), queryWrapper);
        List<CheckRuleVo> collect = page.getRecords().stream().map(checkRuleMapper::toVO).collect(Collectors.toList());
        JsonPage<CheckRuleVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }

    /**
     * 添加
     * @param checkRule
     * @return
     */
    @Operation(summary = "添加信息", description = "根据checkRule对象添加信息")
    @Parameter(name = "checkRule", description = "详细实体checkRule", required = true)
    @PostMapping()
    public AjaxResult saveCheckRule(@RequestBody @Validated({ValidationGroups.Insert.class}) CheckRuleDto checkRule) {
        CheckRuleEntity checkRuleEntity = checkRuleService.saveCheckRule(checkRule);
        return AjaxResult.success(checkRuleMapper.toVO(checkRuleEntity));
    }

    /**
     * 修改
     * @param checkRule
     * @return
     */
    @Operation(summary = "修改信息", description = "根据url的id来指定修改对象，并根据传过来的信息来修改详细信息")
    @Parameters({
            @Parameter(name = "id", description = "ID", required = true),
            @Parameter(name = "checkRule", description = "详细实体checkRule", required = true)
    })
    @PutMapping("/{id}")
    public AjaxResult updateCheckRule(@PathVariable String id, @RequestBody @Validated({ValidationGroups.Update.class}) CheckRuleDto checkRule) {
        CheckRuleEntity checkRuleEntity = checkRuleService.updateCheckRule(checkRule);
        return AjaxResult.success(checkRuleMapper.toVO(checkRuleEntity));
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Operation(summary = "删除", description = "根据url的id来指定删除对象")
    @Parameter(name = "id", description = "ID", required = true)
    @DeleteMapping("/{id}")
    public AjaxResult deleteCheckRuleById(@PathVariable String id) {
        checkRuleService.deleteCheckRuleById(id);
        return AjaxResult.success();
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @Operation(summary = "批量删除", description = "根据url的ids来批量删除对象")
    @Parameter(name = "ids", description = "ID集合", required = true)
    @DeleteMapping("/batch/{ids}")
    public AjaxResult deleteCheckRuleBatch(@PathVariable List<String> ids) {
        checkRuleService.deleteCheckRuleBatch(ids);
        return AjaxResult.success();
    }
}
