package com.dib.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dib.domin.entity.CateGoryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface CateGoryMapper extends BaseMapper<CateGoryEntity> {

    @Select("WITH RECURSIVE category_tree (id, parent_id, asset_code, level) AS ( " +
            "   SELECT id, parent_id, asset_code, 1 as level FROM cate_gory WHERE id = #{id} " +
            "   UNION ALL " +
            "   SELECT c.id, c.parent_id, c.asset_code, ct.level + 1 " +
            "   FROM cate_gory c " +
            "   INNER JOIN category_tree ct ON c.id = ct.parent_id " +
            ") " +
            "SELECT id, asset_code FROM category_tree ORDER BY level DESC")
    List<Map<String, Object>> selectAssetCodePathById(@Param("id") String id);

}
