package com.dib.market.dto;

import com.dib.metadata.validate.ValidationGroups;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 数据API信息表 实体DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-31
 */
@Schema(description = "数据API信息表Model")
@Data
public class DataApiDto implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @NotBlank(message = "主键ID不能为空", groups = {ValidationGroups.Update.class})
    private String id;
    @Schema(description = "API名称")
    @NotBlank(message = "API名称不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String apiName;
    @Schema(description = "API版本")
    @NotBlank(message = "API版本不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String apiVersion;
    @Schema(description = "API路径")
    @NotBlank(message = "API路径不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String apiUrl;
    @Schema(description = "请求方式")
    @NotBlank(message = "请求方式不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String reqMethod;
    @Schema(description = "返回格式")
    @NotBlank(message = "返回格式不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String resType;
    @Schema(description = "IP黑名单多个用英文,隔开")
    private String deny;
    @Schema(description = "限流配置")
    @Valid
    private RateLimit rateLimit;
    @Schema(description = "执行配置")
    @Valid
    private ExecuteConfig executeConfig;
    @Schema(description = "请求参数")
    @Valid
    @NotEmpty(message = "请求参数不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    @Size(min = 1, message="请求参数长度不能少于{min}位")
    private List<ReqParam> reqParams;
    @Schema(description = "返回参数")
    @Valid
    @NotEmpty(message = "返回字段不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    @Size(min = 1, message="返回字段长度不能少于{min}位")
    private List<ResParam> resParams;
    @Schema(description = "状态")
    @NotNull(message = "状态不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String status;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "接口编码")
    private String apiCode;
}
