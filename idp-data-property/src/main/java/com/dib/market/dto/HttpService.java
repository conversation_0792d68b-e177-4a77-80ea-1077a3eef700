package com.dib.market.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Schema(description = "http接口Model")
@Data
public class HttpService implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "请求地址")
    private String url;
    @Schema(description = "请求头")
    private String header;
    @Schema(description = "请求参数")
    private String param;
    @Schema(description = "请求方式")
    private String httpMethod;
}
