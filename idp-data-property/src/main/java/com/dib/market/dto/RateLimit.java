package com.dib.market.dto;

import com.dib.metadata.validate.ValidationGroups;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

@Schema(description = "限流信息Model")
@Data
public class RateLimit implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "是否限流：0:否，1：是")
    @NotNull(message = "是否限流不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String enable;
    @Schema(description = "请求次数默认5次")
    private Integer times = 5;
    @Schema(description = "请求时间范围默认60秒")
    private Integer seconds = 60;
}
