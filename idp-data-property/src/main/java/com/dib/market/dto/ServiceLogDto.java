package com.dib.market.dto;

import com.dib.metadata.validate.ValidationGroups;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 服务集成调用日志表 实体DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-20
 */
@Schema(description = "服务集成调用日志表Model")
@Data
public class ServiceLogDto implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @NotBlank(message = "主键ID不能为空", groups = {ValidationGroups.Update.class})
    private String id;
    @Schema(description = "服务id")
    private String serviceId;
    @Schema(description = "调用者id")
    private String callerId;
    @Schema(description = "调用者ip")
    private String callerIp;
    @Schema(description = "调用时间")
    private LocalDateTime callerDate;
    @Schema(description = "调用请求头")
    private String callerHeader;
    @Schema(description = "调用请求参数")
    private String callerParam;
    @Schema(description = "调用报文")
    private String callerSoap;
    @Schema(description = "调用耗时")
    private Long time;
    @Schema(description = "信息记录")
    private String msg;
    @Schema(description = "状态：0:失败，1：成功")
    private String status;
}
