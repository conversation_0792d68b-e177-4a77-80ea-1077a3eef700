package com.dib.market.dto;

import com.dib.metadata.validate.ValidationGroups;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

@Schema(description = "字段规则Model")
@Data
public class FieldRule implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "字段名")
    @NotBlank(message = "字段名称不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String fieldName;
    @Schema(description = "脱敏类型")
    @NotNull(message = "脱敏类型不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String cipherType;
    @Schema(description = "规则类型")
    @NotNull(message = "规则类型不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String cryptType;
}
