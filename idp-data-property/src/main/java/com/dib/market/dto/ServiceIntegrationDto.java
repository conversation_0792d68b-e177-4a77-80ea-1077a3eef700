package com.dib.market.dto;

import com.dib.metadata.validate.ValidationGroups;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 服务集成表 实体DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-20
 */
@Schema(description = "服务集成表Model")
@Data
public class ServiceIntegrationDto implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @NotBlank(message = "主键ID不能为空", groups = {ValidationGroups.Update.class})
    private String id;
    @Schema(description = "服务名称")
    @NotBlank(message = "服务名称不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String serviceName;
    @Schema(description = "服务类型（1http接口，2webservice接口）")
    private String serviceType;
    @Schema(description = "http接口")
    @Valid
    private HttpService httpService;
    @Schema(description = "webservice接口")
    @Valid
    private WebService webService;
    @Schema(description = "状态")
    @NotNull(message = "状态不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String status;
    @Schema(description = "备注")
    private String remark;
}
