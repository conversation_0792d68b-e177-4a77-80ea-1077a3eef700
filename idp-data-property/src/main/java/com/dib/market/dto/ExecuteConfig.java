package com.dib.market.dto;

import com.dib.metadata.validate.ValidationGroups;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Schema(description = "执行配置信息Model")
@Data
public class ExecuteConfig implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "数据源")
    @NotBlank(message = "数据源不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String sourceId;

    @Schema(description = "配置方式")
    @NotNull(message = "配置方式不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String configType;

    @Schema(description = "数据库表主键")
    private String tableId;

    @Schema(description = "数据库表")
    private String tableName;

    @Schema(description = "表字段列表")
    @Valid
    private List<FieldParam> fieldParams;

    @Schema(description = "解析SQL")
    private String sqlText;
}
