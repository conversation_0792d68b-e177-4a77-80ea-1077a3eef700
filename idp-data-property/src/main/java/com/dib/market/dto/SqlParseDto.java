package com.dib.market.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class SqlParseDto implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "数据源")
    @NotBlank(message = "数据源不能为空")
    private String sourceId;

    @Schema(description = "SQL文本")
    @NotBlank(message = "SQL不能为空")
    private String sqlText;
}
