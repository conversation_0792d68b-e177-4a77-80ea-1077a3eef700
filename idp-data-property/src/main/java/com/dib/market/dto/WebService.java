package com.dib.market.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Schema(description = "webservice接口Model")
@Data
public class WebService implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "wsdl地址")
    private String wsdl;
    @Schema(description = "命名空间")
    private String targetNamespace;
    @Schema(description = "请求报文")
    private String soap;
    @Schema(description = "调用方法")
    private String method;
}
