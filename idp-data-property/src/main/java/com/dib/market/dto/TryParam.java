package com.dib.market.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

@Schema(description = "API调用参数信息Model")
@Data
public class TryParam implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "参数名称")
    @NotBlank(message = "参数名称不能为空")
    private String paramName;

    @Schema(description = "参数值")
    private Object paramValue;
}
