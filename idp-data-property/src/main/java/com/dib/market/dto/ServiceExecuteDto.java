package com.dib.market.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Schema(description = "服务调用Model")
@Data
public class ServiceExecuteDto implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "服务编号")
    private String serviceNo;

    @Schema(description = "http服务请求头")
    private String header;
    @Schema(description = "http服务请求参数")
    private String param;

    @Schema(description = "webservice服务请求报文")
    private String soap;
}
