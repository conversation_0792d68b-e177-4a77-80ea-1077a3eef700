package com.dib.market.dto;

import com.dib.metadata.validate.ValidationGroups;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;


import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 数据API脱敏信息表 实体DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-14
 */
@Schema(description = "数据API脱敏信息表Model")
@Data
public class ApiMaskDto implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @NotBlank(message = "主键ID不能为空", groups = {ValidationGroups.Update.class})
    private String id;
    @Schema(description = "数据API")
    @NotBlank(message = "数据API不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String apiId;
    @Schema(description = "脱敏名称")
    @NotBlank(message = "脱敏名称不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String maskName;
    @Schema(description = "脱敏字段规则配置")
    @Valid
    @NotEmpty(message = "脱敏字段规则配置不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    @Size(min = 1, message="脱敏字段规则配置长度不能少于{min}位")
    private List<FieldRule> rules;
    @Schema(description = "状态")
    @NotNull(message = "状态不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String status;
    @Schema(description = "备注")
    private String remark;
}
