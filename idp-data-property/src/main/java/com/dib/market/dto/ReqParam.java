package com.dib.market.dto;

import com.dib.metadata.validate.ValidationGroups;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

@Schema(description = "请求参数信息Model")
@Data
public class ReqParam implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "参数名称")
    @NotBlank(message = "参数名称不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String paramName;

    @Schema(description = "是否为空")
    @NotNull(message = "是否为空不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String nullable;

    @Schema(description = "描述")
    @NotBlank(message = "描述不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String paramComment;

    @Schema(description = "操作符")
    @NotNull(message = "操作符不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String whereType;

    @Schema(description = "参数类型")
    @NotNull(message = "参数类型不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String paramType;

    @Schema(description = "开始时间")
    private String startValue;

    @Schema(description = "结束时间")
    private String endValue;

    @Schema(description = "示例值")
    @NotBlank(message = "示例值不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String exampleValue;

    @Schema(description = "默认值")
    @NotBlank(message = "默认值不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String defaultValue;



}
