package com.dib.market.dto;

import com.dib.metadata.validate.ValidationGroups;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

@Schema(description = "返回参数信息Model")
@Data
public class ResParam implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "字段名称")
    @NotBlank(message = "字段名称不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String fieldName;

    @Schema(description = "描述")
    @NotBlank(message = "描述不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String fieldComment;

    @Schema(description = "数据类型")
    @NotNull(message = "数据类型不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String dataType;

    @Schema(description = "示例值")
    @NotBlank(message = "示例值不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String exampleValue;

    @Schema(description = "字段别名")
    private String fieldAliasName;
}
