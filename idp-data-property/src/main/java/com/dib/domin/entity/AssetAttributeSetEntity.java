package com.dib.domin.entity;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.dib.domin.enums.MountTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;


/**
 * 资产编码属性集
 */
@Data
@TableName(value = "asset_attribute_set",autoResultMap = true)
public class AssetAttributeSetEntity {

    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private String id;

    //目录id
    private String catalogueId;

    //类目id
    private String cateGoryId;

    //发布状态;0：未发布，1：发布   未发布不能挂接
    private String isRelease;

    //上架状态;0：未上架，1：上架，2：下架
    private String isArrive;

    //标签数据集id
    @TableField(value = "label_data_ids",typeHandler = JacksonTypeHandler.class)
    private List<String> labelDataIds;

    //挂接类型；
    @TableField(value = "mount_type",typeHandler = JacksonTypeHandler.class)
    private MountType mountType;

    //挂接信息
    @TableField(value = "mount_info",typeHandler = JacksonTypeHandler.class)
    private MountInfo mountInfo;

    //apiId
    private String dataApiId;


    @TableLogic(value = "0",delval = "1")
    private String isDel;

    private String createBy;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    private String updateBy;

    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;


    public void setMountInfo(MountInfo mountInfo) {
        if (mountInfo != null){
            if (StringUtils.isNotBlank(mountInfo.getSourceId()) && StringUtils.isNotBlank(mountInfo.getTableId())){
                this.mountType = new MountType(MountTypeEnum.TABLE.getCode(), MountTypeEnum.TABLE.getValue(), MountTypeEnum.TABLE.getDataPurview());
            }else if (CollectionUtil.isNotEmpty(mountInfo.getFileArrId()) && StringUtils.isNotBlank(mountInfo.getFilePath())){
                this.mountType = new MountType(MountTypeEnum.FILE.getCode(), MountTypeEnum.FILE.getValue(), MountTypeEnum.FILE.getDataPurview());
            }else if (mountInfo.getModelTableVo() != null){
                this.mountType = new MountType(MountTypeEnum.MODEL.getCode(), MountTypeEnum.MODEL.getValue(), MountTypeEnum.MODEL.getDataPurview());
            }else if (mountInfo.getTDimBaseRelaColVo() != null){
                this.mountType = new MountType(MountTypeEnum.METRIC.getCode(), MountTypeEnum.METRIC.getValue(), MountTypeEnum.METRIC.getDataPurview());
            }else if (mountInfo.getDataAnalysisId() != null) {
                this.mountType = new MountType(MountTypeEnum.DATA_ANALYSIS.getCode(), MountTypeEnum.DATA_ANALYSIS.getValue(), MountTypeEnum.DATA_ANALYSIS.getDataPurview());
            }
        }

        this.mountInfo = mountInfo;
    }
}
