package com.dib.domin.entity;

import com.dib.index.TDimBaseRelaColVo;
import com.dib.model.ModelTableVo;
import lombok.Data;

import java.util.List;

/**
 * 挂接信息
 */
@Data
public class MountInfo {

    //数据库表
    private String sourceId;

    private String tableId;

    //文件id
    private List<String> fileArrId;
    //文件路径
    private String filePath;


    //模型id
    private ModelTableVo modelTableVo;
    //指标id
    private TDimBaseRelaColVo tDimBaseRelaColVo;
    //数据分析id
    private String dataAnalysisId;

}
