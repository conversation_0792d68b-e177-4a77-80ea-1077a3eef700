package com.dib.domin.enums;

import lombok.Getter;

import java.util.List;

@Getter
public enum MountTypeEnum {
    TABLE("1","数据库表", List.of(1,2,4)),
    FILE("2", "文件", List.of(2,4)),
    MODEL("3", "模型", List.of(1,2,3,4)),
    METRIC("4", "指标", List.of(1,2,4)),
    DATA_ANALYSIS("5", "数据分析", List.of(3));

    private final String code;

    private final String value;

    //数据权限；1：数据查询；2：文件下载 3：数据分析  4：申请交换
    private final List<Integer> dataPurview;

    MountTypeEnum(String code, String value, List<Integer> dataPurview) {
        this.code = code;
        this.value = value;
        this.dataPurview = dataPurview;
    }

    public static MountTypeEnum getMountTypeByValue(String value) {
        for (MountTypeEnum mountType : values()) {
            if (mountType.value.equals(value)) {
                return mountType;
            }
        }
        return null;
    }
}
