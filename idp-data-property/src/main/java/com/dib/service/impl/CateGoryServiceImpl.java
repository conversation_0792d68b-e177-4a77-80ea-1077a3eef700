package com.dib.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.aspose.words.net.System.Data.DataException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.domin.dto.CateGoryDto;
import com.dib.domin.entity.AssetAttributeSetEntity;
import com.dib.domin.entity.AssetCodeEntity;
import com.dib.domin.entity.CateGoryEntity;
import com.dib.domin.enums.AssetCodeType;
import com.dib.mapper.AssetAttributeSetMapper;
import com.dib.mapper.AssetCodeMapper;
import com.dib.mapper.CateGoryMapper;
import com.dib.service.CateGoryService;
import com.dib.utils.AssetCodeUtil;
import org.apache.commons.lang3.StringUtils;
import org.datanucleus.store.rdbms.exceptions.NullValueException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.dib.common.utils.SecurityUtils.getUsername;

@Service
public class CateGoryServiceImpl extends ServiceImpl<CateGoryMapper, CateGoryEntity> implements CateGoryService {

    @Autowired
    private CateGoryMapper cateGoryMapper;
    @Autowired
    private AssetCodeMapper assetCodeMapper;
    @Autowired
    private AssetAttributeSetMapper assetAttributeSetMapper;

    @Override
    public boolean addCateGory(CateGoryEntity cateGoryEntity) {
        LambdaQueryWrapper<CateGoryEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(CateGoryEntity::getId, cateGoryEntity.getParentId());
        CateGoryEntity oldCateGoryEntity = getOne(lqw);//查询同父级数据，填充层级
        cateGoryEntity.setLevel(oldCateGoryEntity.getLevel() + 1);



        LambdaQueryWrapper<AssetAttributeSetEntity> aaLqw = new LambdaQueryWrapper<>();
        aaLqw.eq(AssetAttributeSetEntity::getCatalogueId, cateGoryEntity.getCatalogueId())
                .eq(AssetAttributeSetEntity::getCateGoryId, cateGoryEntity.getParentId());
        Long assetAttributeSetCount = assetAttributeSetMapper.selectCount(aaLqw);
        //查询同当前目录同层编码规则
        if (assetAttributeSetCount > 0) {
            throw new NullValueException("当前类目下存在资产，暂不支持添加子类目！");
        }


        LambdaQueryWrapper<AssetCodeEntity> acLqw = new LambdaQueryWrapper<>();
        acLqw.eq(AssetCodeEntity::getCatalogueId, cateGoryEntity.getCatalogueId())
                .eq(AssetCodeEntity::getType, AssetCodeType.CATALOGUE.getCode())
                .eq(AssetCodeEntity::getLevel, cateGoryEntity.getLevel());
        AssetCodeEntity assetCodeEntity = assetCodeMapper.selectOne(acLqw);
        //查询同当前目录同层编码规则
        if (Objects.isNull(assetCodeEntity)) {
            throw new NullValueException("当前添加层级无资产编码规则或超出规则范围，请先补充扩展规则！");
        } else {
            //查询统一父级下的数据，根据编码生成规则进行填充
            LambdaQueryWrapper<CateGoryEntity> countLqw = new LambdaQueryWrapper<>();
            countLqw.eq(CateGoryEntity::getCatalogueId,cateGoryEntity.getCatalogueId());
            countLqw.eq(CateGoryEntity::getParentId, cateGoryEntity.getParentId());
            int count = (int) this.count(countLqw);

            String assetCode = AssetCodeUtil.getAssetCode(assetCodeEntity, count);
            cateGoryEntity.setAssetCode(assetCode);
        }

        cateGoryEntity.setCreateBy(getUsername());
        cateGoryEntity.setUpdateBy(getUsername());
        cateGoryEntity.setUpdateTime(new Date());

        return save(cateGoryEntity);
    }

    @Override
    public List<CateGoryDto> getCateGoryTreeById(String catalogueId) {
        LambdaQueryWrapper<CateGoryEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(CateGoryEntity::getCatalogueId, catalogueId);
        List<CateGoryEntity> allCategories = list(lqw);

        LambdaQueryWrapper<AssetAttributeSetEntity> cgLqw = new LambdaQueryWrapper<>();
        cgLqw.eq(AssetAttributeSetEntity::getCatalogueId, catalogueId);
        cgLqw.select(AssetAttributeSetEntity::getCateGoryId);
        List<AssetAttributeSetEntity> assetAttributeSetEntities = assetAttributeSetMapper.selectList(cgLqw);

        Map<String, Long> assAttributeCountMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(assetAttributeSetEntities)){
            assAttributeCountMap = assetAttributeSetEntities.stream().collect(Collectors.groupingBy(AssetAttributeSetEntity::getCateGoryId, Collectors.counting()));
        }

        Map<String, CateGoryDto> map = new HashMap<>();
        List<CateGoryDto> rootNodes = new ArrayList<>();

        for (CateGoryEntity cateGoryEntity : allCategories) {
            CateGoryDto dto = new CateGoryDto(cateGoryEntity,assAttributeCountMap);
            map.put(dto.getId(), dto);
            if ("0".equals(dto.getParentId())) {
                rootNodes.add(dto);
            }
        }
        // 使用 buildTree 方法构建树形结构
        for (CateGoryDto rootNode : rootNodes) {
            buildTree(rootNode, map);
        }

        return rootNodes;
    }

    private void buildTree(CateGoryDto root, Map<String, CateGoryDto> map) {
        for (CateGoryDto child : map.values()) {
            if (child.getParentId().equals(root.getId())) {
                root.getChildren().add(child);
                buildTree(child, map);
            }
        }
    }

    @Override
    public boolean updateCateGory(CateGoryEntity cateGoryEntity) {
        cateGoryEntity.setUpdateBy(getUsername());
        return this.updateById(cateGoryEntity);
    }

    @Override
    public boolean deleteCateGory(CateGoryEntity cateGoryEntity) {
        //查询所有的子节点
        LambdaQueryWrapper<CateGoryEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(CateGoryEntity::getCatalogueId, cateGoryEntity.getCatalogueId());
        List<CateGoryEntity> allCategories = list(lqw);

        List<String> ids = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(allCategories)){
            Map<String, List<CateGoryEntity>> cateGoryByParentId = allCategories.stream().collect(Collectors.groupingBy(CateGoryEntity::getParentId));

            buildTree(cateGoryEntity.getId(),cateGoryByParentId,ids);

            ids.add(cateGoryEntity.getId());
        }

        //查看类目下的是否存在资产，存在资产不删除
        LambdaQueryWrapper<AssetAttributeSetEntity> aasLqw = new LambdaQueryWrapper<>();
        aasLqw.in(AssetAttributeSetEntity::getCateGoryId,ids);
        long count = assetAttributeSetMapper.selectCount(aasLqw);
        if (count > 0){
            throw new DataException("当前类目或子类目下有资产，请先删除资产再删除类目");
        }


        return this.removeByIds(ids);


    }
    private void buildTree(String parentId , Map<String, List<CateGoryEntity>> cateGoryByParentId,List<String> ids) {
        List<CateGoryEntity> cateGoryEntities = cateGoryByParentId.get(parentId);
        if (CollectionUtil.isNotEmpty(cateGoryEntities)){
            for (CateGoryEntity cateGoryEntity : cateGoryEntities) {
                ids.add(cateGoryEntity.getId());
                buildTree(cateGoryEntity.getId(),cateGoryByParentId,ids);
            }
        }
    }

    /**
     * 获取类目完整编码
     * @param id
     * @return
     */
    public String getAssetCodePath(String id) {
        List<Map<String, Object>> result = cateGoryMapper.selectAssetCodePathById(id);
        if (result == null || result.isEmpty()) {
            return "";
        }

        // 过滤掉 asset_code 为空或 null 的项后再拼接
        return result.stream()
                .map(map -> (String) map.get("asset_code"))
                .filter(StringUtils::isNotBlank)   // 排除空
                .collect(Collectors.joining(""));
    }

}
