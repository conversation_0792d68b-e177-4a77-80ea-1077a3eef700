package com.dib.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.domin.entity.*;
import com.dib.domin.query.CatalogueQuery;
import com.dib.mapper.*;
import com.dib.service.CatalogueAttributeService;
import com.dib.service.CatalogueService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.dib.common.database.utils.SecurityUtil.getUserName;

@Service
public class CatalogueServiceImpl extends ServiceImpl<CatalogueMapper, CatalogueEntity> implements CatalogueService {

    @Autowired
    private CateGoryMapper cateGoryMapper;
    @Autowired
    private AttributeDataMapper attributeDataMapper;
    @Autowired
    private AssetAttributeSetMapper assetAttributeSetMapper;
    @Autowired
    private CatalogueAttributeService catalogueAttributeService;


    @Override
    public List<CatalogueEntity> getCatalogueList() {
        return list();
    }

    @Override
    public PageInfo<CatalogueEntity> getCataloguePage(CatalogueQuery catalogueQuery) {

        LambdaQueryWrapper<CatalogueEntity> lqw = new LambdaQueryWrapper<>();
        if (catalogueQuery.getDataPropertyName() != null) {
            lqw.like(CatalogueEntity::getDataPropertyName, catalogueQuery.getDataPropertyName());
        }
        lqw.orderByDesc(CatalogueEntity::getUpdateTime);
        PageHelper.startPage(catalogueQuery.getPageNum(), catalogueQuery.getPageSize());
        List<CatalogueEntity> list = this.list(lqw);
        PageInfo<CatalogueEntity> pageInfo = new PageInfo<>(list);

        return pageInfo;
    }

    @Override
    public boolean addCatalogue(CatalogueEntity catalogueEntity) {
        catalogueEntity.setEdit("0");
        catalogueEntity.setOpenness("0");
        catalogueEntity.setCreateBy(getUserName());
        catalogueEntity.setUpdateBy(getUserName());
        catalogueEntity.setId(IdWorker.getIdStr());

        //类目首层构建
        CateGoryEntity cateGoryEntity = new CateGoryEntity();
        cateGoryEntity.setParentId("0");
        cateGoryEntity.setCatalogueId(catalogueEntity.getId());
        cateGoryEntity.setCateGoryName(catalogueEntity.getCateGoryName());
        cateGoryEntity.setDescription(catalogueEntity.getDescription());
        cateGoryEntity.setLevel(0);
        cateGoryEntity.setCreateBy(catalogueEntity.getCreateBy());
        cateGoryEntity.setUpdateBy(catalogueEntity.getUpdateBy());
        cateGoryEntity.setUpdateTime(new Date());
        cateGoryEntity.setCreateTime(new Date());
        cateGoryEntity.setIsDel("0");
        int insert = cateGoryMapper.insert(cateGoryEntity);
        catalogueEntity.setCateGoryId(cateGoryEntity.getId());

        //内置属性添加
        saveCatalogueAttribute(catalogueEntity);


        //序号排序
        int count = (int) this.count();
        catalogueEntity.setNumber(count + 1);

        return save(catalogueEntity);
    }


    /**
     * 内置属性添加
     *
     * @param catalogueEntity
     */
    private void saveCatalogueAttribute(CatalogueEntity catalogueEntity) {
        LambdaQueryWrapper<AttributeDataEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(AttributeDataEntity::getIsBuiltIn, 1);
        List<AttributeDataEntity> attributeDataEntities = attributeDataMapper.selectList(lqw);

        List<CatalogueAttributeEntity> catalogueAttributeEntities = new ArrayList<>();
        int num = 1;
        if (CollectionUtil.isNotEmpty(attributeDataEntities)) {
            for (AttributeDataEntity attributeDataEntity : attributeDataEntities) {
                CatalogueAttributeEntity catalogueAttributeEntity = new CatalogueAttributeEntity();
                catalogueAttributeEntity.setCatalogueId(catalogueEntity.getId());
                catalogueAttributeEntity.setAttributeDataId(attributeDataEntity.getId());
                catalogueAttributeEntity.setAttributeSetId(attributeDataEntity.getAttributeSetId());
                catalogueAttributeEntity.setNumber(num);
                catalogueAttributeEntity.setIsDisabled("0");
                catalogueAttributeEntity.setIsPortalDisplay("1");
                catalogueAttributeEntity.setIsEditable("1");

                String attributeCode = attributeDataEntity.getAttributeCode();
                //资产代码、资产类目不可编辑
                if (attributeCode.matches("SJZCLM|SJZCDM")) {
                    catalogueAttributeEntity.setIsEditable("0");
                }
                catalogueAttributeEntity.setCreateBy(catalogueEntity.getCreateBy());
                catalogueAttributeEntity.setUpdateBy(catalogueEntity.getUpdateBy());
                catalogueAttributeEntities.add(catalogueAttributeEntity);
                num++;
            }
            catalogueAttributeService.saveBatch(catalogueAttributeEntities);
        }
    }

    @Override
    public boolean deleteCatalogue(List<String> ids) {

        if (CollectionUtil.isNotEmpty(ids)) {

            //删除目录下所有的类目
            LambdaQueryWrapper<CateGoryEntity> lqw = new LambdaQueryWrapper<>();
            lqw.in(CateGoryEntity::getCatalogueId, ids);
            List<CateGoryEntity> allCategories = cateGoryMapper.selectList(lqw);
            List<String> categoryIds = allCategories.stream().map(CateGoryEntity::getId).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(categoryIds)){
                cateGoryMapper.deleteBatchIds(categoryIds);
            }

            //删除属性
            LambdaQueryWrapper<CatalogueAttributeEntity> caLqw = new LambdaQueryWrapper<>();
            caLqw.in(CatalogueAttributeEntity::getCatalogueId, ids);
            catalogueAttributeService.remove(caLqw);

            return this.removeByIds(ids);
        }

        return false;
    }


    @Override
    public String deleteTestCatalogue(List<String> ids) {

        StringBuilder sb = new StringBuilder();

        if (CollectionUtil.isNotEmpty(ids)) {
            List<CatalogueEntity> catalogueEntities = this.listByIds(ids);

            //查询目录下所有的类目
            LambdaQueryWrapper<CateGoryEntity> lqw = new LambdaQueryWrapper<>();
            lqw.in(CateGoryEntity::getCatalogueId, ids);
            List<CateGoryEntity> cateGoryEntities = cateGoryMapper.selectList(lqw);
            Map<String, List<CateGoryEntity>> cateGoryByCatalogueIdMap = cateGoryEntities.stream().collect(Collectors.groupingBy(CateGoryEntity::getCatalogueId));

            for (CatalogueEntity catalogueEntity : catalogueEntities) {
                //查看当前目录下是否有资产，如果有则不能删除

                List<CateGoryEntity> allCategories = cateGoryByCatalogueIdMap.get(catalogueEntity.getId());
                if (CollectionUtil.isNotEmpty(allCategories)) {
                    List<String> categoryIds = allCategories.stream().map(CateGoryEntity::getId).collect(Collectors.toList());

                    //查看类目下的是否存在资产，存在资产不删除
                    LambdaQueryWrapper<AssetAttributeSetEntity> aasLqw = new LambdaQueryWrapper<>();
                    aasLqw.in(AssetAttributeSetEntity::getCateGoryId, categoryIds);
                    long count = assetAttributeSetMapper.selectCount(aasLqw);
                    if (count > 0) {
                        sb.append(catalogueEntity.getDataPropertyName()).append("、");
                    }
                }

            }
        }

        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }

        return sb.toString();
    }

    @Override
    public boolean updateCatalogue(CatalogueEntity catalogueEntity) {

        LambdaUpdateWrapper<CateGoryEntity> luw = new LambdaUpdateWrapper<>();
        luw.eq(CateGoryEntity::getId, catalogueEntity.getCateGoryId());
        luw.set(CateGoryEntity::getCateGoryName, catalogueEntity.getCateGoryName());
        luw.set(CateGoryEntity::getDescription, catalogueEntity.getDescription());
        luw.set(CateGoryEntity::getUpdateBy, getUserName());
        cateGoryMapper.update(null, luw);

        catalogueEntity.setUpdateBy(getUserName());
        return updateById(catalogueEntity);
    }

    @Override
    public List<CatalogueEntity> queryEditCatalogueList() {
        LambdaQueryWrapper<CatalogueEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(CatalogueEntity::getEdit, "1");
        return this.list(lqw);
    }

    @Override
    public List<CatalogueEntity> queryOpennessCatalogueList() {
        LambdaQueryWrapper<CatalogueEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(CatalogueEntity::getOpenness, "1");
        return this.list(lqw);
    }
}
