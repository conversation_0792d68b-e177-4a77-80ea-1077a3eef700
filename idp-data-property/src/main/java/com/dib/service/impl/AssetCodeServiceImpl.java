package com.dib.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.domin.dto.AssetCodeDto;
import com.dib.domin.entity.AssetCodeEntity;
import com.dib.domin.entity.CateGoryEntity;
import com.dib.domin.enums.AssetCodeType;
import com.dib.mapper.AssetCodeMapper;
import com.dib.mapper.CateGoryMapper;
import com.dib.service.AssetCodeService;
import com.dib.utils.AssetCodeUtil;
import io.jsonwebtoken.lang.Collections;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.dib.common.utils.SecurityUtils.getUsername;

@Service
public class AssetCodeServiceImpl extends ServiceImpl<AssetCodeMapper, AssetCodeEntity> implements AssetCodeService {
    @Autowired
    private CateGoryMapper cateGoryMapper;


    @Override
    public boolean createAssetCode(List<AssetCodeEntity> assetCodeEntities) {

        String startRange = "";
        String catalogueId = "";

        List<AssetCodeEntity> newAssetCodeEntities = new ArrayList<>();
        for (AssetCodeEntity assetCodeEntity : assetCodeEntities) {
            //以存在数据，不新增
            if (assetCodeEntity.getId() != null && assetCodeEntity.getId().length() > 8) {
                continue;
            }
            assetCodeEntity.setId(null);

            if (!assetCodeEntity.getType().equals(AssetCodeType.SEPARATE.getCode())) {

                if (assetCodeEntity.getRule() == null || assetCodeEntity.getCodeDigit() == null || StringUtils.isAnyEmpty(assetCodeEntity.getLeftRange(), assetCodeEntity.getRightRange())) {
                    continue;
                }
                AssetCodeUtil.validateInput(assetCodeEntity);
            } else {
                assetCodeEntity.setCodeDigit(1);
            }
            assetCodeEntity.setCreateBy(getUsername());
            assetCodeEntity.setUpdateBy(getUsername());
            assetCodeEntity.setUpdateTime(new Date());

            if (assetCodeEntity.getType().equals(AssetCodeType.CATALOGUE.getCode()) &&  assetCodeEntity.getLevel() == 1){
                startRange = assetCodeEntity.getLeftRange();
                catalogueId = assetCodeEntity.getCatalogueId();
            }

            newAssetCodeEntities.add(assetCodeEntity);
        }

        boolean b = this.saveBatch(newAssetCodeEntities);
        //更新创建时的一层类目
//        if (b && !StringUtils.isEmpty(startRange)) {
//            LambdaUpdateWrapper<CateGoryEntity> luw = new LambdaUpdateWrapper<>();
//            luw.eq(CateGoryEntity::getCatalogueId, catalogueId);
//            luw.set(CateGoryEntity::getAssetCode, startRange);
//            cateGoryMapper.update(null, luw);
//        }

        return b;
    }

    @Override
    public List<AssetCodeDto> getAssetCodeByCatalogueId(String catalogueId) {
        LambdaQueryWrapper<AssetCodeEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(AssetCodeEntity::getCatalogueId, catalogueId)
                .orderByAsc(AssetCodeEntity::getLevel);

        List<AssetCodeEntity> assetCodeEntities = this.list(lqw);

        List<AssetCodeDto> assetCodeDtoList = new ArrayList<>();
        if (!Collections.isEmpty(assetCodeEntities)) {
            for (AssetCodeEntity assetCodeEntity : assetCodeEntities) {
                AssetCodeDto assetCodeDto = new AssetCodeDto();
                BeanUtils.copyProperties(assetCodeEntity, assetCodeDto);
                assetCodeDto.setAssetModifyType(true);
                assetCodeDtoList.add(assetCodeDto);
            }
        }

        return assetCodeDtoList;
    }

//    @Override
//    public boolean updateAssetCode(AssetCodeEntity assetCodeEntity) {
//        AssetCodeUtil.validateInput(assetCodeEntity);
//
//        return "";
//    }

//    @Override
//    public boolean deleteAssetCode(AssetCodeEntity assetCodeEntity) {
//        LambdaQueryWrapper<CateGoryEntity> lqw = new LambdaQueryWrapper<>();
//        lqw.eq(CateGoryEntity::getLevel,assetCodeEntity.getLevel())
//                .eq(CateGoryEntity::getCatalogueId,assetCodeEntity.getCatalogueId());
//        Integer i = cateGoryMapper.selectCount(lqw);
//        //存在使用当前编码类目
//        if (i != 0){
//            throw new RuntimeException("当前编码已有类目存在，不可删除");
//        }
//        return this.removeById(assetCodeEntity.getId());
//    }

    @Override
    public String getPreviewCode(List<AssetCodeEntity> assetCodeEntities) {
        StringBuilder sb = new StringBuilder();
        if (CollectionUtil.isNotEmpty(assetCodeEntities)){

            for (AssetCodeEntity assetCodeEntity : assetCodeEntities) {
                if (!assetCodeEntity.getType().equals(AssetCodeType.SEPARATE.getCode())) {
                    if (assetCodeEntity.getRule() == null || assetCodeEntity.getCodeDigit() == null || StringUtils.isAnyEmpty(assetCodeEntity.getLeftRange(), assetCodeEntity.getRightRange())) {
                        continue;
                    }
                    AssetCodeUtil.validateInput(assetCodeEntity);
                } else {
                    assetCodeEntity.setCodeDigit(1);
                }
            }

            assetCodeEntities = assetCodeEntities.stream().sorted(Comparator.comparingInt(AssetCodeEntity::getLevel)).sorted(Comparator.comparingInt(AssetCodeEntity::getType)).collect(Collectors.toList());
            assetCodeEntities.forEach(
                    assetCodeEntity -> {
                        sb.append(assetCodeEntity.getLeftRange());
                    }
            );
        }

        return sb.toString();
    }
}
