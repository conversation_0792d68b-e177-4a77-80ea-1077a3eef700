package com.dib.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.aspose.words.net.System.Data.DataException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.domin.dto.*;
import com.dib.domin.entity.*;
import com.dib.domin.query.AssetGateWayQuery;
import com.dib.mapper.AssetAttributeSetMapper;
import com.dib.mapper.AssetGateWayMapper;
import com.dib.service.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.dib.common.database.utils.SecurityUtil.getUserName;

@Service
public class AssetGateWayServiceImpl extends ServiceImpl<AssetGateWayMapper, AssetGateWayEntity> implements AssetGateWayService {

    @Autowired
    private CatalogueService catalogueService;
    @Autowired
    private CateGoryService cateGoryService;
    @Autowired
    private CatalogueAttributeService catalogueAttributeService;
    @Autowired
    private AssetAttributeSetService attributeSetService;
    @Autowired
    private AssetAttributeService assetAttributeService;

    @Override
    public PageInfo<AssetGateWayInfoDto> getAssetGateWay(AssetGateWayQuery assetGateWayQuery) {
        PageInfo<AssetGateWayInfoDto> result = new PageInfo<>();

        LambdaQueryWrapper<AssetGateWayEntity> lqw = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(assetGateWayQuery.getAssetGateWayName())) {
            lqw.like(AssetGateWayEntity::getAssetGateWayName, assetGateWayQuery.getAssetGateWayName());
        }
        PageHelper.startPage(assetGateWayQuery.getPageNum(), assetGateWayQuery.getPageSize());
        List<AssetGateWayEntity> list = this.list(lqw);
        PageInfo<AssetGateWayEntity> pageInfo = new PageInfo<>(list);
        if (!pageInfo.getList().isEmpty()) {
            Set<String> catalogueIds = pageInfo.getList().stream().map(AssetGateWayEntity::getCatalogueId).collect(Collectors.toSet());
            LambdaQueryWrapper<CatalogueEntity> catalogueEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
            catalogueEntityLambdaQueryWrapper.in(CatalogueEntity::getId, catalogueIds);
            List<CatalogueEntity> catalogueEntities = catalogueService.list(catalogueEntityLambdaQueryWrapper);

            Map<String, CatalogueEntity> catalogueEntityMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(catalogueEntities)) {
                catalogueEntityMap = catalogueEntities.stream().collect(Collectors.toMap(CatalogueEntity::getId, catalogueEntity -> catalogueEntity));
            }


            BeanUtils.copyProperties(pageInfo, result, "list");

            List<AssetGateWayInfoDto> assetGateWayInfoDtoList = new ArrayList<>();
            for (AssetGateWayEntity assetGateWayEntity : pageInfo.getList()) {
                AssetGateWayInfoDto assetGateWayInfoDto = new AssetGateWayInfoDto(assetGateWayEntity);
                if (catalogueEntityMap != null && catalogueEntityMap.containsKey(assetGateWayEntity.getCatalogueId())) {
                    assetGateWayInfoDto.setCatalogueName(catalogueEntityMap.get(assetGateWayEntity.getCatalogueId()).getDataPropertyName());
                }
                assetGateWayInfoDtoList.add(assetGateWayInfoDto);
            }

            result.setList(assetGateWayInfoDtoList);
        }

        return result;
    }

    @Override
    public boolean saveAssetAttribute(AssetGateWayEntity assetGateWayEntity) {
        assetGateWayEntity.setCreateBy(getUserName());
        assetGateWayEntity.setUpdateBy(getUserName());
        assetGateWayEntity.setUpdateTime(new Date());
        return this.save(assetGateWayEntity);
    }

    @Override
    public AssetGateWayCatalogueDto getAssetGateWayById(String id) {
        AssetGateWayCatalogueDto assetGateWayCatalogueDto = new AssetGateWayCatalogueDto();

        List<AssetGateWayAttributeValueDto> assetGateWayAttributeValueDtoList = new ArrayList<>();

        AssetGateWayEntity assetGateWayEntity = getById(id);
        if (Objects.isNull(assetGateWayEntity) || CollectionUtil.isEmpty(assetGateWayEntity.getCatalogueAttrIds())) {
            return null;
        }
        String catalogueId = assetGateWayEntity.getCatalogueId();

        //查询目录下属性值，用于去除已禁用的属性值
        List<CatalogueAttributeDto> catalogueAttributeDtoList = catalogueAttributeService.getAssetCatalogueData(catalogueId);

        if (CollectionUtil.isEmpty(catalogueAttributeDtoList)) {
            throw new DataException("目录下没有属性值");
        }

        //分类id
        List<String> catalogueAttrIds = assetGateWayEntity.getCatalogueAttrIds();
        List<String> oldCatalogueAttrIds = catalogueAttributeDtoList.stream().map(CatalogueAttributeDto::getId).collect(Collectors.toList());

        catalogueAttrIds = catalogueAttrIds.stream().filter(oldCatalogueAttrIds::contains).collect(Collectors.toList());


        //查询目录下所有上架的资产
        LambdaQueryWrapper<AssetAttributeSetEntity> aasLqw = new LambdaQueryWrapper<>();
        aasLqw.eq(AssetAttributeSetEntity::getCatalogueId, catalogueId);
        List<AssetAttributeSetEntity> assetAttributeSetEntities = attributeSetService.list(aasLqw);
        if (CollectionUtil.isNotEmpty(assetAttributeSetEntities)) {
            assetGateWayCatalogueDto.setAssetNumber(assetAttributeSetEntities.size());

            //查询资产下的属性值
            List<String> assetAttributeSetIds = assetAttributeSetEntities.stream().map(AssetAttributeSetEntity::getId).collect(Collectors.toList());
            LambdaQueryWrapper<AssetAttributeEntity> aaLqw = new LambdaQueryWrapper<>();
            aaLqw.in(AssetAttributeEntity::getAssetAttributeSetId, assetAttributeSetIds);
            List<AssetAttributeEntity> assetAttributeEntities = assetAttributeService.list(aaLqw);

            if (CollectionUtil.isNotEmpty(assetAttributeEntities)) {
                //根据目录属性分组，构建结果
                List<String> finalCatalogueAttrIds = catalogueAttrIds;
                Map<String, List<AssetAttributeEntity>> assetAttributeDataByCatalogueAttrIdMap = assetAttributeEntities.stream().filter(assetAttributeEntity -> finalCatalogueAttrIds.contains(assetAttributeEntity.getCatalogueAttrId())).collect(Collectors.groupingBy(AssetAttributeEntity::getCatalogueAttrId));

                if (MapUtil.isNotEmpty(assetAttributeDataByCatalogueAttrIdMap)) {

                    for (String catalogueAttrId : assetAttributeDataByCatalogueAttrIdMap.keySet()) {
                        List<AssetAttributeEntity> tmpAssetAttributeEntities = assetAttributeDataByCatalogueAttrIdMap.get(catalogueAttrId);
                        AssetGateWayAttributeValueDto assetGateWayAttributeValueDto = new AssetGateWayAttributeValueDto();
                        assetGateWayAttributeValueDto.setAttributeDataId(catalogueAttrId);
                        assetGateWayAttributeValueDto.setAttributeName(tmpAssetAttributeEntities.get(0).getAttributeName());

                        Map<String, List<String>> attributeValueAndCount = tmpAssetAttributeEntities.stream()
                                .filter(entity -> entity.getAttributeValue() != null && !entity.getAttributeValue().isEmpty())
                                .collect(Collectors.groupingBy(
                                        entity -> entity.getAttributeValue().get(0), // 使用 attributeValue 列表的第一个元素作为分组键
                                        Collectors.mapping(AssetAttributeEntity::getAssetAttributeSetId, Collectors.toList()) // 收集每个分组中的 assetAttributeSetId 为列表
                                ));

                        assetGateWayAttributeValueDto.setAttributeValueAndAssetAttributeSetId(attributeValueAndCount);
                        assetGateWayAttributeValueDtoList.add(assetGateWayAttributeValueDto);
                    }
                }
            }
        }

        //查询目录下类目分类
        List<CateGoryDto> cateGoryTreeById = cateGoryService.getCateGoryTreeById(catalogueId);

        assetGateWayCatalogueDto.setCateGoryTree(cateGoryTreeById);
        assetGateWayCatalogueDto.setAssetGateWayAttributeValueDtoList(assetGateWayAttributeValueDtoList);


        return assetGateWayCatalogueDto;
    }


    @Override
    public List<AssetAttributeSetDto> getAssetGateWayAttributeInfo(AssetGateWayQuery assetGateWayQuery) {

        List<AssetAttributeSetDto> result = new ArrayList<>();

        AssetGateWayEntity assetGateWayEntity = getById(assetGateWayQuery.getId());
        if (Objects.isNull(assetGateWayEntity)) {
            return null;
        }
        List<String> displayCatalogueAttrIds = assetGateWayEntity.getDisplayCatalogueAttrIds();

        List<AssetAttributeSetEntity> assetAttributeSetEntities = new ArrayList<>();

        //查询对应目录下所有资产
        LambdaQueryWrapper<AssetAttributeSetEntity> aasLqw = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(assetGateWayQuery.getQueryIds())) {
            List<String> split = List.of(assetGateWayQuery.getQueryIds().split(","));
            aasLqw.in(AssetAttributeSetEntity::getCateGoryId, split).or().in(AssetAttributeSetEntity::getId, split);
            assetAttributeSetEntities = attributeSetService.list(aasLqw);
        }
        if (CollectionUtil.isNotEmpty(assetAttributeSetEntities)) {
            List<AssetAttributeSetDto> assetAttributeSetDtoList = new ArrayList<>();
            attributeSetService.queryAttribute(assetAttributeSetEntities, assetAttributeSetDtoList);

            if (CollectionUtil.isNotEmpty(assetAttributeSetDtoList)) {
                List<String> labelDataIds = assetGateWayQuery.getLabelDataIds();

                label:
                for (AssetAttributeSetDto assetAttributeSetDto : assetAttributeSetDtoList) {


                    //标签
                    if (labelDataIds != null) {
                        for (String labelDataId : labelDataIds) {
                            if (!assetAttributeSetDto.getLabelDataIds().contains(labelDataId)) {
                                continue label;
                            }
                        }
                    }

                    List<AssetAttributeEntity> assetAttributeEntities = assetAttributeSetDto.getAssetAttributeEntities();

                    if (CollectionUtil.isNotEmpty(assetAttributeEntities)){

                        String attributeSetName = assetAttributeEntities.stream().filter(assetAttributeEntity -> assetAttributeEntity.getAttributeValue() != null && assetAttributeEntity.getAttributeCode().equals("SJZCMC"))
                                .map(assetAttributeEntity -> assetAttributeEntity.getAttributeValue().get(0)).findFirst()
                                .orElse(null);
                        assetAttributeSetDto.setAttributeSetName(attributeSetName);


                        assetAttributeEntities = assetAttributeEntities.stream().filter(assetAttributeEntity -> displayCatalogueAttrIds.contains(assetAttributeEntity.getCatalogueAttrId())).collect(Collectors.toList());

                        //搜索值
                        String searchAttributeValue = assetGateWayQuery.getSearchAttributeValue();

                        if (StringUtils.isNotEmpty(searchAttributeValue)) {
                            List<String> attributeValues = assetAttributeEntities.stream().map(AssetAttributeEntity::getAttributeValue).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
                            if (!CollectionUtil.isNotEmpty(attributeValues) && !attributeValues.contains(searchAttributeValue)) {
                                continue label;
                            }
                        }
                        assetAttributeSetDto.setAssetAttributeEntities(assetAttributeEntities);
                    }

                    result.add(assetAttributeSetDto);
                }
            }


            if (assetGateWayQuery.getQuerySoft() != null){
                //根据更新时间排序
                if (assetGateWayQuery.getQuerySoft().contains("updTimeAsc")) {
                    result = result.stream().sorted(Comparator.comparing(AssetAttributeSetDto::getUpdateTime)).collect(Collectors.toList());
                } else if(assetGateWayQuery.getQuerySoft().contains("updTimeAsc")){
                    result = result.stream().sorted(Comparator.comparing(AssetAttributeSetDto::getUpdateTime).reversed()).collect(Collectors.toList());
                }

                //根据标题排序
                if (assetGateWayQuery.getQuerySoft().contains("titleAsc")) {
                    result = result.stream().sorted(Comparator.comparing(AssetAttributeSetDto::getAttributeSetName)).collect(Collectors.toList());
                } else if(assetGateWayQuery.getQuerySoft().contains("titleDesc")) {
                    result = result.stream().sorted(Comparator.comparing(AssetAttributeSetDto::getAttributeSetName).reversed()).collect(Collectors.toList());
                }
            }

        }


        return result;
    }

    @Override
    public boolean updateAssetGateWay(AssetGateWayEntity assetGateWayEntity) {
        assetGateWayEntity.setUpdateBy(getUserName());
        return this.updateById(assetGateWayEntity);
    }

    @Override
    public boolean deleteAssetGateWay(String id) {
        return this.removeById(id);
    }
}
