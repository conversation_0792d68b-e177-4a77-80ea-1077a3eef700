package com.dib.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.aspose.words.net.System.Data.DataException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.domin.dto.AssetAttributeAndSetDto;
import com.dib.domin.dto.AssetAttributeDto;
import com.dib.domin.dto.AssetAttributeSetAndDataDto;
import com.dib.domin.dto.AssetAttributeSetDto;
import com.dib.domin.entity.AssetAttributeEntity;
import com.dib.domin.entity.AssetAttributeSetEntity;
import com.dib.domin.entity.AttributeDataEntity;
import com.dib.domin.query.AssetAttributeSetQuery;
import com.dib.mapper.AssetAttributeSetMapper;
import com.dib.market.entity.DataApiEntity;
import com.dib.market.mapper.DataApiDao;
import com.dib.service.AssetAttributeService;
import com.dib.service.AssetAttributeSetService;
import com.dib.service.AttributeSetService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.dib.common.utils.SecurityUtils.getUsername;

@Service
@Transactional
public class AssetAttributeSetServiceImpl extends ServiceImpl<AssetAttributeSetMapper, AssetAttributeSetEntity> implements AssetAttributeSetService {

    @Autowired
    private AssetAttributeService assetAttributeService;
    @Autowired
    private AttributeSetService attributeSetService;
    @Autowired
    private DataApiDao dataApiDao;

    @Override
    public boolean createAssetAttributeSet(AssetAttributeAndSetDto assetAttributeSetDto) {
        AssetAttributeSetEntity assetAttributeSetEntity = new AssetAttributeSetEntity();
        assetAttributeSetEntity.setId(assetAttributeSetDto.getId());
        assetAttributeSetEntity.setCatalogueId(assetAttributeSetDto.getCatalogueId());
        assetAttributeSetEntity.setCateGoryId(assetAttributeSetDto.getCateGoryId());
        assetAttributeSetEntity.setMountInfo(assetAttributeSetDto.getMountInfo());
        if (StringUtils.isBlank(assetAttributeSetDto.getId())) {
            assetAttributeSetEntity.setIsRelease("0");
            assetAttributeSetEntity.setIsArrive("0");
            assetAttributeSetEntity.setCreateBy(getUsername());
        }

        assetAttributeSetEntity.setUpdateBy(getUsername());
        assetAttributeSetEntity.setUpdateTime(new Date());
        boolean save = this.saveOrUpdate(assetAttributeSetEntity);

        assetAttributeService.createAndUpdateAssetAttribute(assetAttributeSetDto.getAssetAttributeDtoMap(), assetAttributeSetEntity.getId());

        return save;
    }

    @Override
    public PageInfo<AssetAttributeSetDto> getAssetAttributeSetByCategoryId(AssetAttributeSetQuery assetAttributeSetQuery) {
        List<AssetAttributeSetDto> assetAttributeSetDtoList = new ArrayList<>();

        LambdaQueryWrapper<AssetAttributeSetEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(AssetAttributeSetEntity::getCateGoryId, assetAttributeSetQuery.getCateGoryId());
        if (assetAttributeSetQuery.getIsArrive() != null) {
            lqw.eq(AssetAttributeSetEntity::getIsArrive, assetAttributeSetQuery.getIsArrive());
        }
        if (assetAttributeSetQuery.getIsRelease() != null) {
            lqw.eq(AssetAttributeSetEntity::getIsRelease, assetAttributeSetQuery.getIsRelease());
        }

        PageInfo<AssetAttributeSetEntity> pageInfo = new PageInfo<>();

        String assetNameAndAssetCode = assetAttributeSetQuery.getAssetNameAndAssetCode();
        if (StringUtils.isNotBlank(assetNameAndAssetCode)) {
            List<AssetAttributeSetEntity> assetAttributeSetEntities = this.list(lqw);

            if (CollectionUtil.isNotEmpty(assetAttributeSetEntities)) {
                List<String> asserAttributeIds = assetAttributeSetEntities.stream().map(AssetAttributeSetEntity::getId).collect(Collectors.toList());

                if (StringUtils.isNotBlank(assetAttributeSetQuery.getAssetNameAndAssetCode())) {
                    LambdaQueryWrapper<AssetAttributeEntity> aaLqw = new LambdaQueryWrapper<>();
                    aaLqw.in(AssetAttributeEntity::getAssetAttributeSetId, asserAttributeIds);
                    aaLqw.like(AssetAttributeEntity::getAttributeValue, assetNameAndAssetCode);
                    aaLqw.and(aaLqw1 -> aaLqw1.like(AssetAttributeEntity::getAttributeCode, "SJZCMC").or().like(AssetAttributeEntity::getAttributeCode, "SJZCDM"));

                    //查询基础属性
                    PageHelper.startPage(assetAttributeSetQuery.getPageNum(), assetAttributeSetQuery.getPageSize());
                    List<AssetAttributeEntity> assetAttributeEntities = assetAttributeService.list(aaLqw);
                    PageInfo<AssetAttributeEntity> assetAttributeEntityPageInfo = new PageInfo<>(assetAttributeEntities);
                    List<AssetAttributeEntity> list = assetAttributeEntityPageInfo.getList();

                    BeanUtils.copyProperties(pageInfo, assetAttributeSetDtoList, "list");

                    if (CollectionUtil.isNotEmpty(list)) {
                        List<String> assetAttributeSetIds = list.stream().map(AssetAttributeEntity::getAssetAttributeSetId).collect(Collectors.toList());
                        List<AssetAttributeSetEntity> newAssetAttributeSetEntities = assetAttributeSetEntities.stream().filter(assetAttributeSetEntity -> assetAttributeSetIds.contains(assetAttributeSetEntity.getId())).collect(Collectors.toList());
                        pageInfo.setList(newAssetAttributeSetEntities);
                    }
                }
            }
        } else {
            PageHelper.startPage(assetAttributeSetQuery.getPageNum(), assetAttributeSetQuery.getPageSize());
            List<AssetAttributeSetEntity> assetAttributeSetEntities = this.list(lqw);
            pageInfo = new PageInfo<>(assetAttributeSetEntities);
        }


        //查询基础属性、标签
        queryAttribute(pageInfo.getList(), assetAttributeSetDtoList);

        PageInfo<AssetAttributeSetDto> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);
        result.setList(assetAttributeSetDtoList);

        return result;
    }

    /**
     * 查询属性集下属性
     *
     * @param assetAttributeSetEntities
     * @param assetAttributeSetDtoList
     */
    @Override
    public void queryAttribute(List<AssetAttributeSetEntity> assetAttributeSetEntities, List<AssetAttributeSetDto> assetAttributeSetDtoList) {

        //查询对应目录属性集下所有属性
        List<AttributeDataEntity> attributeDataEntities = attributeSetService.getCatalogueAttributeSetTree();


        List<String> attributeCodes = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(attributeDataEntities)) {
            attributeCodes = attributeDataEntities.stream().map(AttributeDataEntity::getAttributeCode).collect(Collectors.toList());
        }

        if (CollectionUtil.isNotEmpty(assetAttributeSetEntities)) {
            List<String> assetAttributeSetIds = assetAttributeSetEntities.stream().map(AssetAttributeSetEntity::getId).collect(Collectors.toList());
            LambdaQueryWrapper<AssetAttributeEntity> aaLqw = new LambdaQueryWrapper<>();
            aaLqw.in(AssetAttributeEntity::getAssetAttributeSetId, assetAttributeSetIds);
            aaLqw.in(AssetAttributeEntity::getAttributeCode, attributeCodes);
            List<AssetAttributeEntity> assetAttributeEntities = assetAttributeService.list(aaLqw);


            Map<String, List<AssetAttributeEntity>> assetAttributeBySetIdMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(assetAttributeEntities)) {
                assetAttributeBySetIdMap = assetAttributeEntities.stream().collect(Collectors.groupingBy(AssetAttributeEntity::getAssetAttributeSetId));
            }

            for (AssetAttributeSetEntity attributeSetEntity : assetAttributeSetEntities) {
                AssetAttributeSetDto assetAttributeSetDto = new AssetAttributeSetDto();
                BeanUtils.copyProperties(attributeSetEntity, assetAttributeSetDto);

                List<AssetAttributeEntity> tmpAssetAttributeEntities = assetAttributeBySetIdMap.get(attributeSetEntity.getId());

                assetAttributeSetDto.setAssetAttributeEntities(tmpAssetAttributeEntities);
                assetAttributeSetDtoList.add(assetAttributeSetDto);
            }
        }
    }

    @Override
    public boolean updateMountAssetAttributeData(AssetAttributeSetAndDataDto assetAttributeSetAndDataDto) {
        AssetAttributeSetEntity assetAttributeSetEntity = new AssetAttributeSetEntity();
        assetAttributeSetEntity.setId(assetAttributeSetAndDataDto.getId());
        assetAttributeSetEntity.setMountInfo(assetAttributeSetAndDataDto.getMountInfo());

        boolean update = this.updateById(assetAttributeSetEntity);

        List<AssetAttributeDto> assetAttributeDtoList = assetAttributeSetAndDataDto.getAssetAttributeDtoList();
        if (CollectionUtil.isNotEmpty(assetAttributeDtoList)){
            List<AssetAttributeEntity> assetAttributeEntities = new ArrayList<>();
            for (AssetAttributeDto assetAttributeDto : assetAttributeDtoList) {
                AssetAttributeEntity assetAttributeEntity = new AssetAttributeEntity();
                assetAttributeEntity.setAttributeValue(assetAttributeDto.getAttributeValue());
                assetAttributeEntity.setId(assetAttributeDto.getId());
                assetAttributeEntities.add(assetAttributeEntity);
            }
            assetAttributeService.updateBatchById(assetAttributeEntities);
        }


        return update;
    }

    @Override
    public boolean updateAssetAttributeSet(AssetAttributeSetDto assetAttributeSetDto) {
        AssetAttributeSetEntity assetAttributeSetEntity = new AssetAttributeSetEntity();
        BeanUtils.copyProperties(assetAttributeSetDto, assetAttributeSetEntity);
        assetAttributeSetEntity.setUpdateBy(getUsername());

        return this.updateById(assetAttributeSetEntity);
    }

    @Override
    public boolean deleteAssetAttributeSet(String id) {
        //查询当前资产下没有api
        AssetAttributeSetEntity assetAttributeSetEntity = getById(id);

        if (StringUtils.isNotBlank(assetAttributeSetEntity.getDataApiId())) {
            throw new DataException("当前属性集下存在数据接口，无法删除");
        }

        //删除属性、标签
        LambdaQueryWrapper<AssetAttributeEntity> aaLqw = new LambdaQueryWrapper<>();
        aaLqw.eq(AssetAttributeEntity::getAssetAttributeSetId, id);
        assetAttributeService.remove(aaLqw);


        return this.removeById(id);
    }
}
