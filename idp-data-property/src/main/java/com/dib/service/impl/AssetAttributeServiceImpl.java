package com.dib.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.dto.AssetAttributeDto;
import com.dib.domin.dto.AssetAttributeSetAndDataDto;
import com.dib.domin.dto.CatalogueAttributeDto;
import com.dib.domin.entity.*;
import com.dib.domin.enums.AssetCodeType;
import com.dib.domin.enums.AttributeDataType;
import com.dib.domin.enums.MountTypeEnum;
import com.dib.mapper.AssetAttributeMapper;
import com.dib.mapper.AssetAttributeSetMapper;
import com.dib.mapper.AssetCodeMapper;
import com.dib.market.service.DataApiService;
import com.dib.metadata.entity.MetadataColumnEntity;
import com.dib.metadata.service.MetadataColumnService;
import com.dib.metadata.service.SqlConsoleService;
import com.dib.service.*;
import com.dib.utils.AssetCodeUtil;
import org.apache.commons.lang3.StringUtils;
import org.datanucleus.store.rdbms.exceptions.NullValueException;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

import static com.dib.common.utils.SecurityUtils.getUsername;

@Service
public class AssetAttributeServiceImpl extends ServiceImpl<AssetAttributeMapper, AssetAttributeEntity> implements AssetAttributeService {

    @Autowired
    private CateGoryService cateGoryService;
    @Autowired
    private AssetCodeMapper assetCodeMapper;
    @Autowired
    private AttributeSetService attributeSetService;
    @Autowired
    private CatalogueAttributeService catalogueAttributeService;
    @Autowired
    private AssetAttributeSetMapper assetAttributeSetMapper;
    @Autowired
    private DataApiService dataApiService;
    @Autowired
    private MetadataColumnService metadataColumnService;


    //    @Autowired
//    private IndexManageClient indexManageClient;
    @Autowired
    private SqlConsoleService sqlConsoleService;

    @Override
    public Map<String, List<AssetAttributeDto>> queryAssetAttributeData(AssetAttributeSetEntity assetAttributeSetEntity) {
        Map<String, List<AssetAttributeDto>> attributeSetNameToDtoMap = queryAssetAttributeAll(assetAttributeSetEntity);

        if (MapUtil.isNotEmpty(attributeSetNameToDtoMap) && "0".equals(assetAttributeSetEntity.getMountType()) && assetAttributeSetEntity.getMountInfo() != null) {
            attributeSetNameToDtoMap.remove("信息项属性");
        }

        return attributeSetNameToDtoMap;
    }

    private @NotNull Map<String, List<AssetAttributeDto>> queryAssetAttributeAll(AssetAttributeSetEntity assetAttributeSetEntity) {
        Map<String, List<AssetAttributeDto>> attributeSetNameToDtoMap = new LinkedHashMap<>();

        String catalogueId = assetAttributeSetEntity.getCatalogueId();
        String assetCode = "";
        String cateGoryName = "";

        Map<String, AssetAttributeEntity> assetAttributeEntityMap = new HashMap<>();
        if (StringUtils.isNotBlank(assetAttributeSetEntity.getId())) {
            LambdaQueryWrapper<AssetAttributeEntity> lqw = new LambdaQueryWrapper<>();
            lqw.eq(AssetAttributeEntity::getAssetAttributeSetId, assetAttributeSetEntity.getId());
            List<AssetAttributeEntity> assetAttributeEntities = this.list(lqw);
            if (CollectionUtil.isNotEmpty(assetAttributeEntities)) {
                assetAttributeEntityMap = assetAttributeEntities.stream().collect(Collectors.toMap(AssetAttributeEntity::getCatalogueAttrId, assetAttributeEntity -> assetAttributeEntity));
            }
        } else {
            LambdaQueryWrapper<AssetCodeEntity> acLqw = new LambdaQueryWrapper<>();
            acLqw.eq(AssetCodeEntity::getCatalogueId, catalogueId)
                    .eq(AssetCodeEntity::getType, AssetCodeType.ASSET.getCode());
            AssetCodeEntity assetCodeEntity = assetCodeMapper.selectOne(acLqw);
            //查询同当前目录同层编码规则
            if (Objects.isNull(assetCodeEntity)) {
                throw new NullValueException("当前层级无资产编码规则存在，请先补充规则");
            } else {

                assetCode = cateGoryService.getAssetCodePath(assetAttributeSetEntity.getCateGoryId());
                //查询统一父级下的数据，根据编码生成规则进行填充
                LambdaQueryWrapper<AssetAttributeSetEntity> countLqw = new LambdaQueryWrapper<>();
                countLqw.eq(AssetAttributeSetEntity::getCatalogueId, catalogueId);
                countLqw.eq(AssetAttributeSetEntity::getCateGoryId, assetAttributeSetEntity.getCateGoryId());
                int count = Math.toIntExact(assetAttributeSetMapper.selectCount(countLqw));

                String newAssetCode = AssetCodeUtil.getAssetCode(assetCodeEntity, count);
                assetCode = assetCode + "/" + newAssetCode;
            }

            //查询类目名称
            LambdaQueryWrapper<CateGoryEntity> cateGoryEntityLambdaQueryWrapper = new LambdaQueryWrapper<>();
            cateGoryEntityLambdaQueryWrapper.eq(CateGoryEntity::getCatalogueId, catalogueId).eq(CateGoryEntity::getId, assetAttributeSetEntity.getCateGoryId());
            CateGoryEntity cateGoryEntity = cateGoryService.getOne(cateGoryEntityLambdaQueryWrapper);
            cateGoryName = cateGoryEntity.getCateGoryName();
        }

        List<AssetAttributeDto> assetAttributeDtoList = getAssetAttributeDtoList(catalogueId, assetAttributeEntityMap, assetCode, cateGoryName);

        if (CollectionUtil.isNotEmpty(assetAttributeDtoList)) {
            Map<String, List<AssetAttributeDto>> assetAttributeDtoMap = assetAttributeDtoList.stream().collect(Collectors.groupingBy(AssetAttributeDto::getAttributeSetId));

            //属性集
            List<AttributeSetEntity> attributeSetEntities = attributeSetService.listByIds(assetAttributeDtoMap.keySet());
            if (CollectionUtil.isNotEmpty(attributeSetEntities)) {
                attributeSetEntities = attributeSetEntities.stream()
                        .sorted(Comparator.comparing(AttributeSetEntity::getNumber))
                        .collect(Collectors.toList());

                // 如果对应的属性集不存在，则会丢失
                // 赋值到一个新的 map，键为 attributeSetName，值为对应的 AssetAttributeDto 列表
                for (AttributeSetEntity attributeSetEntity : attributeSetEntities) {
                    String attributeSetName = attributeSetEntity.getAttributeSetName();
                    String attributeSetId = attributeSetEntity.getId();
                    if (assetAttributeDtoMap.containsKey(attributeSetId)) {
                        List<AssetAttributeDto> attributeDtoList = assetAttributeDtoMap.get(attributeSetId).stream().sorted(Comparator.comparing(AssetAttributeDto::getNumber)).collect(Collectors.toList());
                        attributeSetNameToDtoMap.put(attributeSetName, attributeDtoList);
                    }
                }
            }
        }
        return attributeSetNameToDtoMap;
    }


    @Override
    public Map<String, List<AssetAttributeDto>> queryInfoAttribute(AssetAttributeSetEntity assetAttributeSetEntity) {

        Map<String, List<AssetAttributeDto>> attributeSetNameToDtoMap = queryAssetAttributeData(assetAttributeSetEntity);
        handelInfoAttribute(assetAttributeSetEntity, attributeSetNameToDtoMap);

//        if (MapUtil.isNotEmpty(attributeSetNameToDtoMap) && attributeSetNameToDtoMap.size() == 1){
//            return attributeSetNameToDtoMap.values().stream().findFirst().orElse(null);
//        }
        return attributeSetNameToDtoMap;
    }

    /**
     * 信息项属性
     *
     * @param assetAttributeSetEntity
     * @param attributeSetNameToDtoMap
     */
    private void handelInfoAttribute(AssetAttributeSetEntity assetAttributeSetEntity, Map<String, List<AssetAttributeDto>> attributeSetNameToDtoMap) {

        Set<String> validMountTypes = Set.of(MountTypeEnum.TABLE.getCode(), MountTypeEnum.MODEL.getCode(), MountTypeEnum.METRIC.getCode());
        if (MapUtil.isNotEmpty(attributeSetNameToDtoMap) && assetAttributeSetEntity.getMountInfo() != null && assetAttributeSetEntity.getMountType() != null &&
                (validMountTypes.contains(assetAttributeSetEntity.getMountType().getCode()))) {

            List<AssetAttributeDto> tmpAssetAttributeDtoList = attributeSetNameToDtoMap.get("信息项属性");

            if (CollectionUtil.isNotEmpty(tmpAssetAttributeDtoList)) {
                attributeSetNameToDtoMap.clear();

                MountInfo mountInfo = assetAttributeSetEntity.getMountInfo();
                if (StringUtils.isNotBlank(mountInfo.getSourceId()) && StringUtils.isNotBlank(mountInfo.getTableId())) {
                    LambdaQueryWrapper<MetadataColumnEntity> lqw = new LambdaQueryWrapper<>();
                    lqw.eq(MetadataColumnEntity::getSourceId, mountInfo.getSourceId());
                    lqw.eq(MetadataColumnEntity::getTableId, mountInfo.getTableId());

                    List<String> modelColumns = null;
                    String code = assetAttributeSetEntity.getMountType().getCode();
                    if (MountTypeEnum.MODEL.getCode().equals(code) && assetAttributeSetEntity.getMountInfo().getModelTableVo() != null){
                         modelColumns = assetAttributeSetEntity.getMountInfo().getModelTableVo().getModelColumns();
                    }
                    if (MountTypeEnum.METRIC.getCode().equals(code) && assetAttributeSetEntity.getMountInfo().getTDimBaseRelaColVo() != null){
                        modelColumns = assetAttributeSetEntity.getMountInfo().getTDimBaseRelaColVo().getDimColumnSettingVoList();
                    }

                    lqw.in(modelColumns != null, MetadataColumnEntity::getColumnName, modelColumns);
                    List<MetadataColumnEntity> metadataColumnEntities = metadataColumnService.list(lqw);
                    if (CollectionUtil.isNotEmpty(metadataColumnEntities)) {

                        for (AssetAttributeDto assetAttributeDto : tmpAssetAttributeDtoList) {
                            if (CollectionUtil.isNotEmpty(assetAttributeDto.getAttributeValue())) {
                                continue;
                            }
//                            NAME	信息项英文名称
//                            CAPTION	信息项中文名称
//                            LENGTH	数据长度
//                            TYPE	数据类型
//                            SCALE	数据精度
//                            DESC	信息项描述
//                            LINKDIM	关联维表
//                            ISSHOW	是否显示
//                            NULLABLE	是否允许为空
                            String attributeCode = assetAttributeDto.getAttributeCode();
                            if ("NAME".equals(attributeCode)) {
                                List<String> value = metadataColumnEntities.stream().map(MetadataColumnEntity::getColumnName).collect(Collectors.toList());
                                assetAttributeDto.setAttributeValue(value);
                            } else if ("CAPTION".equals(attributeCode)) {
                                List<String> value = metadataColumnEntities.stream().map(MetadataColumnEntity::getColumnComment).collect(Collectors.toList());
                                assetAttributeDto.setAttributeValue(value);
                            } else if ("LENGTH".equals(attributeCode)) {
                                List<String> value = metadataColumnEntities.stream().map(MetadataColumnEntity::getDataLength).collect(Collectors.toList());
                                assetAttributeDto.setAttributeValue(value);
                            } else if ("TYPE".equals(attributeCode)) {
                                List<String> value = metadataColumnEntities.stream().map(MetadataColumnEntity::getDataType).collect(Collectors.toList());
                                assetAttributeDto.setAttributeValue(value);
                            } else if ("SCALE".equals(attributeCode)) {
                                List<String> value = metadataColumnEntities.stream().map(MetadataColumnEntity::getDataPrecision).collect(Collectors.toList());
                                assetAttributeDto.setAttributeValue(value);
                            } else if ("NULLABLE".equals(attributeCode)) {
                                List<String> value = metadataColumnEntities.stream().map(MetadataColumnEntity::getColumnNullable).collect(Collectors.toList());
                                assetAttributeDto.setAttributeValue(value);
                            }
                        }
                    }
                }
                attributeSetNameToDtoMap.put("信息项属性", tmpAssetAttributeDtoList);
            }
        }
    }

    @Override
    public List<AssetAttributeDto> getAssetAttributeDtoList(String catalogueId, Map<String, AssetAttributeEntity> assetAttributeEntityMap, String assetCode, String cateGoryName) {
        List<AssetAttributeDto> assetAttributeDtoList = new ArrayList<>();

        List<CatalogueAttributeDto> catalogueAttributeDtoList = catalogueAttributeService.getAssetCatalogueData(catalogueId);

        if (CollectionUtil.isNotEmpty(catalogueAttributeDtoList)) {

            for (CatalogueAttributeDto catalogueAttributeDto : catalogueAttributeDtoList) {
                AssetAttributeDto assetAttributeDto = new AssetAttributeDto();

                BeanUtils.copyProperties(catalogueAttributeDto, assetAttributeDto, "id", "createBy", "updateBy", "updateTime", "createTime");


                if (Integer.parseInt(assetAttributeDto.getDataType()) != AttributeDataType.DIMENSION_TABLE.getCode() && StringUtils.isNotBlank(assetAttributeDto.getDataDefault())) {
                    assetAttributeDto.setAttributeValue(Collections.singletonList(assetAttributeDto.getDataDefault()));
                } else if (Integer.parseInt(assetAttributeDto.getDataType()) == AttributeDataType.DIMENSION_TABLE.getCode() && assetAttributeDto.getRelatedDimensionTable() != null) {
                    getRelateDimensionData(assetAttributeDto);
                }


                String catalogueAttrId = catalogueAttributeDto.getId();
                if (MapUtil.isNotEmpty(assetAttributeEntityMap) && assetAttributeEntityMap.containsKey(catalogueAttrId)) {
                    AssetAttributeEntity assetAttributeEntity = assetAttributeEntityMap.get(catalogueAttributeDto.getId());

                    assetAttributeDto.setId(assetAttributeEntity.getId());
                    assetAttributeDto.setCreateBy(assetAttributeEntity.getCreateBy());
                    assetAttributeDto.setUpdateBy(assetAttributeEntity.getUpdateBy());
                    assetAttributeDto.setUpdateTime(assetAttributeEntity.getUpdateTime());
                    assetAttributeDto.setCreateTime(assetAttributeEntity.getCreateTime());

                    assetAttributeDto.setAttributeValue(assetAttributeEntity.getAttributeValue());
                    assetAttributeDto.setAssetAttributeSetId(assetAttributeEntity.getAssetAttributeSetId());
                }

                String attributeCode = assetAttributeDto.getAttributeCode();
                //资产代码、资产类目不可编辑
                if (StringUtils.isNotBlank(assetCode) && attributeCode.equals("SJZCDM") && CollectionUtil.isEmpty(assetAttributeDto.getAttributeValue())) {
                    assetAttributeDto.setAttributeValue(Collections.singletonList(assetCode));
                }
                if (StringUtils.isNotBlank(cateGoryName) && attributeCode.equals("SJZCLM") && CollectionUtil.isEmpty(assetAttributeDto.getAttributeValue())) {
                    assetAttributeDto.setAttributeValue(Collections.singletonList(cateGoryName));
                }

                assetAttributeDto.setCatalogueAttrId(catalogueAttrId);


                assetAttributeDtoList.add(assetAttributeDto);
            }
        }
        return assetAttributeDtoList;
    }

    private static final String BASE_URL = "http://127.0.0.1:8090";

    public AjaxResult queryDimDataByDimNo(Long dimNo) throws Exception {
        String url = BASE_URL + "/dim-base-info/queryDimDataByDimNo";
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, Long> requestBody = new LinkedMultiValueMap<>();
        requestBody.add("dimNo", dimNo);

        HttpEntity<MultiValueMap<String, Long>> requestEntity = new HttpEntity<>(requestBody, headers);
        ResponseEntity<AjaxResult> responseEntity = restTemplate.postForEntity(url, requestEntity, AjaxResult.class);

        return responseEntity.getBody();
    }

    //获取维表数据
    private void getRelateDimensionData(AssetAttributeDto assetAttributeDto) {
        //查维表
//        AjaxResult ajaxResult = indexManageClient.queryDimDataByDimNo(Long.parseLong(assetAttributeDto.getRelatedDimensionTable()));

        try {
            AjaxResult ajaxResult = queryDimDataByDimNo(Long.parseLong(assetAttributeDto.getRelatedDimensionTable()));

            int code = (int) ajaxResult.get("code");
            if (code == 200 && ajaxResult.get("data") != null) {
                Map<String, Object> sqlConsoleVo = (Map) ajaxResult.get("data");
                //系统维表数据必须只有一列
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) sqlConsoleVo.get("dataList");
                if (CollectionUtil.isNotEmpty(dataList)) {
                    List<String> optionData = dataList.stream()
                            .map(map -> {
                                Map<String, Object> filteredMap = new HashMap<>(map);
                                filteredMap.remove("id"); // 移除 key 为 "id" 的项
                                return filteredMap;
                            })
                            .filter(map -> map.size() == 1) // 确保每个 Map 只有一个键值对
                            .map(map -> map.values().iterator().next().toString())
                            .collect(Collectors.toList());
                    assetAttributeDto.setOptionData(optionData);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public AssetAttributeSetAndDataDto queryMountAssetAttributeDataList(AssetAttributeSetEntity assetAttributeSetEntity) {

        AssetAttributeSetAndDataDto assetAttributeSetAndDataDto = new AssetAttributeSetAndDataDto();

        AssetAttributeSetEntity assetAttributeSet = assetAttributeSetMapper.selectById(assetAttributeSetEntity.getId());

        Map<String, AssetAttributeEntity> assetAttributeEntityMap = new HashMap<>();
        LambdaQueryWrapper<AssetAttributeEntity> lqw = new LambdaQueryWrapper<>();
        lqw.eq(AssetAttributeEntity::getAssetAttributeSetId, assetAttributeSetEntity.getId());
        lqw.in(AssetAttributeEntity::getAttributeCode, "SJZCGSFL", "SJZCGS");
        List<AssetAttributeEntity> assetAttributeEntities = this.list(lqw);
        if (CollectionUtil.isNotEmpty(assetAttributeEntities)) {
            assetAttributeEntityMap = assetAttributeEntities.stream().collect(Collectors.toMap(AssetAttributeEntity::getCatalogueAttrId, assetAttributeEntity -> assetAttributeEntity));
        }

        List<AssetAttributeDto> assetAttributeDtoList = getAssetAttributeDtoList(assetAttributeSetEntity.getCatalogueId(), assetAttributeEntityMap, null, null);

        if (assetAttributeDtoList != null) {
            assetAttributeDtoList = assetAttributeDtoList.stream().filter(catalogueAttributeDto -> catalogueAttributeDto.getAttributeCode().matches("(SJZCGSFL|SJZCGS)")).collect(Collectors.toList());
            for (AssetAttributeDto assetAttributeDto : assetAttributeDtoList) {
                //查维表
                getRelateDimensionData(assetAttributeDto);
            }
        }

        BeanUtils.copyProperties(assetAttributeSet, assetAttributeSetAndDataDto);
        assetAttributeSetAndDataDto.setAssetAttributeDtoList(assetAttributeDtoList);

        return assetAttributeSetAndDataDto;
    }


    @Override
    public boolean createAndUpdateAssetAttribute(Map<String, List<AssetAttributeDto>> assetAttributeDtoMap, String assetAttributeSetId) {
        boolean save = false;
        boolean update = false;

        if (MapUtil.isNotEmpty(assetAttributeDtoMap)) {
            List<AssetAttributeDto> assetAttributeDtoList = new ArrayList<>();

            for (String attributeSetName : assetAttributeDtoMap.keySet()) {
                assetAttributeDtoList.addAll(assetAttributeDtoMap.get(attributeSetName));
            }

            List<AssetAttributeEntity> saveAssetAttributeEntities = new ArrayList<>();
            List<AssetAttributeEntity> updateAssetAttributeEntities = new ArrayList<>();

            for (AssetAttributeDto assetAttributeDto : assetAttributeDtoList) {
                AssetAttributeEntity assetAttributeEntity = new AssetAttributeEntity();
                assetAttributeEntity.setAssetAttributeSetId(assetAttributeSetId);
                assetAttributeEntity.setCatalogueAttrId(assetAttributeDto.getCatalogueAttrId());
                assetAttributeEntity.setAttributeName(assetAttributeDto.getAttributeName());
                assetAttributeEntity.setAttributeCode(assetAttributeDto.getAttributeCode());

                assetAttributeEntity.setAttributeValue(assetAttributeDto.getAttributeValue());
                assetAttributeEntity.setUpdateBy(getUsername());
                if (StringUtils.isNotBlank(assetAttributeDto.getId())) {
                    assetAttributeEntity.setId(assetAttributeDto.getId());
                    updateAssetAttributeEntities.add(assetAttributeEntity);
                } else {
                    assetAttributeEntity.setCreateBy(getUsername());
                    assetAttributeEntity.setUpdateTime(new Date());
                    saveAssetAttributeEntities.add(assetAttributeEntity);
                }
            }


            if (CollectionUtil.isNotEmpty(updateAssetAttributeEntities)) {
                update = this.updateBatchById(updateAssetAttributeEntities);
            }
            if (CollectionUtil.isNotEmpty(saveAssetAttributeEntities)) {
                save = this.saveBatch(saveAssetAttributeEntities);
            }


        }

        return save && update;
    }


}
