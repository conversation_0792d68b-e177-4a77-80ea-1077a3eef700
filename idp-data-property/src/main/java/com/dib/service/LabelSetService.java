package com.dib.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dib.domin.dto.LabelSetDto;
import com.dib.domin.entity.LabelSetEntity;

import java.util.List;

public interface LabelSetService extends IService<LabelSetEntity> {
    boolean saveLabelSet(LabelSetEntity labelSetEntity);

    boolean updateLabelSet(LabelSetEntity labelSetEntity);

    boolean deleteLabelSetById(String id);

    List<LabelSetDto> getLabelSetList();
}
