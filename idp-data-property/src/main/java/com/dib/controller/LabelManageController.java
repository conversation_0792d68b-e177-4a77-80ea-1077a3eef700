package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.entity.LabelDataEntity;
import com.dib.domin.entity.LabelSetEntity;
import com.dib.service.LabelDataService;
import com.dib.service.LabelSetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 标签管理
 */
@Tag(name = "资产标签管理")
@RestController
@RequestMapping("/labelManage")
public class LabelManageController {

    @Autowired
    private LabelSetService labelSetService;

    @Autowired
    private LabelDataService labelDataService;

    // 添加
    @Operation(summary = "新增标签集", description = "新增标签集")
    @PostMapping("/addLabelSet")
    public AjaxResult addLabelSet(@RequestBody LabelSetEntity labelSetEntity) {
        return AjaxResult.success(labelSetService.saveLabelSet(labelSetEntity));
    }

    // 更新
    @Operation(summary = "修改标签集", description = "修改标签集")
    @PostMapping("/updateLabelSet")
    public AjaxResult updateLabelSet(@RequestBody LabelSetEntity labelSetEntity) {
        return AjaxResult.success(labelSetService.updateLabelSet(labelSetEntity));
    }

    // 删除
    @Operation(summary = "修改标签集", description = "修改标签集")
    @DeleteMapping("/deleteLabelSet")
    public AjaxResult deleteLabelSet(@RequestParam String id) {
        return AjaxResult.success(labelSetService.deleteLabelSetById(id));
    }

    // 查询
    @Operation(summary = "获取标签集", description = "获取标签集")
    @GetMapping("/getLabelSetList")
    public AjaxResult getLabelSetList() {
        return AjaxResult.success(labelSetService.getLabelSetList());
    }

    // 查询
    @Operation(summary = "获取所有标签数据", description = "获取所有标签数据")
    @GetMapping("/getLabelDataAll")
    public AjaxResult getLabelDataAll() {
        return AjaxResult.success(labelDataService.getLabelDataAll());
    }


    // 添加
    @Operation(summary = "新增标签数据", description = "新增标签数据")
    @PostMapping("/addLabelData")
    public AjaxResult addLabelData(@RequestBody LabelDataEntity labelDataEntity) {
        return AjaxResult.success(labelDataService.addLabelData(labelDataEntity));
    }

    // 更新
    @Operation(summary = "修改标签数据", description = "修改标签数据")
    @PostMapping("/updateLabelData")
    public AjaxResult updateLabelData(@RequestBody LabelDataEntity labelDataEntity) {
        return AjaxResult.success(labelDataService.updateLabelData(labelDataEntity));
    }

    // 删除
    @Operation(summary = "删除标签数据", description = "删除标签数据")
    @DeleteMapping("/deleteLabelData")
    public AjaxResult deleteLabelData(@RequestParam String id) {
        return AjaxResult.success(labelDataService.deleteLabelData(id));
    }



}
