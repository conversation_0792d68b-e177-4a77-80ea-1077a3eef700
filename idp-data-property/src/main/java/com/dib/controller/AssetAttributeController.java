package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.entity.AssetAttributeSetEntity;
import com.dib.service.AssetAttributeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 资产
 */
@Tag(name = "资产属性数据")
@RestController
@RequestMapping("/assetAttribute")
public class AssetAttributeController {

    @Autowired
    private AssetAttributeService assetAttributeService;


    // 读取所有
    @Operation(summary = "查询资产下所有属性", description = "查询资产下所有属性")
    @PostMapping("/queryAssetAttributeData")
    public AjaxResult queryAssetAttributeData(@RequestBody AssetAttributeSetEntity assetAttributeSetEntity) {
        return AjaxResult.success(assetAttributeService.queryAssetAttributeData(assetAttributeSetEntity));
    }


    // 读取所有
    @Operation(summary = "查询资产下信息项属性", description = "查询资产下信息项属性")
    @PostMapping("/queryInfoAttribute")
    public AjaxResult queryInfoAttribute(@RequestBody AssetAttributeSetEntity assetAttributeSetEntity) {
        return AjaxResult.success(assetAttributeService.queryInfoAttribute(assetAttributeSetEntity));
    }


    // 查询挂接的用到的属性
    @Operation(summary = "查询资产下所有挂接属性", description = "查询资产下所有挂接属性")
    @PostMapping("/queryMountAssetAttributeDataList")
    public AjaxResult queryMountAssetAttributeDataList(@RequestBody AssetAttributeSetEntity assetAttributeSetEntity) {
        return AjaxResult.success(assetAttributeService.queryMountAssetAttributeDataList(assetAttributeSetEntity));
    }

}