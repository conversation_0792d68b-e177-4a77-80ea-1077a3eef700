package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.dto.AttributeDataDto;
import com.dib.domin.dto.CatalogueAttributeDto;
import com.dib.domin.entity.CatalogueAttributeEntity;
import com.dib.service.CatalogueAttributeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "目录属性")
@RestController
@RequestMapping("/catalogueAttribute")
public class CatalogueAttributeController {

    @Autowired
    private CatalogueAttributeService catalogueAttributeService;

    /**
     * 新增
     * @param attributeDataDtoList
     * @return
     */
    @Operation(summary = "绑定目录属性", description = "绑定目录属性")
    @PostMapping("/add")
    public AjaxResult createCatalogueAttribute(@RequestBody List<AttributeDataDto> attributeDataDtoList) {
        return AjaxResult.success(catalogueAttributeService.createCatalogueAttribute(attributeDataDtoList));
    }

    /**
     * 获取指定目录，指定属性类型下数据
     * @param catalogueAttributeDto
     * @return
     */
    @Operation(summary = "获取指定目录指定属性集下属性", description = "获取指定目录指定属性集下属性")
    @PostMapping("/getCatalogueAttributeData")
    public AjaxResult getCatalogueAttributeData(@RequestBody CatalogueAttributeDto catalogueAttributeDto) {
        return AjaxResult.success(catalogueAttributeService.getCatalogueAttributeData(catalogueAttributeDto));
    }

    /**
     * 获取指定目录下数据
     * @param catalogueId
     * @return
     */
    @Operation(summary = "获取指定目录下详细属性", description = "获取指定目录下详细属性")
    @GetMapping("/getAssetCatalogueData")
    public AjaxResult getAssetCatalogueData(@RequestParam String catalogueId) {
        return AjaxResult.success(catalogueAttributeService.getAssetCatalogueData(catalogueId));
    }

    /**
     * 获取指定目录下目录数据
     * @param catalogueId
     * @return
     */
    @Operation(summary = "获取目录属性集下详细属性", description = "获取目录属性集下详细属性")
    @GetMapping("/getAttributeSetCatalogueData")
    public AjaxResult getAttributeSetCatalogueData(@RequestParam String catalogueId) {
        return AjaxResult.success(catalogueAttributeService.getAttributeSetCatalogueData(catalogueId));
    }

    /**
     * 修改目录属性
     * @param catalogueAttribute
     * @return
     */
    @Operation(summary = "修改目录属性", description = "修改目录属性")
    @PostMapping("/updateCatalogueAttribute")
    public AjaxResult updateCatalogueAttribute(@RequestBody CatalogueAttributeEntity catalogueAttribute) {
        boolean row = catalogueAttributeService.updateCatalogueAttribute(catalogueAttribute);
        if (row) {
            return AjaxResult.success(row);
        } else {
            return AjaxResult.error();
        }
    }

    /**
     * 根据id删除目录属性
     * @param id
     * @return
     */
    @Operation(summary = "删除目录属性", description = "删除目录属性")
    @DeleteMapping("deleteCatalogueAttribute")
    public AjaxResult deleteCatalogueAttribute(@RequestParam String id) {
        return AjaxResult.success(catalogueAttributeService.deleteCatalogueAttribute(id));
    }
}
