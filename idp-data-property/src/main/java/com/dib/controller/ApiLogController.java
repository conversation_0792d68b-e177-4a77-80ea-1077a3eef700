package com.dib.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.common.core.controller.BaseController;
import com.dib.common.core.domain.AjaxResult;
import com.dib.core.database.core.JsonPage;
import com.dib.market.dto.ApiLogDto;
import com.dib.market.entity.ApiLogEntity;
import com.dib.market.mapstruct.ApiLogMapper;
import com.dib.market.query.ApiLogQuery;
import com.dib.market.service.ApiLogService;
import com.dib.market.vo.ApiLogVo;
import com.dib.metadata.validate.ValidationGroups;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * api调用日志信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-21
 */
@Tag(name = "api调用日志信息表")
@RestController
@RequestMapping("market/apiLogs")
public class ApiLogController extends BaseController {

    @Autowired
    private ApiLogService apiLogService;

    @Autowired
    private ApiLogMapper apiLogMapper;

    /**
     * 通过ID查询信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "获取详细信息", description = "根据url的id来获取详细信息")
    @Parameter(name = "id", description = "ID", required = true)
    @GetMapping("/{id}")
    public AjaxResult getApiLogById(@PathVariable String id) {
        ApiLogEntity apiLogEntity = apiLogService.getApiLogById(id);
        return AjaxResult.success(apiLogMapper.toVO(apiLogEntity));
    }

    /**
     * 分页查询信息
     *
     * @param apiLogQuery
     * @return
     */
    @Operation(summary = "分页查询", description = "")
    @Parameter(name = "apiLogQuery", description = "查询实体apiLogQuery", required = true)
    @GetMapping("/page")
    public AjaxResult getApiLogPage(ApiLogQuery apiLogQuery) {
        QueryWrapper<ApiLogEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(apiLogQuery.getApiName()), "api.api_name", apiLogQuery.getApiName());
        IPage<ApiLogEntity> page = apiLogService.page(new Page<>(apiLogQuery.getPageNum(), apiLogQuery.getPageSize()), queryWrapper);
        List<ApiLogVo> collect = page.getRecords().stream().map(apiLogMapper::toVO).collect(Collectors.toList());
        JsonPage<ApiLogVo> jsonPage = new JsonPage<>(page.getCurrent(), page.getSize(), page.getTotal(), collect);
        return AjaxResult.success(jsonPage);
    }

    /**
     * 添加
     * @param apiLog
     * @return
     */
    @Operation(summary = "添加信息", description = "根据apiLog对象添加信息")
    @Parameter(name = "apiLog", description = "详细实体apiLog", required = true)
    @PostMapping()
    public AjaxResult saveApiLog(@RequestBody @Validated({ValidationGroups.Insert.class}) ApiLogDto apiLog) {
        ApiLogEntity apiLogEntity = apiLogService.saveApiLog(apiLog);
        return AjaxResult.success(apiLogMapper.toVO(apiLogEntity));
    }

    /**
     * 修改
     * @param apiLog
     * @return
     */
    @Operation(summary = "修改信息", description = "根据url的id来指定修改对象，并根据传过来的信息来修改详细信息")
    @Parameter(name = "id", description = "ID", required = true)
    @Parameter(name = "apiLog", description = "详细实体apiLog", required = true)
    @PutMapping("/{id}")
    public AjaxResult updateApiLog(@PathVariable String id, @RequestBody @Validated({ValidationGroups.Update.class}) ApiLogDto apiLog) {
        ApiLogEntity apiLogEntity = apiLogService.updateApiLog(apiLog);
        return AjaxResult.success(apiLogMapper.toVO(apiLogEntity));
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Operation(summary = "删除", description = "根据url的id来指定删除对象")
    @Parameter(name = "id", description = "ID", required = true)
    @DeleteMapping("/{id}")
    public AjaxResult deleteApiLogById(@PathVariable String id) {
        apiLogService.deleteApiLogById(id);
        return AjaxResult.success();
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @Operation(summary = "批量删除", description = "根据url的ids来批量删除对象")
    @Parameter(name = "ids", description = "ID集合", required = true)
    @DeleteMapping("/batch/{ids}")
    public AjaxResult deleteApiLogBatch(@PathVariable List<String> ids) {
        apiLogService.deleteApiLogBatch(ids);
        return AjaxResult.success();
    }
}
