package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.entity.CatalogueEntity;
import com.dib.domin.query.CatalogueQuery;
import com.dib.service.CatalogueService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 目录
 */
@Tag(name = "目录管理")
@RestController
@RequestMapping("/catalogue")
public class CatalogueController {

    @Autowired
    private CatalogueService catalogueService;


    @Operation(summary = "获取所有目录", description = "获取所有目录")
    @GetMapping("/list")
    public AjaxResult getCatalogueList() {
        return AjaxResult.success(catalogueService.getCatalogueList());
    }

    @Operation(summary = "分页查询目录", description = "分页查询目录")
    @PostMapping("/page")
    public AjaxResult getCataloguePage(@RequestBody CatalogueQuery catalogueQuery) {
        return AjaxResult.success(catalogueService.getCataloguePage(catalogueQuery));
    }

    @Operation(summary = "新增目录", description = "新增目录")
    @PostMapping("/addCatalogue")
    public AjaxResult addCatalogue(@RequestBody CatalogueEntity catalogueEntity) {
        boolean result = catalogueService.addCatalogue(catalogueEntity);
        if (!result) {
            return AjaxResult.error("新增失败");
        }
        return AjaxResult.success("新增成功");
    }

    @Operation(summary = "删除目录", description = "删除目录")
    @PostMapping("/deleteCatalogue")
    public AjaxResult deleteCatalogue(@RequestBody List<String> ids) {
        boolean result = catalogueService.deleteCatalogue(ids);
        if (!result) {
            return AjaxResult.error("删除失败");
        }
        return AjaxResult.success("删除成功");
    }

    @Operation(summary = "删除目录测试", description = "删除目录测试")
    @PostMapping("/deleteTestCatalogue")
    public AjaxResult deleteTestCatalogue(@RequestBody List<String> ids) {
        return AjaxResult.success(catalogueService.deleteTestCatalogue(ids));
    }


    @Operation(summary = "修改目录", description = "修改目录")
    @PostMapping("/updateCatalogue")
    public AjaxResult updateCatalogue(@RequestBody CatalogueEntity catalogueEntity) {
        boolean result = catalogueService.updateCatalogue(catalogueEntity);
        if (!result) {
            return AjaxResult.error("修改失败");
        }
        return AjaxResult.success("修改成功");
    }


    @Operation(summary = "查询在编目录", description = "查询在编目录")
    @GetMapping("/queryEditCatalogueList")
    public AjaxResult queryEditCatalogueList() {
        return AjaxResult.success(catalogueService.queryEditCatalogueList());
    }

    @Operation(summary = "查询开放目录", description = "查询开放目录")
    @GetMapping("/queryOpennessCatalogueList")
    public AjaxResult queryOpennessCatalogueList() {
        return AjaxResult.success(catalogueService.queryOpennessCatalogueList());
    }

}
