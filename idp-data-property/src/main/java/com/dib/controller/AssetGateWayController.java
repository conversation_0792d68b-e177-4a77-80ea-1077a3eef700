package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.entity.AssetGateWayEntity;
import com.dib.domin.query.AssetGateWayQuery;
import com.dib.service.AssetGateWayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name = "资产门户")
@RestController
@RequestMapping("/assetGateWay")
public class AssetGateWayController {

    @Autowired
    private AssetGateWayService assetGateWayService;

    // 创建
    @Operation(summary = "创建资产门户", description = "创建资产门户")
    @PostMapping("/add")
    public AjaxResult addAssetGateWay(@RequestBody AssetGateWayEntity assetGateWayEntity) {

        return AjaxResult.success(assetGateWayService.saveAssetAttribute(assetGateWayEntity));
    }

    // 查询所有
    @Operation(summary = "分页查询资产门户列表", description = "分页查询资产门户列表")
    @PostMapping("/page")
    public AjaxResult page(@RequestBody AssetGateWayQuery assetGateWayQuery) {
        return AjaxResult.success(assetGateWayService.getAssetGateWay(assetGateWayQuery));
    }

    // 查询单个
    @Operation(summary = "获取资产门户查询分类信息", description = "获取资产门户查询分类信息")
    @GetMapping("/getAssetGateWayById")
    public AjaxResult getAssetGateWayById(@RequestParam String id) {
        return AjaxResult.success(assetGateWayService.getAssetGateWayById(id));

    }

    // 更新
    @Operation(summary = "更新资产门户", description = "更新资产门户")
    @PostMapping("/updateAssetGateWay")
    public AjaxResult updateAssetGateWay(@RequestBody AssetGateWayEntity assetGateWayEntity) {
        return AjaxResult.success(assetGateWayService.updateAssetGateWay(assetGateWayEntity));
    }

    // 删除
    @Operation(summary = "删除资产门户", description = "删除资产门户")
    @DeleteMapping("/deleteAssetGateWay")
    public AjaxResult deleteAssetGateWay(@RequestParam String id) {
        return AjaxResult.success(assetGateWayService.deleteAssetGateWay(id));
    }

    // 查询资产详细信息
    @Operation(summary = "查询资产门户详细信息", description = "查询资产门户详细信息")
    @PostMapping("/getAssetGateWayAttributeInfo")
    public AjaxResult getAssetGateWayAttributeInfo(@RequestBody AssetGateWayQuery assetGateWayQuery) {
        return AjaxResult.success(assetGateWayService.getAssetGateWayAttributeInfo(assetGateWayQuery));
    }
}
