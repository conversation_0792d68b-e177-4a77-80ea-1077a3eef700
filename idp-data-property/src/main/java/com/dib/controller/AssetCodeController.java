package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.entity.AssetCodeEntity;
import com.dib.service.AssetCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 编码规则
 */
@Tag(name = "资产编码")
@RestController
@RequestMapping("/assetCode")
public class AssetCodeController {

    @Autowired
    private AssetCodeService assetCodeService;


    // 创建（Create）
    @Operation(summary = "创建资产编码", description = "创建资产编码")
    @PostMapping("/createAssetCode")
    public AjaxResult createAssetCode(@RequestBody List<AssetCodeEntity> assetCodeEntities) {
        return AjaxResult.success(assetCodeService.createAssetCode(assetCodeEntities));
    }


    // 读取目录下所有资产编码（Read One）
    @Operation(summary = "根据目录id获取资产编码", description = "根据目录id获取资产编码")
    @GetMapping("/getAssetCodeByCatalogueId")
    public AjaxResult getAssetCodeByCatalogueId(@RequestParam String catalogueId) {
        return AjaxResult.success(assetCodeService.getAssetCodeByCatalogueId(catalogueId));
    }

    // 更新（Update）
//    @PutMapping("/{id}")
//    public AjaxResult updateAssetCode(@RequestBody AssetCodeEntity assetCodeEntity) {
//        return AjaxResult.success(assetCodeService.updateAssetCode(assetCodeEntity));
//    }

    // 删除（Delete）
//    @DeleteMapping("/deleteAssetCode")
//    public AjaxResult deleteAssetCode(@RequestBody AssetCodeEntity assetCodeEntity) {
//        return AjaxResult.success(assetCodeService.deleteAssetCode(assetCodeEntity));
//    }


    // 读取目录下所有资产编码（Read One）
    @Operation(summary = "预览资产编码", description = "预览资产编码")
    @PostMapping("/getPreviewCode")
    public AjaxResult getPreviewCode(@RequestBody List<AssetCodeEntity> assetCodeEntities) {
        return AjaxResult.success("预览生成成功",assetCodeService.getPreviewCode(assetCodeEntities));
    }


}
