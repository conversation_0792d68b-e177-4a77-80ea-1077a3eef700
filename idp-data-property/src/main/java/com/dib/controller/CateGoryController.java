package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.entity.CateGoryEntity;
import com.dib.service.CateGoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 目录类目
 */
@Tag(name = "资产目录类目")
@RestController
@RequestMapping("/cateGory")
public class CateGoryController {

    @Autowired
    private CateGoryService cateGoryService;

    /**
     * 创建目录
     * @param cateGoryEntity
     * @return
     */
    @Operation(summary = "添加目录类目", description = "添加目录类目")
    @PostMapping("/add")
    public AjaxResult addCateGory(@RequestBody CateGoryEntity cateGoryEntity) {
        return AjaxResult.success(cateGoryService.addCateGory(cateGoryEntity));
    }


    // 读取单个分类（Read One）
    @Operation(summary = "获取目录类目树", description = "获取目录类目树")
    @GetMapping("/getCateGoryTreeById")
    public AjaxResult getCateGoryTreeById(@RequestParam String catalogueId) {
        return AjaxResult.success(cateGoryService.getCateGoryTreeById(catalogueId));
    }

    // 更新（Update）
    @Operation(summary = "更新目录类目", description = "更新目录类目")
    @PostMapping("/update")
    public AjaxResult updateCateGory(@RequestBody CateGoryEntity cateGoryEntity) {
        return AjaxResult.success(cateGoryService.updateCateGory(cateGoryEntity));
    }

    // 删除（Delete）
    @Operation(summary = "删除目录类目", description = "删除目录类目")
    @PostMapping("/deleteCateGory")
    public AjaxResult deleteCateGory(@RequestBody CateGoryEntity cateGoryEntity) {
        return AjaxResult.success(cateGoryService.deleteCateGory(cateGoryEntity));
    }

}
