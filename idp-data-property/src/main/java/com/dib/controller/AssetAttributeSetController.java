package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.dto.AssetAttributeAndSetDto;
import com.dib.domin.dto.AssetAttributeSetAndDataDto;
import com.dib.domin.dto.AssetAttributeSetDto;
import com.dib.domin.query.AssetAttributeSetQuery;
import com.dib.service.AssetAttributeSetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 资产编目
 */
@Tag(name = "资产编目")
@RestController
@RequestMapping("/assetAttributeSet")
public class AssetAttributeSetController {

    @Autowired
    private AssetAttributeSetService assetAttributeSetService;


    // 创建
    @Operation(summary = "创建资产", description = "创建资产")
    @PostMapping("/add")
    public AjaxResult createAssetAttributeSet(@RequestBody AssetAttributeAndSetDto assetAttributeAndSetDto) {
        return AjaxResult.success(assetAttributeSetService.createAssetAttributeSet(assetAttributeAndSetDto));
    }


    //根据类目id获取资产
    @Operation(summary = "根据类目分页获取资产", description = "根据类目分页获取资产")
    @PostMapping("getAssetAttributeSetByCategoryId")
    public AjaxResult getAssetAttributeSetByCategoryId(@RequestBody AssetAttributeSetQuery assetAttributeSetQuery) {
        return AjaxResult.success(assetAttributeSetService.getAssetAttributeSetByCategoryId(assetAttributeSetQuery));
    }

    // 更新
    @Operation(summary = "修改资产", description = "修改资产")
    @PostMapping("/updateAssetAttributeSet")
    public AjaxResult updateAssetAttributeSet(@RequestBody AssetAttributeSetDto assetAttributeSetDto) {
        return AjaxResult.success(assetAttributeSetService.updateAssetAttributeSet(assetAttributeSetDto));
    }

    // 删除
    @Operation(summary = "删除资产", description = "删除资产")
    @DeleteMapping("/deleteAssetAttributeSet")
    public AjaxResult deleteAssetAttributeSet(@RequestParam String id) {
        return AjaxResult.success(assetAttributeSetService.deleteAssetAttributeSet(id));
    }

    // 修改挂接资产数据
    @Operation(summary = "修改挂接资产数据", description = "修改挂接资产数据")
    @PostMapping("/updateMountAssetAttributeData")
    public AjaxResult updateMountAssetAttributeData(@RequestBody AssetAttributeSetAndDataDto assetAttributeSetAndDataDto) {
        return AjaxResult.success(assetAttributeSetService.updateMountAssetAttributeData(assetAttributeSetAndDataDto));
    }
}
