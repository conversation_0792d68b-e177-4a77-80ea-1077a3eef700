package com.dib.index;

import java.util.List;

public class TDimBaseRelaColVo {

    /**
     * 维度基础信息的唯一编号，作为主键
     */
    private Long id;
    /**
     * 维度的名称
     */
    private String dimName;

    /**
     * 所使用的数据库连接池
     */
    private String databasePool;

    /**
     * 数据库id
     */
    private String sourceId;

    /**
     * 维度表ID
     */
    private String tableId;

    /**
     * 关联的表名
     */
    private String tableName;

    /**
     * 维度表列信息
     */
    private List<String> dimColumnSettingVoList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDimName() {
        return dimName;
    }

    public void setDimName(String dimName) {
        this.dimName = dimName;
    }

    public String getDatabasePool() {
        return databasePool;
    }

    public void setDatabasePool(String databasePool) {
        this.databasePool = databasePool;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List<String> getDimColumnSettingVoList() {
        return dimColumnSettingVoList;
    }

    public void setDimColumnSettingVoList(List<String> dimColumnSettingVoList) {
        this.dimColumnSettingVoList = dimColumnSettingVoList;
    }

    public String getTableId() {
        return tableId;
    }

    public void setTableId(String tableId) {
        this.tableId = tableId;
    }
}
