package com.dib.ftpdata.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("data_source_ftp")
@Schema(description = "FTP配置Model")
public class FtpConfig {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("title")
    private String title;

    @TableField("protocol")
    private String protocol;

    @TableField("host")
    private String host;

    @TableField("port")
    private Integer port;

    @TableField("username")
    private String username;

    @TableField("password")
    private String password;

    @TableField("charset")
    private String charset;

    @TableField("private_key")
    private String privateKey;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField("remark")
    private String remark;

    @TableField("sys_id")
    private String sysId;

}
