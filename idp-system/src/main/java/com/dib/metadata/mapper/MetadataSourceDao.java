package com.dib.metadata.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.dib.core.database.base.BaseDao;
import com.dib.metadata.entity.MetadataSourceEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 数据源信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-14
 */
@Mapper
public interface MetadataSourceDao extends BaseMapper<MetadataSourceEntity> {
}
