package com.dib.metadata.dto;

import com.dib.metadata.validate.ValidationGroups;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class SqlConsoleDto implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "当前时间戳")
    @NotBlank(message = "时间戳不能为空", groups = {ValidationGroups.Other.class})
    private String sqlKey;

    @Schema(description = "数据源")
    @NotBlank(message = "数据源不能为空")
    private String sourceId;

    @Schema(description = "SQL文本")
    private String sqlText;

    @Schema(description = "表名")
    private String tableName;

    @Schema(description = "是否限制查询行数")
    private Boolean isLimit = true;
}
