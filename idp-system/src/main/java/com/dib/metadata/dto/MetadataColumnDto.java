package com.dib.metadata.dto;

import com.dib.metadata.validate.ValidationGroups;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <p>
 * 元数据信息表 实体DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-29
 */
@Schema(description = "元数据字段DTO")
@Data
public class MetadataColumnDto implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @NotBlank(message = "主键ID不能为空", groups = {ValidationGroups.Update.class})
    private String id;
    @Schema(description = "所属数据源")
    private String sourceId;
    @Schema(description = "所属数据表")
    private String tableId;
    @Schema(description = "字段名")
    private String columnName;
    @Schema(description = "字段描述")
    private String columnComment;
    @Schema(description = "字段是否主键(1是0否)")
    private String columnKey;
    @Schema(description = "字段是否允许为空(1是0否)")
    private String columnNullable;
    @Schema(description = "字段序号")
    private Integer columnPosition;
    @Schema(description = "字段类型")
    private String dataType;
    @Schema(description = "数据长度")
    private String dataLength;
    @Schema(description = "数据精度")
    private String dataPrecision;
    @Schema(description = "数据小数位")
    private String dataScale;
    @Schema(description = "数据默认值")
    private String dataDefault;
}
