package com.dib.metadata.service;



import com.dib.metadata.dto.SqlConsoleDto;
import com.dib.metadata.vo.SqlConsoleVo;
import com.dib.metadata.vo.SqlTableVo;

import java.sql.SQLException;
import java.util.List;

public interface SqlConsoleService {
    
    List<SqlConsoleVo> sqlRun(SqlConsoleDto sqlConsoleDto) throws SQLException;

    void sqlStop(SqlConsoleDto sqlConsoleDto);

    List<SqlTableVo> selectTablesBySourceId(SqlConsoleDto sqlConsoleDto);

    String querySql(SqlConsoleDto sqlConsoleDto);

}
