package com.dib.apidata.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Schema(description = "API 接口同步配置项")
@TableName("api_sync_config_new")
public class ApiSyncConfig {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "主键 ID")
    private Long id;

    @Schema(description = "接口编号")
    @TableField("api_id")
    private String apiId;

    @Schema(description = "系统id")
    @TableField("sys_id")
    private String sysId;

    @Schema(description = "配置名称，例如：同步用户信息")
    @TableField("name")
    private String name;

    @Schema(description = "所属系统名称")
    @TableField("sys_name")
    private String sysName;

    @Schema(description = "负责公司")
    @TableField("org_name")
    private String orgName;

    @Schema(description = "接口功能描述")
    @TableField("api_description")
    private String apiDescription;

    @Schema(description = "服务大类")
    @TableField("service_category")
    private String serviceCategory;

    @Schema(description = "服务小类")
    @TableField("service_subcategory")
    private String serviceSubcategory;

    @Schema(description = "接口版本")
    @TableField("api_version")
    private String apiVersion;

    @Schema(description = "请求 URL，例如：https://api.example.com/users")
    @TableField("url")
    private String url;

    @Schema(description = "调用方式 如 http")
    @TableField("invocation_method")
    private String invocationMethod;

    @Schema(description = "请求方法，例如：GET、POST")
    @TableField("method")
    private String method;

    @Schema(description = "数据格式，例如：json、xml")
    @TableField("data_format")
    private String dataFormat;

    @Schema(description = "前置 API 配置 ID（可选）")
    @TableField("pre_api_id")
    private Long preApiId;

    @Schema(description = "请求头 JSON 格式字符串")
    @TableField("headers_json")
    private String headersJson;

    @Schema(description = "请求参数 JSON 格式字符串")
    @TableField("params_json")
    private String paramsJson;

    @Schema(description = "Groovy 响应解析脚本")
    @TableField("response_parse_script")
    private String responseParseScript;

    @Schema(description = "认证方式")
    @TableField("authentication_method")
    private String authenticationMethod;

    @Schema(description = "权限控制")
    @TableField("access_control")
    private String accessControl;

    @Schema(description = "敏感数据 ", defaultValue = "true")
    @TableField("sensitive_data_flag")
    private boolean sensitiveDataFlag;

    @Schema(description = "加密要求 ")
    @TableField("encryption_requirement")
    private String encryptionRequirement;

    @Schema(description = "api账号 ")
    @TableField("api_account")
    private String apiAccount;

    @Schema(description = "api密码 ")
    @TableField("api_password")
    private String apiPassword;

    @Schema(description = "调用频率 ")
    @TableField("call_frequency")
    private String callFrequency;

    @Schema(description = "平均响应时间 ")
    @TableField("avg_response_time")
    private String avgResponseTime;

    @Schema(description = "错误率（近30天） ")
    @TableField("error_rate")
    private String errorRate;

    @Schema(description = "超时阈值")
    @TableField("timeout_threshold")
    private String timeoutThreshold;

    @Schema(description = "告警条件")
    @TableField("monitoring_config")
    private String monitoringConfig;

    @Schema(description = "接口负责人")
    @TableField("api_owner")
    private String apiOwner;

    @Schema(description = "联系方式")
    @TableField("api_contact")
    private String apiContact;

    @Schema(description = "文档连接地址")
    @TableField("documentation_url")
    private String documentationUrl;

    @Schema(description = "接口状态", defaultValue = "true")
    @TableField("enabled")
    private boolean enabled;

    @Schema(description = "接口最后更新时间")
    @TableField("last_update_time")
    private Date lastUpdateTime;

    @Schema(description = "备注")
    @TableField("remarks")
    private String remarks;

    @Schema(description = "字段映射 JSON，例如 {\"name\": \"userName\"}")
    @TableField("field_mapping_json")
    private String fieldMappingJson;

    @Schema(description = "目标表名，用于存储数据")
    @TableField("target_table")
    private String targetTable;

    @Schema(description = "创建时间")
    @TableField("create_time")
    private Date createTime;

    @Schema(description = "更新时间")
    @TableField("update_time")
    private Date updateTime;

    @Schema(description = "定时表达式（Quartz Cron 表达式）")
    @TableField("cron_expression")
    private String cronExpression;

}
