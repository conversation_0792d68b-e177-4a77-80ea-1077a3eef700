package com.dib.standard.dto;

import com.dib.metadata.validate.ValidationGroups;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 字典对照信息表 实体DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-27
 */
@Schema(description = "字典对照信息表Model")
@Data
public class ContrastDictDto implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @NotBlank(message = "主键ID不能为空", groups = {ValidationGroups.Update.class})
    private String id;
    @Schema(description = "字典对照主键")
    private String contrastId;
    @Schema(description = "字典编码")
    private String colCode;
    @Schema(description = "字典名称")
    private String colName;
    @Schema(description = "状态")
    @NotNull(message = "状态不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String status;
    @Schema(description = "备注")
    private String remark;
}
