package com.dib.standard.dto;

import com.dib.metadata.validate.ValidationGroups;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
/**
 * <p>
 * 数据标准类别表 实体DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-26
 */
@Schema(description = "数据标准类别表Model")
@Data
public class TypeDto implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @NotBlank(message = "主键ID不能为空", groups = {ValidationGroups.Update.class})
    private String id;
    @Schema(description = "标准类别编码")
    @NotBlank(message = "标准类别编码不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String gbTypeCode;
    @Schema(description = "标准类别名称")
    @NotBlank(message = "标准类别名称不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String gbTypeName;
}
