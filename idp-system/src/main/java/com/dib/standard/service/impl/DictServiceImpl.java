package com.dib.standard.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dib.common.core.redis.RedisCache;
import com.dib.core.database.base.BaseServiceImpl;
import com.dib.core.database.core.RedisConstant;
import com.dib.standard.dto.DictDto;
import com.dib.standard.entity.DictEntity;
import com.dib.standard.mapper.DictDao;
import com.dib.standard.mapstruct.DictMapper;
import com.dib.standard.service.DictService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.dib.common.utils.SecurityUtils.getUsername;

/**
 * <p>
 * 数据标准字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-26
 */
@Service
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class DictServiceImpl extends BaseServiceImpl<DictDao, DictEntity> implements DictService {

    @Autowired
    private DictDao dictDao;

    @Autowired
    private DictMapper dictMapper;

    @Autowired
    private RedisCache redisService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DictEntity saveDict(DictDto dictDto) {
        DictEntity dict = dictMapper.toEntity(dictDto);
        dict.setCreateBy(getUsername());
        dictDao.insert(dict);
        return dict;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DictEntity updateDict(DictDto dictDto) {
        DictEntity dict = dictMapper.toEntity(dictDto);
        dict.setUpdateBy(getUsername());
        dictDao.updateById(dict);
        return dict;
    }

    @Override
    public DictEntity getDictById(String id) {
        DictEntity dictEntity = super.getById(id);
        return dictEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDictById(String id) {
        dictDao.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDictBatch(List<String> ids) {
        dictDao.deleteBatchIds(ids);
    }

    @Override
    public void refreshDict() {
        String dictKey = RedisConstant.STANDARD_DICT_KEY;
        Boolean hasDictKey = redisService.hasKey(dictKey);
        if (hasDictKey) {
            redisService.del(dictKey);
        }
        List<DictEntity> dictEntityList = dictDao.selectList(Wrappers.emptyWrapper());
        Map<String, List<DictEntity>> dictListMap = dictEntityList.stream().collect(Collectors.groupingBy(DictEntity::getTypeId));
        redisService.putAll(dictKey, dictListMap);
    }
}
