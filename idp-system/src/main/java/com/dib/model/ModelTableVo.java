package com.dib.model;


import java.util.List;

public class ModelTableVo {

    private Long ModelId;

    private String TableId;

    private String tabName;

    private String sourceId;

    private List<String> modelColumns;

    public Long getModelId() {
        return ModelId;
    }

    public void setModelId(Long modelId) {
        ModelId = modelId;
    }

    public String getTableId() {
        return TableId;
    }

    public void setTableId(String tableId) {
        TableId = tableId;
    }

    public String getTabName() {
        return tabName;
    }

    public void setTabName(String tabName) {
        this.tabName = tabName;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public List<String> getModelColumns() {
        return modelColumns;
    }

    public void setModelColumns(List<String> modelColumns) {
        this.modelColumns = modelColumns;
    }
}
