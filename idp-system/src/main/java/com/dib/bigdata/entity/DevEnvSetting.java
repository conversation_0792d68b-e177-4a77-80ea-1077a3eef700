package com.dib.bigdata.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Data
public class DevEnvSetting {

    @Schema(description = "环境ID")
    private int id;

    @Schema(description = "属性名称")
    private String name;

    @Schema(description = "属性值")
    private String propValue;

    @Schema(description = "属性描述")
    private String description;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "标记")
    private Boolean flag;

	@Schema(description = "上传的URL")
	private Boolean uploadurl;

	@Schema(description = "部署的URL")
	private Boolean deployurl;

	@Schema(description = "展示的URL")
	private Boolean showurl;

	@Schema(description = "下线的URL")
	private Boolean offlineurl;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @TableField(exist=false)
    private String userName;
}
