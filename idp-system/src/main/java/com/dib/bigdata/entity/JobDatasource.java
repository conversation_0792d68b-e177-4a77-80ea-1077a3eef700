package com.dib.bigdata.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.dib.bigdata.core.handler.AESEncryptHandler;
import com.dib.metadata.validate.ValidationGroups;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * jdbc数据源配置实体类(job_jdbc_datasource)
 *
 * <AUTHOR>
 * @version v1.0
 * @since 2019-07-30
 */

@Data
@Schema(description = "数据源Model")
@TableName("job_jdbc_datasource")
public class JobDatasource extends Model<JobDatasource> {

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "数据源ID")
    private Long id;

    /**
     * 数据源名称
     */
    @Schema(description = "数据源名称")
    private String datasourceName;

    /**
     * 数据源
     */
    @Schema(description = "数据源")
    private String datasource;

    /**
     * 数据源分组
     */
    @Schema(description = "数据源分组")
    private String datasourceGroup;

    /**
     * 用户名
     * AESEncryptHandler 加密类
     * MyBatis Plus *******之前版本没有typeHandler属性，需要升级到最低3.1.2
     */
    @Schema(description = "用户名")
    @TableField(typeHandler = AESEncryptHandler.class)
    private String jdbcUsername;

    /**
     * 密码
     */
    @TableField(typeHandler = AESEncryptHandler.class)
    @Schema(description = "密码")
    private String jdbcPassword;

    /**
     * jdbc url
     */
    @Schema(description = "jdbc url")
    private String jdbcUrl;

    /**
     * jdbc驱动类
     */
    @Schema(description = "jdbc驱动类")
    private String jdbcDriverClass;

    /**
     * 状态：0禁用 1启用
     */
    @Schema(description = "状态：0禁用 1启用")
    private Integer status;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人", hidden = true)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JSONField(format = "yyyy/MM/dd")
    @Schema(description = "创建时间", hidden = true)
    private Date createDate;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新人", hidden = true)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JSONField(format = "yyyy/MM/dd")
    @Schema(description = "更新时间", hidden = true)
    private Date updateDate;

    /**
     * 备注
     */
    @Schema(description = "备注", hidden = true)
    private String comments;

    /**
     * zookeeper地址
     */
    @Schema(description = "zookeeper地址", hidden = true)
    private String zkAdress;

    /**
     * 数据库名
     */
    @Schema(description = "数据库名", hidden = true)
    private String databaseName;

    /**
     * 数据库模式
     */
    @Schema(description = "模式名", hidden = true)
    @TableField(value = "sid")
    private String sid;


    /**
     * 系统id
     */
    @Schema(description = "系统id", hidden = true)
    private String sysId;


    @Schema(description = "主机")
    @NotBlank(message = "主机不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    @TableField(exist = false)
    private String host;


    @Schema(description = "端口")
    @NotNull(message = "端口不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    @TableField(exist = false)
    private Integer port;

    /**
     * 元数据同步（0否，1同步中, 2是）
     */
    @Schema(description = "元数据同步状态")
    @TableField(exist = false)
    private String isSync;


    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}