package com.dib.bigdata.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 */
@Data
public class DeployTask {

	@Schema(description = "任务ID")
	private String jid;

    @Schema(description = "作业名称")
    private String name;

    @Schema(description = "开始时间")
    private String begintime;

    @Schema(description = "持续时间")
    private String duration;

	@Schema(description = "结束时间")
	private String endtime;

	@Schema(description = "任务数")
	private String tasknumber;

	@Schema(description = "状态")
	private String status;
}
