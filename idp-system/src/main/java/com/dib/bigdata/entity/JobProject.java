package com.dib.bigdata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * Created by jingwk on 2022/05/24
 */
@Data
public class JobProject {

    @Schema(description = "项目ID")
    @TableId(value = "id",type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "项目名称")
    private String name;

    @Schema(description = "项目描述")
    private String description;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "标记")
    private Boolean flag;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @TableField(exist=false)
    private String userName;

}
