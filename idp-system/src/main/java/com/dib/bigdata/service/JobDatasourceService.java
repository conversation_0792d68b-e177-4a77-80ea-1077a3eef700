package com.dib.bigdata.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dib.bigdata.entity.JobDatasource;
import com.dib.metadata.entity.MetadataSourceEntity;
import com.dib.system.domain.vo.DataSourceSysVo;

import java.io.IOException;
import java.util.List;

/**
 * jdbc数据源配置表服务接口
 *
 * <AUTHOR>
 * @version v2.0
 * @since 2022-01-10
 */
public interface JobDatasourceService extends IService<JobDatasource> {
    /**
     * 测试数据源
     * @param jdbcDatasource
     * @return
     */
    Boolean dataSourceTest(JobDatasource jdbcDatasource) throws IOException;

    /**
     *更新数据源信息
     * @param datasource
     * @return
     */
    int update(JobDatasource datasource);

    /**
     * 获取所有数据源
     * @return
     */
    List<JobDatasource> selectAllDatasource();


    List<JobDatasource> findDataSourceName();

    JobDatasource getDataSourceById(Long datasourceId);

    Boolean saveJobDatasource(JobDatasource entity);

    Boolean updateJobDatasource(JobDatasource entity);

    Boolean removeJobDatasourceIds(List<Long> idList);

    IPage<JobDatasource> selectAll();

    List<JobDatasource> getSourceListBySysId(String sysId);
}