package com.dib.bigdata.util;


import com.dib.bigdata.service.RpcService;
import com.dib.common.config.RPCClient;
import java.io.IOException;
import java.net.InetSocketAddress;

public class TestOutput {

    public static void main(String[] args) throws IOException {
        for(int i=0;i<=5;i++) {
            RpcService service = RPCClient.getRemoteProxyObj(RpcService.class, new InetSocketAddress("***************", 8088));
            System.out.println(service.getMonitor());
        }
    }



}
