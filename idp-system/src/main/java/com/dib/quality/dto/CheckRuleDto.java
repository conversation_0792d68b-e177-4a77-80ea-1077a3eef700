package com.dib.quality.dto;

import com.dib.metadata.validate.ValidationGroups;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 核查规则信息表 实体DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-27
 */
@Schema(description = "校验规则DTO")
@Data
public class CheckRuleDto implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "规则ID")
    @NotBlank(message = "主键ID不能为空", groups = {ValidationGroups.Update.class})
    private String id;
    @Schema(description = "规则名称")
    private String ruleName;
    @Schema(description = "规则类型")
    private String ruleTypeId;
    @Schema(description = "核查类型")
    private String ruleItemId;
    @Schema(description = "规则级别（3高、2中、1低）")
    private String ruleLevelId;
    @Schema(description = "数据源类型")
    private String ruleDbType;
    @Schema(description = "数据源主键")
    private String ruleSourceId;
    @Schema(description = "数据源")
    private String ruleSource;
    @Schema(description = "数据表主键")
    private String ruleTableId;
    @Schema(description = "数据表")
    private String ruleTable;
    @Schema(description = "数据表名称")
    private String ruleTableComment;
    @Schema(description = "核查字段主键")
    private String ruleColumnId;
    @Schema(description = "核查字段")
    private String ruleColumn;
    @Schema(description = "核查字段名称")
    private String ruleColumnComment;
    @Schema(description = "核查配置")
    @Valid
    private RuleConfig ruleConfig;
    @Schema(description = "状态")
    @NotNull(message = "状态不能为空", groups = {ValidationGroups.Insert.class, ValidationGroups.Update.class})
    private String status;
    @Schema(description = "备注")
    private String remark;
}
