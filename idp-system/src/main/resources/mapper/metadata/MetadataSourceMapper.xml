<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dib.metadata.mapper.MetadataSourceDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dib.metadata.entity.MetadataSourceEntity">
        <result column="id" property="id" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="create_dept" property="createDept" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
        <result column="db_type" property="dbType" />
        <result column="db_desc" property="dbDesc" />
        <result column="source_name" property="sourceName" />
        <result column="is_sync" property="isSync" />
        <result column="sys_id" property="sysId" />
        <result column="db_schema" property="dbSchema"  typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <resultMap id="ExtendResultMap" type="com.dib.metadata.entity.MetadataSourceEntity" extends="BaseResultMap">
        <result column="db_schema" property="dbSchema" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        status,
        create_by,
        create_time,
        create_dept,
        update_by,
        update_time,
        remark,
        db_type,db_desc, source_name, is_sync, db_schema,sys_id
    </sql>

    <sql id="Source_Column_List">
        ${alias}.id,
        ${alias}.status,
        ${alias}.create_by,
        ${alias}.create_time,
        ${alias}.create_dept,
        ${alias}.update_by,
        ${alias}.update_time,
        ${alias}.remark,
        ${alias}.db_type,${alias}.db_type, ${alias}.source_name, ${alias}.is_sync, ${alias}.db_schema, ${alias}.sys_id
    </sql>


</mapper>
