<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dib.bigdata.mapper.DevJarMapper">


    <resultMap id="DevTask" type="com.dib.bigdata.entity.DevTask">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="tasktype" property="tasktype"/>
        <result column="runtype" property="runtype" />
        <result column="sql_text" property="sql_text" />
        <result column="run_param" property="run_param" />
        <result column="jarpath" property="jarpath" />
        <result column="task_describe" property="task_describe" />
        <result column="create_time" property="create_time" />
    </resultMap>

    <sql id="Show_Column_List">
		t.id,
		t.name,
		t.tasktype,
		t.runtype,
		t.sql_text,
		t.run_param,
		t.jarpath,
		t.task_describe,
		t.create_time
	</sql>

    <insert id="save" parameterType="com.dib.bigdata.entity.DevTask" useGeneratedKeys="true" keyProperty="id" >
		INSERT INTO lark_dev_tasklist (
			`name`,
			tasktype,
			runtype,
			run_param,
			jarpath,
			task_describe,
			create_time,
			`type`,
			sql_text
		) VALUES (
			#{name},
			#{tasktype},
			#{runtype},
			#{run_param},
			#{jarpath},
			#{task_describe},
			#{create_time},
			#{type},
			#{sql_text}
		);
	</insert>

    <update id="update" parameterType="com.dib.bigdata.entity.DevTask" >
        UPDATE lark_dev_tasklist
        SET
        `name` = #{name},
        tasktype = #{tasktype},
        runtype = #{runtype},
        run_param = #{run_param},
        jarpath = #{jarpath},
        task_describe = #{task_describe},
        create_time = #{create_time},
        sql_text= #{sql_text}
        WHERE id = #{id}
    </update>
    <select id="getById" parameterType="java.util.HashMap" resultMap="DevTask">
        SELECT <include refid="Show_Column_List" />
        FROM lark_dev_tasklist AS t
        WHERE t.id = #{id}
    </select>
</mapper>
