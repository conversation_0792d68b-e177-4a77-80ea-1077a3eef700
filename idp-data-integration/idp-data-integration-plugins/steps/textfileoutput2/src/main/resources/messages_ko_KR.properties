#File generated by Hitachi Vantara Translator for package 'org.pentaho.di.trans.steps.textfileoutput' in locale 'ko_KR'
#
#
#Mon Jun 15 16:02:49 KST 2009
TextFileOutputDialog.SelectOutputFiles.DialogMessage=\uCD9C\uB825 \uD30C\uC77C\:
TextFileOutputDialog.MinWidth.Button=\uCD5C\uC18C \uB108\uBE44(&M)
TextFileOutputDialog.Separator.Label=\uAD6C\uBD84\uC790
TextFileOutputDialog.ShowFiles.Button=\uD30C\uC77C\uC774\uB984 \uBCF4\uAE30(&S)...
TextFileOutputDialog.SpecifyFormat.Label=\uB0A0\uC9DC \uC2DC\uAC04 \uD615\uC2DD \uC9C0\uC815
TextFileOutputDialog.DialogTitle=Text file output
TextFileOutputDialog.NameColumn.Column=\uC774\uB984
TextFileOutputDialog.FastDump.Label=\uBE60\uB978 \uB370\uC774\uD130 \uB364\uD504(\uD615\uC2DD \uC5C6\uC74C)
TextFileOutputDialog.AddStepnr.Label=\uD30C\uC77C\uC774\uB984\uC5D0 Step \uBC88\uD638 \uD3EC\uD568?
TextFileOutputDialog.DecimalColumn.Column=\uC18C\uC218
TextFileOutputDialog.EnclForced.Label=\uD544\uB4DC \uC591\uCABD\uC5D0 \uC778\uD074\uB85C\uC800 \uAC15\uC81C \uCD94\uAC00?
TextFileOutputDialog.FieldsTab.TabTitle=\uD544\uB4DC
TextFileOutputDialog.Encoding.Label=\uC778\uCF54\uB529
TextFileOutputDialog.GroupColumn.Column=\uADF8\uB8F9
TextFileOutputDialog.DateTimeFormat.Label=\uB0A0\uC9DC \uC2DC\uAC04 \uD615\uC2DD
TextFileOutputDialog.EndedLine.Label=\uD30C\uC77C\uC5D0 \uC885\uB8CC\uB77C\uC778 \uCD94\uAC00
TextFileOutputDialog.TrimTypeColumn.Column=Trim \uD615\uC2DD
TextFileOutputDialog.MinWidth.Tooltip=\uD2B9\uC815 \uAE38\uC774\uB85C \uB9DE\uCD94\uC9C0 \uC54A\uC740 \uCD9C\uB825 \uC124\uC815.
TextFileOutputDialog.Separator.Button=TAB \uC785\uB825(&T)
TextFileOutputDialog.Header.Label=\uD5E4\uB354
TextFileOutputDialog.ContentTab.TabTitle=\uB0B4\uC6A9
TextFileOutputDialog.Footer.Label=\uAF2C\uB9AC\uB9D0
TextFileOutputDialog.FileNameInField.Label=\uD544\uB4DC\uC5D0\uC11C \uD30C\uC77C\uC774\uB984 \uAC00\uC838\uC624\uAE30?
TextFileOutputDialog.FailedToGetFields.DialogTitle=\uC624\uB958
TextFileOutputDialog.SplitEvery.Label=\uB9E4 ...\uB85C\uC6B0\uB9C8\uB2E4 \uBD84\uB9AC
TextFileOutputDialog.SpecifyFormat.Tooltip=\uB0A0\uC9DC \uC2DC\uAC04 \uD615\uC2DD \uC9C0\uC815
TextFileOutputDialog.FormatColumn.Column=\uD615\uC2DD
TextFileOutputDialog.CurrencyColumn.Column=\uD1B5\uD654
TextFileOutputDialog.Filename.Label=\uD30C\uC77C\uC774\uB984
TextFileOutputDialog.FileTab.TabTitle=\uD30C\uC77C
TextFileOutputDialog.LengthColumn.Column=\uAE38\uC774
TextFileOutputDialog.SelectOutputFiles.DialogTitle=\uCD9C\uB825 \uD30C\uC77C
TextFileOutputDialog.AddDate.Label=\uD30C\uC77C\uC774\uB984\uC5D0 \uB0A0\uC9DC \uD3EC\uD568?
TextFileOutputDialog.PrecisionColumn.Column=\uC815\uBC00\uB3C4
TextFileOutputDialog.FileNameField.Label=\uD30C\uC77C \uC774\uB984 \uD544\uB4DC
TextFileOutput.Exception.FileNameFieldNotFound=\uC785\uB825 \uC2A4\uD2B8\uB9BC\uC5D0\uC11C \uD30C\uC77C\uC774\uB984 \uD544\uB4DC [{0}]\uC744 \uCC3E\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4\!
TextFileOutputDialog.AddPartnr.Label=\uD30C\uC77C\uC774\uB984\uC5D0 \uD30C\uD2F0\uC158 \uBC88\uD638 \uD3EC\uD568?
TextFileOutputDialog.Pad.Label=\uC624\uB978\uCABD\uC744 \uCC44\uC6B4 \uD544\uB4DC
TextFileOutputDialog.AddFileToResult.Label=\uACB0\uACFC\uC5D0 \uD30C\uC77C\uC774\uB984 \uCD94\uAC00
TextFileOutputDialog.Compression.Label=\uC555\uCD95
TextFileOutputDialog.Append.Label=\uCD94\uAC00
TextFileOutputDialog.TypeColumn.Column=\uB370\uC774\uD130\uD615
TextFileOutputMeta.CheckResult.AllFieldsFound=\uC785\uB825 \uC2A4\uD2B8\uB9BC\uC5D0\uC11C \uBAA8\uB4E0 \uCD9C\uB825 \uD544\uB4DC\uB97C \uCC3E\uC558\uC2B5\uB2C8\uB2E4.
TextFileOutputDialog.DoNotOpenNewFileInit.Label=\uC2DC\uC791\uD560 \uB54C \uD30C\uC77C \uC0DD\uC131\uD558\uC9C0 \uC54A\uAE30
TextFileOutputDialog.NoFilesFound.DialogMessage=\uD30C\uC77C\uC774 \uC5C6\uC2B5\uB2C8\uB2E4\! \uD30C\uC77C\uC774\uB984\uACFC \uB514\uB809\uD1A0\uB9AC \uADF8\uB9AC\uACE0 \uC635\uC158\uC744 \uD655\uC778\uD558\uC2ED\uC2DC\uC624.
TextFileOutputDialog.Enclosure.Label=\uC778\uD074\uB85C\uC800 
TextFileOutputDialog.AddTime.Label=\uD30C\uC77C\uC774\uB984\uC5D0 \uC2DC\uAC04 \uD3EC\uD568?
TextFileOutputDialog.NullColumn.Column=Null
TextFileOutputDialog.Format.Label=\uD615\uC2DD
TextFileOutputDialog.DoNotOpenNewFileInit.Tooltip=Transformation \uC2DC\uC791\uD560 \uB54C \uD30C\uC77C\uC744 \uC0DD\uC131\uD558\uC9C0 \uC54A\uC73C\uB824\uBA74 \uC774 \uC635\uC158 \uCCB4\uD06C.\r\nPDI\uB294 \uCCAB\uBC88\uC9F8 \uB85C\uC6B0\uB97C \uBC1B\uC73C\uBA74 \uD30C\uC77C\uC744 \uC0DD\uC131\uD569\uB2C8\uB2E4.
