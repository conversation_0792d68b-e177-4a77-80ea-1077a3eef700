<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>idp-data-integration-plugins</artifactId>
        <groupId>com.dib</groupId>
        <version>3.8.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>dm-database</artifactId>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!--核心依赖包-->
        <dependency>
            <groupId>com.dib</groupId>
            <artifactId>idp-data-integration-api</artifactId>
            <version>3.8.2</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.dib</groupId>-->
<!--            <artifactId>idp-kettle-export</artifactId>-->
<!--            <version>3.8.2</version>-->
<!--            <type>pom</type>-->
<!--        </dependency>-->
        <dependency>
            <groupId>pentaho-kettle</groupId>
            <artifactId>kettle-core</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../../idp-kettle-export/ext-lib/kettle-core-9.4.0.0-343.jar</systemPath>
        </dependency>
        <!-- https://mvnrepository.com/artifact/pentaho-kettle/kettle-engine -->
        <dependency>
            <groupId>pentaho-kettle</groupId>
            <artifactId>kettle-engine</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../../idp-kettle-export/ext-lib/kettle-engine-9.4.0.0-343.jar</systemPath>
        </dependency>

        <!-- 达梦数据库驱动 -->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
            <version>*********</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.alipay.sofa</groupId>
                <artifactId>sofa-ark-plugin-maven-plugin</artifactId>
                <version>1.1.0</version>
                <executions>
                    <execution>
                        <id>default-cli</id>
                        <goals>
                            <goal>ark-plugin</goal>
                        </goals>

                        <configuration>
                            <!-- configure exported class -->
                            <classifier>ark-plugin</classifier>
                            <exported>
                                <!-- configure class-level exported class -->
                                <packages>
                                    <package>org.pentaho.di.core.database</package>
                                </packages>
                            </exported>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
