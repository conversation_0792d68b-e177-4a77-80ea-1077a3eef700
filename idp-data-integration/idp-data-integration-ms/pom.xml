<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dib</groupId>
        <artifactId>idp-data-integration</artifactId>
        <version>3.8.2</version>
    </parent>

    <artifactId>idp-data-integration-ms</artifactId>
    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.dib</groupId>
            <artifactId>idp-common</artifactId>
            <version>3.8.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsqlparser</artifactId>
                    <groupId>com.github.jsqlparser</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatisplus-spring-boot-starter</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-plus-extension</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-plus-core</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-plus-annotation</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <!-- 排除 SLF4J reload4j 实现，避免与 Logback 冲突 -->
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <!-- 排除 Log4j SLF4J 实现，避免与 Logback 冲突 -->
                <exclusion>
                    <artifactId>log4j-slf4j-impl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dib</groupId>
            <artifactId>idp-data-integration-api</artifactId>
            <version>3.8.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp3</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>idp-system</artifactId>
                    <groupId>com.dib</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>idp-kettle-export</artifactId>
                    <groupId>com.dib</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>feign-core</artifactId>
                    <groupId>io.github.openfeign</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-spring-boot.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>4.0.18</version>
        </dependency>
        <!--        &lt;!&ndash;插件 begin&ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>csvinput2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>excelinput2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>textfileinput2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>textfileoutput2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>exceloutput2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>typeexitexcelwriterstep2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>ssh2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>http2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>parallelGzipCsvInput2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>getFileNames2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>getFilesRowsCount2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>cubeInput2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>sqlFileOutput2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>getSubFolders2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>execSQLRow2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>fixedInput2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>FileExists2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>fileLocked2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>joinRows2</artifactId>-->
        <!--            <classifier>ark-plugin</classifier>-->
        <!--            <version>${project.version}</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.dib</groupId>
            <artifactId>dm-database</artifactId>
            <version>3.8.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>2.0.52</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-redis</artifactId>
            <version>5.5.10</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.2</version>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-sync</artifactId>
            <version>4.7.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>bson</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mongodb-driver-core</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-core</artifactId>
            <version>4.7.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>bson</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>bson</artifactId>
            <version>4.7.1</version>
        </dependency>
<!--        <dependency>
            <groupId>com.dib</groupId>
            <artifactId>idp-system</artifactId>
            <version>3.8.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>mongo-java-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>quartz</artifactId>
                    <groupId>org.quartz-scheduler</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>snappy-java</artifactId>
                    <groupId>org.xerial.snappy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>-->
        <!--插件 end-->

        <!-- Java 11 所需的 JavaMail 依赖 -->
<!--        <dependency>-->
<!--            <groupId>com.sun.mail</groupId>-->
<!--            <artifactId>javax.mail</artifactId>-->
<!--            <version>1.6.2</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>javax.activation</groupId>-->
<!--            <artifactId>activation</artifactId>-->
<!--            <version>1.1.1</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.sun.activation</groupId>-->
<!--            <artifactId>javax.activation</artifactId>-->
<!--            <version>1.2.0</version>-->
<!--        </dependency>-->


        <!-- kettle start -->
        <!-- https://mvnrepository.com/artifact/pentaho-kettle/kettle-core -->
        <dependency>
            <groupId>pentaho-kettle</groupId>
            <artifactId>kettle-core</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/kettle-core-9.4.0.0-343.jar</systemPath>
        </dependency>
        <!-- https://mvnrepository.com/artifact/pentaho-kettle/kettle-engine -->
        <dependency>
            <groupId>pentaho-kettle</groupId>
            <artifactId>kettle-engine</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/kettle-engine-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho</groupId>
            <artifactId>pentaho-encryption-support</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/pentaho-encryption-support-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>pentaho-kettle</groupId>
            <artifactId>metastore</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/metastore-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho.di.plugins</groupId>
            <artifactId>kettle-json-plugin-core</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/kettle-json-plugin-core-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho.di.plugins</groupId>
            <artifactId>elasticsearch-bulk-insert-core</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/elasticsearch-bulk-insert-core-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho.di.plugins</groupId>
            <artifactId>pdi-core</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/pdi-core-plugins-impl-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>pentaho</groupId>
            <artifactId>pentaho-big-data-kettle-plugins-kafka</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/pentaho-big-data-kettle-plugins-kafka-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>pentaho</groupId>
            <artifactId>pentaho-big-data-legacy-core</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/pentaho-big-data-legacy-core-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho</groupId>
            <artifactId>shim-api</artifactId>
            <version>9.4.0.0-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/shim-api-9.4.0.0-20240405.134336-1.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>pentaho</groupId>
            <artifactId>pentaho-metastore-locator-api</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/pentaho-metastore-locator-api-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho.di.plugins</groupId>
            <artifactId>pentaho-streaming-jms-plugin</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/pentaho-streaming-jms-plugin-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>pentaho</groupId>
            <artifactId>pentaho-mongodb-plugin</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/pentaho-mongodb-plugin-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>pentaho</groupId>
            <artifactId>pentaho-mongo-utils</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/pentaho-mongo-utils-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho.di.plugins</groupId>
            <artifactId>excel-plugins-core</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/excel-plugins-core-9.4.0.0-343.jar</systemPath>
        </dependency>

        <!-- postgresql插件使用 -->

        <dependency>
            <groupId>com.enterprisedt</groupId>
            <artifactId>edtftpj</artifactId>
            <version>2.1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/edtftpj-2.1.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho.reporting.library</groupId>
            <artifactId>libformula</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/libformula-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho.reporting.library</groupId>
            <artifactId>libbase</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../idp-kettle-export/ext-lib/libbase-9.4.0.0-343.jar</systemPath>
        </dependency>
<!--        定时任务插件-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
            <version>${spring-boot.version}</version>
        </dependency>
        <!-- ... existing code ... -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-vfs2</artifactId>
            <version>2.9.0</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>${mybatis-spring.version}</version>
        </dependency>
        <!-- 添加 JSqlParser 依赖 -->
<!--        <dependency>-->
<!--            <groupId>com.github.jsqlparser</groupId>-->
<!--            <artifactId>jsqlparser</artifactId>-->
<!--            <version>4.6</version>-->
<!--        </dependency>-->
        <!-- ... existing code ... -->

        <!-- SpringDoc OpenAPI -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.1.9</version>
                <configuration>
                    <!-- 这个配置项默认为 true，确保 system scope 的 JAR 被包含 -->
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                             <!-- 这个 goal 负责创建包含依赖的可执行 JAR -->
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>
</project>