package com.dib.dataIntegration.handler;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import com.dib.common.core.domain.AjaxResult;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Sa-Token 全局异常处理
 */
@RestControllerAdvice
public class SaTokenExceptionHandler {

    /**
     * 处理未登录异常
     */
    @ExceptionHandler(NotLoginException.class)
    public AjaxResult handleNotLoginException(NotLoginException e) {
        // 判断场景值，定制化异常信息
        String message;
        if(e.getType().equals(NotLoginException.NOT_TOKEN)) {
            message = "未提供token";
        } else if(e.getType().equals(NotLoginException.INVALID_TOKEN)) {
            message = "token无效";
        } else if(e.getType().equals(NotLoginException.TOKEN_TIMEOUT)) {
            message = "token已过期";
        } else if(e.getType().equals(NotLoginException.BE_REPLACED)) {
            message = "token已被顶下线";
        } else if(e.getType().equals(NotLoginException.KICK_OUT)) {
            message = "token已被踢下线";
        } else {
            message = "当前会话未登录";
        }
        return AjaxResult.error(401, message);
    }

    /**
     * 处理无权限异常
     */
    @ExceptionHandler(NotPermissionException.class)
    public AjaxResult handleNotPermissionException(NotPermissionException e) {
        return AjaxResult.error(403, "无此权限：" + e.getPermission());
    }

    /**
     * 处理无角色异常
     */
    @ExceptionHandler(NotRoleException.class)
    public AjaxResult handleNotRoleException(NotRoleException e) {
        return AjaxResult.error(403, "无此角色：" + e.getRole());
    }
}
