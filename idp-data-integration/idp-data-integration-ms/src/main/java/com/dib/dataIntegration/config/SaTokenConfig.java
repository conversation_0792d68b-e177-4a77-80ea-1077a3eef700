package com.dib.dataIntegration.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
public class SaTokenConfig implements WebMvcConfigurer {
    //
//    @Autowired
//    private Environment environment;

    @Value("${dibase.login-exclude-url}")
    private String[] loginExcludeUrl;

    /**
     * 拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        List<String> list = new ArrayList<>(Arrays.asList(loginExcludeUrl));

        // 注册 Sa-Token 拦截器，校验规则为 StpUtil.checkLogin() 登录校验。
        registry.addInterceptor(new SaInterceptor(handle -> {
                    // 登录校验 -- 拦截所有路由，并排除登录相关接口
                    StpUtil.checkLogin();
                }))
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(list); // 放行登录接口

    }
}
