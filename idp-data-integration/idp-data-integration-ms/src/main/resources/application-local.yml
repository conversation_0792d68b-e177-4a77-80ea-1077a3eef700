server:
  port: 10321

management:
  endpoints:
    web:
      exposure:
        include: "*"
  metrics:
    enable:
      mongodb: false
  health:
    rabbit:
      enabled: false
    mail:
      enabled: false
    mongo:
      enabled: false

spring:
  application:
    name: idp-dataIntegration-ms
  datasource:
    username: SYSDBA
    password: SYSDBA001
    url: jdbc:dm://**************:30236?schema=idp_ms
    driver-class-name: dm.jdbc.driver.DmDriver
  druid:
    initialSize: 5
    minIdle: 10
    maxActive: 20
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    maxEvictableIdleTimeMillis: 900000
    validationQuery: SELECT version()
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    webStatFilter:
      enabled: true
    statViewServlet:
      enabled: true
      allow:
      url-pattern: /druid/*
      login-username: admin
      login-password: 123456
    filter:
      stat:
        enabled: true
        log-slow-sql: true
        slow-sql-millis: 1000
        merge-sql: true
      wall:
        config:
          multi-statement-allow: true

  mail:
    host: smtp
    port: 465
    username:
    password:
    authorization: kwzfbpemiqzwebgf
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
        socketFactory:
          class: javax.net.ssl.SSLSocketFactory

  messages:
    basename: i18n/messages
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 1000MB
  devtools:
    restart:
      enabled: true
  jackson:
    deserialization:
      fail-on-unknown-properties: false

  quartz:
    job-store-type: jdbc
    jdbc:
      initialize-schema: never
    scheduler-name: TaskFlowScheduler
    startup-delay: 1
    overwrite-existing-jobs: true
    auto-startup: true
    wait-for-jobs-to-complete-on-shutdown: true
    properties:
      org.quartz.jobStore.dataSource: default
      org.quartz.scheduler.instanceName: TaskFlowScheduler
      org.quartz.scheduler.instanceId: AUTO
      org.quartz.jobStore.class: org.quartz.impl.jdbcjobstore.JobStoreTX
      org.quartz.jobStore.driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
      org.quartz.jobStore.useProperties: false
      org.quartz.jobStore.tablePrefix: QRTZ_
      org.quartz.jobStore.isClustered: true
      org.quartz.jobStore.clusterCheckinInterval: 15000
      org.quartz.jobStore.maxMisfiresToHandleAtATime: 1
      org.quartz.jobStore.txIsolationLevelSerializable: true
      org.quartz.jobStore.misfireThreshold: 12000
      org.quartz.threadPool.class: org.quartz.simpl.SimpleThreadPool
      org.quartz.threadPool.threadCount: 20
      org.quartz.threadPool.threadPriority: 5

  data:
    redis:
      host: ***********
      port: 6379
      password: lzx1024
      lettuce:
        shutdown-timeout: 100
        pool:
          max-active: 8
          max-idle: 8
          max-wait: 30
          min-idle: 0

mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  global-config:
    db-config:
      id-type: AUTO
      field-strategy: NOT_NULL
      column-underline: true
      logic-delete-value: 1
      logic-not-delete-value: 0
      db-type: DM8
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'

mybatis:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.dib.**.domain
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

dp:
  async:
    taskExecutor:
      corePoolSize: 16
      maxPoolSize: 16
      queueCapacity: 200
      keepAliveSeconds: 60
  savemodel:
    taskExecutor:
      corePoolSize: 10
      maxPoolSize: 200
      queueCapacity: 200
      keepAliveSeconds: 60
  project:
    execute:
      deleteTmpFile: false
      defaultDataSource: ENGINE_CLICKHOUSE
    restExecute:
      deleteTmpFile: false
      defaultDataSource: ENGINE_CLICKHOUSE
    datasource:
      maxWaitTimeout: 10000
      maxFreeTimeMinute: 180
      clearCron: 0 7 */1 * * ?
  preview:
    downloadMaxRow: 600000

kettle:
  datasource:
    engine:
      metadata:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: *****************************************************************************************************
        username: root
        password: Dib@2024
        hikari:
          minimum-idle: 1
          maximum-pool-size: 10
          max-lifetime: 1800000
          idle-timeout: 600000
          connection-timeout: 30000
          connection-test-query: SELECT 1
  engine:
    name: Pentaho local
    thread:
      corePoolSize: 10
      maximumPoolSize: 200
      keepAliveTime: 60
    variable:
      systemVariables:
        - name: batchDay
          desc: "批量日期(前一天，格式：yyyy-MM-dd)"
          expression: date_to_string(addDate(sysdate(),5,-1),"yyyy-MM-dd")
          formatSting: yyyy-MM-dd
        - name: batchDateTime
          desc: "批量日期时间戳(前一天，格式：yyyy-MM-dd HH:mm:ss)"
          expression: date_to_string(addDate(sysdate(),5,-1),"yyyy-MM-dd HH:mm:ss")
          formatSting: yyyy-MM-dd HH:mm:ss
        - name: currentDay
          desc: "当前日期(格式：yyyy-MM-dd)"
          expression: date_to_string(sysdate(),"yyyy-MM-dd")
          formatSting: yyyy-MM-dd
        - name: currentDateTime
          desc: "当前日期时间戳(格式：yyyy-MM-dd HH:mm:ss)"
          expression: date_to_string(sysdate(),"yyyy-MM-dd HH:mm:ss")
          formatSting: yyyy-MM-dd HH:mm:ss
  global:
    map:
      sql.engine.db.inputPlugin.schema: tmp
      sql.engine.db.temp.schema: tmp
      sql.engine.db.saveModel.schema: pdi
      sql.engine.db.insert.batchSize: 1000
      sql.engine.db.cache.customizeTmpTable: true

ribbon:
  ConnectTimeout: 10000
  ReadTimeout: 10000

jasypt:
  encryptor:
    password: fdasjfldjskfkf787fda9r321

file:
  server:
    useServer: minio
    extendsFileType: s3
    ftp:
      server: 192.168.124.166
      port: 20021
      userName: prime
      password: prime@2020
      passiveMode: true
      ftpEncoding: utf-8
      rootPath: ftp://
      createDir: true
    s3:
      server: **************
      port: 38000
      userName: BMXG3WP8JA9D1GSD2AJJ
      password: vl32x2t0sBxy0BEgcY9Iz442HK2HobPTNw4T99yK
      rootPath: s3://
      bucket: escat-s3
      createDir: true
    minIos3:
      server: **************
      port: 9001
      url: http://**************:9001
      accessKey: ZbqKyY1vqAb41bnk6cMj
      secretKey: YBKE7wWTfO1LeDJJOqxCdabT8KFIJIie6QsoDvXs
      bucketName: eip-test
      rootPath: s3://

pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

swagger:
  enabled: true
  pathMapping: /dev-api

xss:
  enabled: true
  excludes: /system/notice
  urlPatterns: /system/*,/monitor/*,/tool/*

datasource:
  aes:
    key: AD42F6697B035B75
  types: {
      MYSQL: 'com.mysql.cj.jdbc.Driver',
      ORACLE: 'oracle.jdbc.driver.OracleDriver',
      SQLSERVER: 'com.microsoft.sqlserver.jdbc.SQLServerDriver',
      POSTGRESQL: 'org.postgresql.Driver',
      TERADATA: 'com.ncr.teradata.TeraDriver',
      DB2: 'com.ibm.db2.jcc.DB2Driver',
      CLICKHOUSE: 'ru.yandex.clickhouse.ClickHouseDriver'
    }

mysql:
  data:
    type: {'bit','tinyint','smallint','mediumini','int','integer','bigint','decimal','numeric','float','double'}

superset:
  sqllabUrl: http://*************:8088/superset/sqllab_viz/
  getAllDataSources: http://*************:8088/databaseasync/api/read?_flt_0_expose_in_sqllab=1&_oc_DatabaseAsync=database_name&_od_DatabaseAsync=asc
  forwardUrl: http://*************:8088/superset/explore/table/
  sqlForwardUrl: http://cloud.vincenthsing.top:8082/superset/explore/?form_data=

restTemplate:
  poolSize: 200
  perRouteSize: 50
  connectTimeout: 3
  readTimeut: 3
  waitTimeout: 3