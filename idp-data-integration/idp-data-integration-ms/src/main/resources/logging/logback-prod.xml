<configuration>
    <!-- 引用yml配置中的变量值 -->
    <springProperty scope="context" name="LOG_HOME" source="log.file.root"/>
    <springProperty scope="context" name="LOG_SIZE" source="log.file.maxSize"/>
    <springProperty scope="context" name="LOG_NUM" source="log.file.maxHistory"/>
    <springProperty scope="context" name="APP_NAME" source="app.app-name"/>
    <springProperty scope="context" name="APP_NAME_NODE" source="app.app-name-node"/>

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50}[%M:%L] %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="PROJECT" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/info.log</file>
        <!-- <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter> -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件输出的文件名 -->
            <fileNamePattern>${LOG_HOME}/old/info.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!--日志文件最大的大小,当超过maxFileSize中指定大大小时，文件名中的变量%i会加一-->
                <maxFileSize>${LOG_SIZE}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!--日志文件保留天数-->
            <maxHistory>${LOG_NUM}</maxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50} - %M:%L] %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/error.log</file>
        <!-- 过滤器，只记录ERROR级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <!-- 最常用的滚动策略，它根据时间来制定滚动策略.既负责滚动也负责出发滚动 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/old/error.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!--日志文件最大的大小,当超过maxFileSize中指定大大小时，文件名中的变量%i会加一-->
                <maxFileSize>${LOG_SIZE}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!--日志文件保留天数-->
            <maxHistory>${LOG_NUM}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger - %M:%L] %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="org" level="INFO"/>
    <logger name="com.alibaba" level="INFO"/>
    <logger name="springfox" level="INFO"/>

    <!--myibatis log configure-->
    <logger name="jdbc.sqlonly" level="OFF"/>
    <logger name="jdbc.sqltiming" level="OFF"/>
    <logger name="jdbc.audit" level="OFF"/>
    <logger name="jdbc.resultset" level="OFF"/>
    <logger name="jdbc.resultsettable" level="OFF"/>
    <logger name="jdbc.connection" level="OFF"/>
    <logger name="com.apache.ibatis" level="${log.sql.level}"/>
    <logger name="com.ibatis" level="${log.sql.level}"/>
    <logger name="java.sql.Connection" level="${log.sql.level}"/>
    <logger name="java.sql.Statement" level="${log.sql.level}"/>
    <logger name="java.sql.PreparedStatement" level="${log.sql.level}"/>
    <logger name="java.sql.ResultSet" level="${log.sql.level}"/>
    <logger name="org.dozer" level="INFO"/>
    <logger name="net.rubyeye.xmemcached" level="INFO"/>
    <logger name="com.google.code" level="INFO"/>

    <root level="INFO">
        <appender-ref ref="PROJECT"/>
        <appender-ref ref="ERROR"/>
    </root>
</configuration>

