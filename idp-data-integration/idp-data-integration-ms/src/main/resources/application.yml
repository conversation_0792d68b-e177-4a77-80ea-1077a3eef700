spring:
  main:
    allow-bean-definition-overriding: true
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.metrics.mongo.MongoMetricsAutoConfiguration
      - org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
      - org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration
      - org.springframework.boot.autoconfigure.mongo.MongoReactiveAutoConfiguration
      - org.springframework.boot.autoconfigure.data.mongo.MongoRepositoriesAutoConfiguration
      - org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration
      - org.springframework.boot.autoconfigure.mail.MailSenderAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.amqp.RabbitHealthContributorAutoConfiguration
  #      - org.springframework.boot.actuate.autoconfigure.mail.MailHealthIndicatorAutoConfiguration
  # 启用CGLIB代理
  aop:
    proxy-target-class: true
  logging:
    level:
      cn.dev33.satoken: debug

# 禁用Spring Security
security:
  basic:
    enabled: false
  ignored: /**

# 不要完全禁用AOP，只修改代理模式
# spring.aop.auto: false

# Java 11 模块化系统配置
server:
  tomcat:
    additional-jvm-options:
      - --add-opens=java.base/java.lang=ALL-UNNAMED
      - --add-opens=java.base/java.io=ALL-UNNAMED
      - --add-opens=java.base/java.util=ALL-UNNAMED
      - --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
      - --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED

sa-token:
  token-name: Authorization
  timeout: 2592000
  maxLoginCount: -1
  active-timeout: 14400  # 4小时无操作失效
  is-concurrent: true
  is-share: false
  token-style: simple-uuid
  is-log: true
  token-prefix: Bearer
  is-read-cookie: false
  # 配置Sa-Token单独使用的rendis连接
  alone-redis :
    host: *************
    password: Dib123#@!
    port: 6379
    database: 0
    timeout: 10s

dibase:
  login-exclude-url: /login