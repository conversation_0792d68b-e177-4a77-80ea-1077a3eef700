package com.dib.dataIntegration.run.steps.gzipCsvInput.api;

import com.dib.dataIntegration.core.model.Result;
import com.dib.dataIntegration.run.steps.csvinput.vo.FieldVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.pentaho.di.core.exception.KettleException;
import org.pentaho.metastore.api.exceptions.MetaStoreException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Tag(name = "gzip csv input输入接口")
public interface GzipCsvInputApi {

    @Operation(summary = "预览数据", description = "application/json")
    @PostMapping(value = "/getData")
    Result<Map<String, Object>, Object> getData(@RequestHeader("authorization-userId") String userId
            , @RequestBody String json, @RequestParam String count) throws KettleException, IOException, MetaStoreException;

    @Operation(summary = "获取字段", description = "application/json")
    @PostMapping(value = "/getColumns")
    Result<List<FieldVO>, Object> getColumns(@RequestHeader("authorization-userId") String userId
            , @RequestBody String json) throws KettleException, IOException, MetaStoreException;

}
