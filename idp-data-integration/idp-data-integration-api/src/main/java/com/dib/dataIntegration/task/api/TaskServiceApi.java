package com.dib.dataIntegration.task.api;

import com.dib.dataIntegration.core.model.Result;
import com.dib.dataIntegration.task.dto.GroupDTO;
import com.dib.dataIntegration.task.dto.TaskDTO;
import com.dib.dataIntegration.task.dto.TaskFileDTO;
import com.dib.dataIntegration.task.dto.UserGroupDTO;
import com.dib.dataIntegration.task.entity.DpPortalTask;
import com.dib.dataIntegration.task.vo.TaskAddVO;
import com.dib.dataIntegration.task.vo.TaskTreeVO;
import com.dib.dataIntegration.task.vo.TaskUpdateVO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 任务管理API接口.
 */
@Api(tags = "任务管理API接口")
public interface TaskServiceApi {

    /**
     * 新增任务.
     *
     * @param userId     userId
     * @param userName   userName
     * @param taskAddVO  taskAddVO
     * @return 接口应答
     */
    @ApiOperation(value = "新增任务", notes = "content返回的是任务id", produces = "application/json")
    @PostMapping(value = "/add")
    Result<DpPortalTask, Object> add(@RequestBody TaskAddVO taskAddVO);

    /**
     * 下载任务.
     *
     * @param taskId taskId
     * @param response  response
     **/
    @ApiOperation(value = "下载任务", notes = "返回的是文件流", produces = "application/json")
    @GetMapping(value = "/download")
    void download(@RequestParam("taskId") String taskId, HttpServletResponse response);

    /**
     * 修改任务.
     *
     * @param userId          userId
     * @param userName        userName
     * @param taskUpdateVO    taskUpdateVO
     * @return 接口应答
     */
    @ApiOperation(value = "修改任务", notes = "参数对象中如果有null值，数据库会被更新成null", produces = "application/json")
    @PostMapping(value = "/update")
    Result<Boolean, Object> update(@RequestBody TaskUpdateVO taskUpdateVO);

    /**
     * 修改任务,选择性修改.
     *
     * @param userId          userId
     * @param userName        userName
     * @param taskUpdateVO    taskUpdateVO
     * @return 接口应答
     */
    @ApiOperation(value = "修改任务，选择性修改", notes = "参数对象中如果有null值，数据库将不会被更新", produces = "application/json")
    @PostMapping(value = "/updateSelective")
    Result<String, Object> updateSelective(@RequestBody TaskUpdateVO taskUpdateVO);

    /**
     * 删除任务.
     *
     * @param taskId 任务id
     * @return 接口应答
     */
    @ApiOperation(value = "删除任务", notes = "根据id删除任务", produces = "application/json")
    @PostMapping(value = "/delete")
    Result<Boolean, Object> delete(@RequestParam("taskId") String taskId);

    /**
     * 删除任务.
     *
     * @param taskIds 任务id 数组
     * @return 接口应答
     */
    @ApiOperation(value = "删除任务", notes = "根据id删除任务", produces = "application/json")
    @PostMapping(value = "/deleteBatch")
    Result<Boolean, Object> deleteBatch(@ApiParam("任务id 数组") @RequestBody List<String> taskIds);


    /**
     * 根据id获取任务.
     *
     * @param taskId 任务id
     * @return 接口应答
     */
    @ApiOperation(value = "获取任务", notes = "根据id获取任务", produces = "application/json")
    @GetMapping(value = "/get/{taskId}")
    Result<TaskDTO, Object> get(@PathVariable("taskId") String taskId);

    /**
     * 根据id获取任务附件.
     *
     * @param taskId 任务id
     * @return 接口应答
     */
    @ApiOperation(value = "获取任务", notes = "根据id获取任务附件", produces = "application/json")
    @GetMapping(value = "/getTaskFile/{taskId}")
    Result<TaskFileDTO, Object> getTaskFile(@PathVariable("taskId") String taskId);

    /**
     * 根据任务名称模糊查询指任务信息.
     *
     * @param taskType  taskType
     * @param userId    userId
     * @param taskName  任务名称
     * @param pageNum   页数
     * @param pageSize  每页记录数
     * @return 接口应答
     */
    @ApiOperation(value = "根据任务名称模糊查询指任务信息", produces = "application/json")
    @GetMapping(value = "/selectLikeByTaskName")
    Result<PageInfo<TaskDTO>, Object> selectLikeByTaskName(@ApiParam("任务名称，模糊查询") @RequestParam(value = "taskName", required = false) String taskName,
                                                           @ApiParam("任务类型，（使用拼音简写）：探索脚本(TSJB)、集成脚本(JSJB)、报表(BB)") @RequestParam(value = "taskType") String taskType,
                                                           @ApiParam("页数") @RequestParam("pageNum") int pageNum,
                                                           @ApiParam("每页记录数") @RequestParam("pageSize") int pageSize);

    /**
     * 根据任务名称模糊查询指定分组下的任务信息.
     *
     * @param taskType  taskType
     * @param userId    userId
     * @param taskName  分组名称
     * @param groupId   分组编号
     * @param pageNum   页数
     * @param pageSize  每页记录数
     * @return 接口应答
     */
    @ApiOperation(value = "根据分组名称注模糊查询指定分组下的任务信息", produces = "application/json")
    @GetMapping(value = "/selectLikeByTaskName/{groupId}")
    Result<PageInfo<TaskDTO>, Object> selectLikeByGroupId(@ApiParam("任务名称，模糊查询") @RequestParam(value = "taskName", required = false) String taskName,
                                                          @ApiParam("分组编号") @PathVariable("groupId") String groupId,
                                                          @ApiParam("任务类型，（使用拼音简写）：探索脚本(TSJB)、集成脚本(JSJB)、报表(BB)") @RequestParam(value = "taskType") String taskType,
                                                          @ApiParam("页数") @RequestParam("pageNum") int pageNum,
                                                          @ApiParam("每页记录数") @RequestParam("pageSize") int pageSize);

    /**
     * 根据任务名称模糊查询 我的任务.
     *
     * @param groupName 分组名称
     * @param taskType  任务类型
     * @param userId    用户ID
     * @param taskName  任务名称
     * @param pageNum   页数
     * @param pageSize  每页记录数
     * @return 接口应答
     */
    @ApiOperation(value = "根据任务名称模糊查询 我的任务", produces = "application/json")
    @GetMapping(value = "/selectMyTask")
    Result<PageInfo<GroupDTO>, Object> selectMyTask(@ApiParam("任务名称，模糊查询") @RequestParam(value = "taskName", required = false) String taskName,
                                                    @RequestParam(value = "groupName", required = false) String groupName,
                                                    @ApiParam("任务类型，（使用拼音简写）：探索脚本(TSJB)、集成脚本(JSJB)、报表(BB)") @RequestParam(value = "taskType") String taskType,
                                                    @ApiParam("页数") @RequestParam("pageNum") int pageNum, @ApiParam("每页记录数") @RequestParam("pageSize") int pageSize);

    /**
     * 根据任务名称模糊查询 我的任务.
     *
     * @param taskType  任务类型
     * @param userId    用户ID
     * @param taskName  任务名称
     * @param pageNum   页数
     * @param pageSize  每页记录数
     * @return 接口应答
     */
    @ApiOperation(value = "根据任务名称模糊查询 我的任务", produces = "application/json")
    @GetMapping(value = "/selectAllTask")
    Result<PageInfo<GroupDTO>, Object> selectAllTask(@ApiParam("任务名称，模糊查询") @RequestParam(value = "taskName", required = false) String taskName,
                                                     @ApiParam("任务类型，（使用拼音简写）：探索脚本(TSJB)、集成脚本(JSJB)、报表(BB)")
                                                     @RequestParam(value = "taskType") String taskType,
                                                     @ApiParam("页数") @RequestParam("pageNum") int pageNum, @ApiParam("每页记录数") @RequestParam("pageSize") int pageSize);

    /**
     * 根据任务名称模糊查询 授权给我的任务.
     *
     * @param taskType  任务类型
     * @param userId    用户ID
     * @param taskName  任务名称
     * @param pageNum   页数
     * @param pageSize  每页记录数
     * @return 接口应答
     */
    @ApiOperation(value = "根据任务名称模糊查询 授权给我的任务", produces = "application/json")
    @GetMapping(value = "/selectGrantMyTask")
    Result<PageInfo<UserGroupDTO>, Object> selectGrantMyTask(@ApiParam("任务名称，模糊查询") @RequestParam(value = "taskName", required = false) String taskName,
                                                             @ApiParam("任务类型，（使用拼音简写）：探索脚本(TSJB)、集成脚本(JSJB)、报表(BB)") @RequestParam(value = "taskType") String taskType,
                                                             @ApiParam("页数") @RequestParam("pageNum") int pageNum,
                                                             @ApiParam("每页记录数") @RequestParam("pageSize") int pageSize);


    @ApiOperation(value = "获取任务列表（含分组）", produces = "application/json")
    @PostMapping(value = "/listByGroup")
    Result<List<TaskTreeVO>, Object> listByGroup(@RequestBody TaskTreeVO taskTreeVO);


    /**
     * 发布任务
     *
     * @param taskId 任务id
     * @return 接口应答
     */
    @ApiOperation(value = "发布任务", notes = "将任务复制一份到上线任务表，并将状态改为已发布", produces = "application/json")
    @PostMapping(value = "/publish")
    Result<Boolean, Object> publishTask(@RequestParam("taskId") String taskId);
} 