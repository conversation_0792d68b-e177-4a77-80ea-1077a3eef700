package com.dib.dataIntegration.task.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户分组DTO.
 */
@ApiModel("用户分组信息")
@Data
public class UserGroupDTO {

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "用户名称")
    private String describe;

    @ApiModelProperty(value = "我的项目")
    private List<GroupDTO> projects = new ArrayList<>();

    /**
     * add .
     *
     * @param groupDTO groupDTO
     */
    public void add(GroupDTO groupDTO) {
        projects.add(groupDTO);
    }
} 