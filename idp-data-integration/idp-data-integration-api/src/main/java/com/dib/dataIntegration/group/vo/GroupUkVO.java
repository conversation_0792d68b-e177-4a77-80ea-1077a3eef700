package com.dib.dataIntegration.group.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;

/**
 * 模型分组输入参数.
 *
 * <AUTHOR>
 * @since 2020/2/10 12:42 下午
 */
@Data
@NoArgsConstructor
@Schema(description = "模型分组联合组件对象")
public class GroupUkVO {

    @Schema(description = "分组名称", required = true)
    @NotNull
    @Length(min = 1, max = 30)
    private String groupName;

    @Schema(description = "分组类型", required = true)
    @NotNull
    @Length(min = 1, max = 30)
    private String groupType;

}
