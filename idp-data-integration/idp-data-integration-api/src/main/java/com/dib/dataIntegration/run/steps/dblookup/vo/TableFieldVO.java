package com.dib.dataIntegration.run.steps.dblookup.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: j<PERSON><PERSON><PERSON>
 * @Descripition:
 * @Date:2020/3/7 1:24 下午
 */
@Data
@Schema(description = "表字段 字段")
public class TableFieldVO {

    @Schema(description = "字段名称")
    private String name;

    @Schema(description = "类型")
    private String type;


}
