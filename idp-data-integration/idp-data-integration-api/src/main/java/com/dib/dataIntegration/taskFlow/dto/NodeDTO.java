package com.dib.dataIntegration.taskFlow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Schema(description = "任务流节点数据传输对象")
@Data
public class NodeDTO {
    @Schema(description = "节点唯一ID")
    private String id;

    @Schema(description = "节点类型: start | task | end")
    private String type;

    @Schema(description = "任务类型（仅当 type == task 时有效）")
    private String taskType;

    @Schema(description = "节点显示文本")
    private String text;

    @Schema(description = "节点属性和配置参数")
    private Map<String, Object> properties;
}