package com.dib.dataIntegration.run.server.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020/3/6 5:15 下午
 */
@Data
@Schema(description = "保存模型查询")
public class SaveModelVO {

    @Schema(description = "模型名称")
    String modelName;

    @Schema(description = "开始时间")
    Date startTime;

    @Schema(description = "结束时间")
    Date endTime;

    public SaveModelVO() {
    }
}
