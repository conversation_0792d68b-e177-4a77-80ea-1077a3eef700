package com.dib.dataIntegration.taskFlow.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "在线任务流视图对象，用于表示已发布/在线的任务流信息")
public class TaskFlowOnlineVO {

    @Schema(description = "主键ID", example = "1234567890", required = true)
    private String id;

    @Schema(description = "源任务流ID,发布前的原始任务流ID", example = "1234567890",  required = false)
    private String taskFlowId;

    @Schema(description = "分组ID", example = "group123", required = false)
    private String groupId;

    @Schema(description = "分组名称", example = "数据同步任务组", required = false)
    private String groupName;

    @Schema(description = "标题", example = "数据同步任务流", required = true)
    private String title;

    @Schema(description = "代号", example = "DATA_SYNC_FLOW", required = false)
    private String code;

    @Schema(description = "类型，任务流的类型标识", example = "SYNC", required = false)
    private String type;

    @Schema(description = "状态，任务流的当前运行状态", example = "RUNNING", required = false)
    private String status;

    @Schema(description = "引用任务，引用的任务ID列表，逗号分隔", example = "task123,task456", required = false)
    private String referenceTask;

    @Schema(description = "创建人", example = "admin", required = false)
    private String creator;

    @Schema(description = "创建时间", required = false)
    private Date createTime;

    @Schema(description = "更新人", example = "admin", required = false)
    private String updater;

    @Schema(description = "描述", example = "用于同步数据库A和数据库B的数据", required = false)
    private String description;

    @Schema(description = "流程数据，存储任务流的配置信息，可能是JSON格式", required = false)
    private String flowFile;

    @Schema(description = "更新时间", required = false)
    private Date updateTime;

    @Schema(description = "是否删除，逻辑删除标记", example = "false", required = false)
    private Boolean deleted;
}
