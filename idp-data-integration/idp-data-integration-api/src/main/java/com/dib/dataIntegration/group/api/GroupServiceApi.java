package com.dib.dataIntegration.group.api;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.dib.dataIntegration.group.vo.GroupQueryVO;
import com.github.pagehelper.PageInfo;
import com.dib.dataIntegration.core.model.Result;
import com.dib.dataIntegration.group.dto.DpPortalGroupDTO;
import com.dib.dataIntegration.group.vo.GroupAddVO;
import com.dib.dataIntegration.group.vo.GroupUkVO;
import com.dib.dataIntegration.group.vo.GroupUpdateVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 分组服务api声明.
 *
 * <AUTHOR>
 * @since 2020/2/10 12:41 下午
 */
@Tag(name = "分组服务api声明")
public interface GroupServiceApi {
    /**
     * 新增分组.
     *
     * @param groupAddVO modelGroupAddVO
     * @return 接口应答
     */
    @Operation(summary = "新增分组", description = "新增分组", operationId = "add")
    @PostMapping(value = "/add")
    Result<DpPortalGroupDTO, Object> add(@RequestBody GroupAddVO groupAddVO);

    /**
     * 修改分组.
     *
     * @param groupUpdateVO modelGroupUpdateVO
     * @return 接口应答
     */
    @Operation(summary = "修改分组", description = "参数对象中如果有null值，数据库会被更新成null", operationId = "update")
    @PostMapping(value = "/update")
    Result<Boolean, Object> update(@RequestBody GroupUpdateVO groupUpdateVO);

    /**
     * 批量删除分组.
     *
     * @param groupIds 分组id
     * @return 接口应答
     */
    @Operation(summary = "批量删除分组", description = "根据id删除分组", operationId = "deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    Result<Boolean, Object> deleteBatch(@RequestParam("groupIds") String[] groupIds);

    /**
     * 删除分组.
     *
     * @param groupId 分组id
     * @return 接口应答
     */
    @Operation(summary = "删除分组", description = "根据id删除分组", operationId = "delete")
    @DeleteMapping(value = "/delete/{groupId}")
    Result<Boolean, Object> delete(@PathVariable("groupId") String groupId);

}

