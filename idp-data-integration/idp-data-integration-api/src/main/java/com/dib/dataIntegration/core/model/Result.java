package com.dib.dataIntegration.core.model;

import com.dib.dataIntegration.core.util.StatusCode;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import java.io.Serializable;

/**
 * 接口数据响应对象.
 *
 * <AUTHOR>
 * @since 2017-9-19 16:13
 */
@Setter
@Getter
@Schema(description = "接口响应对象")
@Data
public class Result<T, A> implements Serializable {

    @Schema(description = "返回码")
    private String code;

    @Schema(description = "返回描述信息")
    private String msg;

    @Schema(description = "返回内容的签名数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String sign;

    @Schema(description = "返回内容")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private T content;

    @Schema(description = "附件（满足不同场景需求返回数据）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private A attachment;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public T getContent() {
        return content;
    }

    public void setContent(T content) {
        this.content = content;
    }

    public A getAttachment() {
        return attachment;
    }

    public void setAttachment(A attachment) {
        this.attachment = attachment;
    }

    public Result() {
    }

    public Result(final String code) {
        this.code = code;
    }

    public Result(final String code, final String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Result(final String code, final String msg, final T content) {
        this.code = code;
        this.msg = msg;
        this.content = content;
    }

    /**
     * 成功.
     *
     * @param content 返回内容对象
     * @param <T>     content type
     * @return 返回PrimeResponse对象
     */
    public static <T> Result<T, Object> success(final T content) {
        Result<T, Object> result = new Result<>();
        result.setContent(content);
        result.setCode(StatusCode.CODE_200.getCode());

        return result;
    }

    /**
     * 成功.
     *
     * @param content 返回内容对象
     * @param message 提示信息
     * @param <T>     content type
     * @return 返回PrimeResponse对象
     */
    public static <T> Result<T, Object> success(final T content, final String message) {
        Result<T, Object> result = new Result<>();
        result.setContent(content);
        result.setMsg(message);
        result.setCode(StatusCode.CODE_200.getCode());
        return result;
    }

    /**
     * 失败.
     *
     * @param code    状态码
     * @param content 返回内容对象
     * @param message 提示信息
     * @param <T>     content type
     * @return 返回PrimeResponse对象
     */
    public static <T> Result fail(final String code, final T content, final String message) {
        Result<T, Object> result = new Result<>();
        result.setContent(content);
        result.setMsg(message);
        result.setCode(code);
        return result;
    }

    /**
     * 失败.
     *
     * @param code    状态码
     * @param content 返回内容对象
     * @param message 提示信息
     * @param <T>     content type
     * @return 返回PrimeResponse对象
     */
    public static <T> Result fail(final StatusCode code, final T content, final String message) {
        Result<T, Object> result = new Result<>();
        result.setContent(content);
        result.setMsg(message);
        result.setCode(code.getCode());
        return result;
    }

    /**
     * 失败.
     *
     * @param code    状态码
     * @param content 返回内容对象
     * @param message 提示信息
     * @param <T>     content type
     * @return 返回PrimeResponse对象
     */
    public static <T> Result fail(final String message) {
        Result<T, Object> result = new Result<>();
        result.setContent(null);
        result.setMsg(message);
        result.setCode(StatusCode.CODE_10010.getCode());
        return result;
    }

    /**
     * 失败.
     *
     * @param code    状态码
     * @param message 提示信息
     * @param <T>     content type
     * @return 返回PrimeResponse对象
     */
    public static <T> Result fail(final String code, final String message) {
        Result<T, Object> result = new Result<>();
        result.setMsg(message);
        result.setCode(code);
        return result;
    }

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this);
    }

}
