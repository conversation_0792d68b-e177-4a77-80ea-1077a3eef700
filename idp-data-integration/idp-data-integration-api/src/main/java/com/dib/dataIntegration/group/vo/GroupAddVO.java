package com.dib.dataIntegration.group.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;

/**
 * 模型分组输入参数.
 *
 * <AUTHOR>
 * @since 2020/2/10 12:42 下午
 */
@Data
@NoArgsConstructor
@Schema(description = "分组新增VO")
public class GroupAddVO {

    @Schema(description = "父级分组id", required = true)
    private String parentId;

    @Schema(description = "分组名称", required = true)
    @NotNull
    private String groupName;

    @Schema(description = "分组描述")
    private String describe;

    @Schema(description = "分组类型 1 任务 2 定版任务 3 任务流 4 定版任务流 5 任务流调度", required = true)
    @NotNull
    private String groupType;

    @Schema(description = "分组排序")
    private Integer groupOrder;

    @Schema(description = "是否启用 1-启用；0-未启用；", required = true)
    private boolean enabled;

}
