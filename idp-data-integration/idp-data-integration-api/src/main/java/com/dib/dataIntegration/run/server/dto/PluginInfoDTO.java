package com.dib.dataIntegration.run.server.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: jere<PERSON><PERSON>
 * @Descripition:
 * @Date:2020/2/18 3:48 下午
 */
@Data
@Schema(description = "插件信息")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PluginInfoDTO<T> {


    @Schema(description = "插件Id")
    private String pluginId;

    @Schema(description = "插件中文名")
    private String pluginName;

    @Schema(description = "插件描述")
    private String pluginDescribe;

    @Schema(description = "插件模型名称")
    private String modelName;

    @Schema(description = "插件模型中文名称")
    private String modelNameCn;

    @Schema(description = "模型类型")
    private String modelType;
    /**
     * 插件分类
     */
    @Schema(description = "插件分类")
    private String pluginCategory;

    /**
     * 插件类型（basic-基础拆件，business-业务插件）
     */
    @Schema(description = "插件类型（basic-基础拆件，business-业务插件）")
    private String pluginType;

    /**
     * 插件图片
     */
    @Schema(description = "插件图片")
    private String pluginImage;

    private Integer categoryOrder;

    private Integer pluginOrder;


    @Schema(description = "插件二级类型 input-输入 output-输出  merge-合并类型 filter-过滤型 normal-基本型")
    private String secondPluginType;


    @Schema(description = "用来判断是否可以分发复制")
    private String pluginFilter;

    @Schema(description = "用来判断是否是主输出")
    private String pluginOutput;

    private boolean hasParent = false;

    private boolean hasChildren = false;


    private String nodeId;

    private String id;

    private String label;


    private List<PluginInfoDTO<T>> children;


    public void initChildren() {
        this.children = new ArrayList<>();
    }
}
