package com.dib.dataIntegration.group.vo;

import com.dib.dataIntegration.group.entity.DpPortalGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotNull;

/**
 * 模型分组输入参数.
 *
 * <AUTHOR>
 * @since 2020/2/10 12:42 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Schema(description = "分组更新VO")
public class GroupUpdateVO extends DpPortalGroup {

    @Schema(description = "父级分组id", required = true)
    private String parentId;

    @Schema(description = "分组编号", required = true)
    @NotNull
    @Length(min = 1, max = 50)
    private String groupId;

    @Schema(description = "分组名称")
    @Length(min = 1, max = 30)
    private String groupName;

    @Schema(description = "分组类型")
    @Length(min = 1, max = 30)
    private String groupType;

    @Schema(description = "分组描述")
    private String describe;

    @Schema(description = "分组排序")
    private Integer groupOrder;

    @Schema(description = "是否启用\tT-启用；F-未启用；")
    private boolean enabled;

}
