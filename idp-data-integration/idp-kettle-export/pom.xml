<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dib</groupId>
        <artifactId>idp-data-integration</artifactId>
        <version>3.8.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>idp-kettle-export</artifactId>
    <packaging>jar</packaging>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <!-- kettle start -->
        <!-- https://mvnrepository.com/artifact/pentaho-kettle/kettle-core -->
        <dependency>
            <groupId>pentaho-kettle</groupId>
            <artifactId>kettle-core</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/kettle-core-9.4.0.0-343.jar</systemPath>
        </dependency>
        <!-- https://mvnrepository.com/artifact/pentaho-kettle/kettle-engine -->
        <dependency>
            <groupId>pentaho-kettle</groupId>
            <artifactId>kettle-engine</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/kettle-engine-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho</groupId>
            <artifactId>pentaho-encryption-support</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/pentaho-encryption-support-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>pentaho-kettle</groupId>
            <artifactId>metastore</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/metastore-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho.di.plugins</groupId>
            <artifactId>kettle-json-plugin-core</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/kettle-json-plugin-core-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho.di.plugins</groupId>
            <artifactId>elasticsearch-bulk-insert-core</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/elasticsearch-bulk-insert-core-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho.di.plugins</groupId>
            <artifactId>pdi-core</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/pdi-core-plugins-impl-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>pentaho</groupId>
            <artifactId>pentaho-big-data-kettle-plugins-kafka</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/pentaho-big-data-kettle-plugins-kafka-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>pentaho</groupId>
            <artifactId>pentaho-big-data-legacy-core</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/pentaho-big-data-legacy-core-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho</groupId>
            <artifactId>shim-api</artifactId>
            <version>9.4.0.0-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/shim-api-9.4.0.0-20240405.134336-1.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>pentaho</groupId>
            <artifactId>pentaho-metastore-locator-api</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/pentaho-metastore-locator-api-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho.di.plugins</groupId>
            <artifactId>pentaho-streaming-jms-plugin</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/pentaho-streaming-jms-plugin-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>pentaho</groupId>
            <artifactId>pentaho-mongodb-plugin</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/pentaho-mongodb-plugin-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>pentaho</groupId>
            <artifactId>pentaho-mongo-utils</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/pentaho-mongo-utils-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho.di.plugins</groupId>
            <artifactId>excel-plugins-core</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/excel-plugins-core-9.4.0.0-343.jar</systemPath>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-vfs2 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-vfs2</artifactId>
            <version>2.6.0</version>
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.55</version>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.9.0</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.7</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.dom4j/dom4j -->
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.3</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.scannotation/scannotation -->
        <dependency>
            <groupId>org.scannotation</groupId>
            <artifactId>scannotation</artifactId>
            <version>1.0.3</version>
            <exclusions>
                <exclusion>
                    <groupId>javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.30.2-GA</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/rhino/js -->
        <dependency>
            <groupId>rhino</groupId>
            <artifactId>js</artifactId>
            <version>1.7R2</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.owasp.encoder/encoder -->
        <dependency>
            <groupId>org.owasp.encoder</groupId>
            <artifactId>encoder</artifactId>
            <version>1.2.3</version>
        </dependency>
        <!-- kettle end -->
        <!-- https://mvnrepository.com/artifact/com.github.vlsi.mxgraph/jgraphx -->
        <dependency>
            <groupId>com.github.vlsi.mxgraph</groupId>
            <artifactId>jgraphx</artifactId>
            <version>4.1.0</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.xmlbeans/xmlbeans -->
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>3.1.0</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.google.guava/guava -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>33.0.0-jre</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-compress -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.21</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-collections4 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.1</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.1</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.1</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-math3</artifactId>
            <version>3.6.1</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- rest插件使用 -->
        <dependency>
            <groupId>com.sun.jersey.contribs</groupId>
            <artifactId>jersey-apache-client4</artifactId>
            <version>1.19.1</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sun.jersey</groupId>
            <artifactId>jersey-bundle</artifactId>
            <version>1.19.1</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.9</version>
        </dependency>
        <!-- rest插件使用 -->
        <!-- json插件使用 -->
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>net.minidev</groupId>
            <artifactId>json-smart</artifactId>
            <version>2.2</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.json-simple</groupId>
            <artifactId>json-simple</artifactId>
            <version>1.1</version>
        </dependency>
        <!-- json插件使用 -->
        <!-- java脚本插件使用 -->
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
            <version>3.1.4</version>
        </dependency>
        <!-- java脚本插件使用 -->
        <!-- es插件使用 -->
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>6.4.2</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>transport</artifactId>
            <version>6.4.2</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.plugin</groupId>
            <artifactId>transport-netty4-client</artifactId>
            <version>6.4.2</version>
        </dependency>
        <!-- es插件使用 -->
        <!-- kafka插件使用 -->
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>2.8.0</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-util</artifactId>
            <version>9.4.18.v20190429</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/io.reactivex.rxjava2/rxjava -->
        <dependency>
            <groupId>io.reactivex.rxjava2</groupId>
            <artifactId>rxjava</artifactId>
            <version>2.2.3</version>
        </dependency>
        <!-- kafka插件使用 -->
        <!-- jms插件使用 -->
        <dependency>
            <groupId>javax.jms</groupId>
            <artifactId>javax.jms-api</artifactId>
            <version>2.0.1</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.activemq/artemis-jms-client -->
        <dependency>
            <groupId>org.apache.activemq</groupId>
            <artifactId>artemis-jms-client</artifactId>
            <version>2.17.0</version>
        </dependency>
        <!-- jms插件使用 -->
        <!-- mongo插件使用 -->
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongo-java-driver</artifactId>
            <version>3.12.9</version>
        </dependency>
        <!-- mongo插件使用 -->
        <!-- oracle插件使用 -->
        <dependency>
            <groupId>oracle</groupId>
            <artifactId>ojdbc6</artifactId>
            <version>11.2.0.3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/ojdbc6.jar</systemPath>
        </dependency>
        <!-- oracle插件使用 -->
        <!-- postgresql插件使用 -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.5.0</version>
        </dependency>
        <!-- postgresql插件使用 -->

        <dependency>
            <groupId>com.enterprisedt</groupId>
            <artifactId>edtftpj</artifactId>
            <version>2.1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/edtftpj-2.1.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho.reporting.library</groupId>
            <artifactId>libformula</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/libformula-9.4.0.0-343.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.pentaho.reporting.library</groupId>
            <artifactId>libbase</artifactId>
            <version>9.4.0.0-343</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/ext-lib/libbase-9.4.0.0-343.jar</systemPath>
        </dependency>
    </dependencies>



</project>