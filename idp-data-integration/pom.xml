<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.dib</groupId>
        <artifactId>idp-ms</artifactId>
        <version>3.8.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <artifactId>idp-data-integration</artifactId>

    <description>
        数据集市
    </description>

    <properties>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <java.version>17</java.version>
        <junit.version>4.13.1</junit.version>

        <!-- Lib JAR路径配置 -->
        <lib.dir>${project.basedir}/../lib</lib.dir>
        <kettle-core.jar>${lib.dir}/kettle-core-*******-343.jar</kettle-core.jar>
        <kettle-engine.jar>${lib.dir}/kettle-engine-*******-343.jar</kettle-engine.jar>
        <pentaho-encryption-support.jar>${lib.dir}/pentaho-encryption-support-*******-343.jar</pentaho-encryption-support.jar>
        <metastore.jar>${lib.dir}/metastore-*******-343.jar</metastore.jar>
        <kettle-json-plugin-core.jar>${lib.dir}/kettle-json-plugin-core-*******-343.jar</kettle-json-plugin-core.jar>

        <!-- Maven Plugin Versions -->
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
        <maven-jar-plugin.version>3.2.0</maven-jar-plugin.version>
        <maven-release.version>2.5.3</maven-release.version>
        <spring-boot-maven-plugin.version>2.4.1</spring-boot-maven-plugin.version>


        <!-- Apache Commons -->
        <bouncycastle.version>1.55</bouncycastle.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <lombok.version>1.18.38</lombok.version>
        <json.version>20180813</json.version>
        <knife4j.version>3.0.3</knife4j.version>


        <!-- mybatis -->

        <mybatis.version>3.5.7</mybatis.version>
        <mybatis-spring.version>2.0.6</mybatis-spring.version>
        <pagehelper-spring-boot-starter.version>1.4.7</pagehelper-spring-boot-starter.version>


        <sofaboot.version>3.3.0</sofaboot.version>
        <sofa.ark.version>1.1.0</sofa.ark.version>

    </properties>

    <modules>
        <module>idp-data-integration-api</module>
        <module>idp-data-integration-ms</module>
        <module>idp-kettle-export</module>
        <module>idp-data-integration-plugins</module>
    </modules>

    <dependencies>
        <!-- 通用工具-->
        <!--        <dependency>-->
        <!--            <groupId>com.dib</groupId>-->
        <!--            <artifactId>idp-system</artifactId>-->
        <!--        </dependency>-->
    </dependencies>


</project>