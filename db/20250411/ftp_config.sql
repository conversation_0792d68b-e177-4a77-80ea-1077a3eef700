CREATE TABLE ftp_config (
                            id SERIAL PRIMARY KEY,
                            title VARCHAR(100) NOT NULL,             -- 配置标题
                            protocol VARCHAR(10) NOT NULL,           -- ftp / sftp
                            host VARCHAR(255) NOT NULL,              -- 服务器IP
                            port INTEGER NOT NULL DEFAULT 21,        -- 端口号
                            username VARCHAR(100) NOT NULL,          -- 用户名
                            password TEXT,                           -- 密码（可加密）
                            charset VARCHAR(50) DEFAULT 'UTF-8',     -- 字符集
                            private_key TEXT,                        -- 私钥内容（可选）
                            create_time TIMESTAMP DEFAULT now(),
                            update_time TIMESTAMP DEFAULT now()
);