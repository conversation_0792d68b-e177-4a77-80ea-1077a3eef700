INSERT INTO public.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
	 (1,'男','0','sys_user_sex','','','Y','0','admin','2022-04-08 14:52:38','',NULL,'性别男'),
	 (2,'女','1','sys_user_sex','','','N','0','admin','2022-04-08 14:52:38','',NULL,'性别女'),
	 (3,'未知','2','sys_user_sex','','','N','0','admin','2022-04-08 14:52:38','',NULL,'性别未知'),
	 (1,'显示','0','sys_show_hide','','primary','Y','0','admin','2022-04-08 14:52:38','',NULL,'显示菜单'),
	 (2,'隐藏','1','sys_show_hide','','danger','N','0','admin','2022-04-08 14:52:38','',NULL,'隐藏菜单'),
	 (1,'正常','0','sys_normal_disable','','primary','Y','0','admin','2022-04-08 14:52:38','',NULL,'正常状态'),
	 (2,'停用','1','sys_normal_disable','','danger','N','0','admin','2022-04-08 14:52:38','',NULL,'停用状态'),
	 (1,'正常','0','sys_job_status','','primary','Y','0','admin','2022-04-08 14:52:38','',NULL,'正常状态'),
	 (2,'暂停','1','sys_job_status','','danger','N','0','admin','2022-04-08 14:52:39','',NULL,'停用状态'),
	 (1,'默认','DEFAULT','sys_job_group','','','Y','0','admin','2022-04-08 14:52:39','',NULL,'默认分组');
INSERT INTO public.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
	 (2,'系统','SYSTEM','sys_job_group','','','N','0','admin','2022-04-08 14:52:39','',NULL,'系统分组'),
	 (1,'是','Y','sys_yes_no','','primary','Y','0','admin','2022-04-08 14:52:39','',NULL,'系统默认是'),
	 (2,'否','N','sys_yes_no','','danger','N','0','admin','2022-04-08 14:52:39','',NULL,'系统默认否'),
	 (1,'通知','1','sys_notice_type','','warning','Y','0','admin','2022-04-08 14:52:39','',NULL,'通知'),
	 (2,'公告','2','sys_notice_type','','success','N','0','admin','2022-04-08 14:52:39','',NULL,'公告'),
	 (1,'正常','0','sys_notice_status','','primary','Y','0','admin','2022-04-08 14:52:39','',NULL,'正常状态'),
	 (2,'关闭','1','sys_notice_status','','danger','N','0','admin','2022-04-08 14:52:39','',NULL,'关闭状态'),
	 (1,'新增','1','sys_oper_type','','info','N','0','admin','2022-04-08 14:52:39','',NULL,'新增操作'),
	 (2,'修改','2','sys_oper_type','','info','N','0','admin','2022-04-08 14:52:39','',NULL,'修改操作'),
	 (3,'删除','3','sys_oper_type','','danger','N','0','admin','2022-04-08 14:52:39','',NULL,'删除操作');
INSERT INTO public.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
	 (4,'授权','4','sys_oper_type','','primary','N','0','admin','2022-04-08 14:52:39','',NULL,'授权操作'),
	 (5,'导出','5','sys_oper_type','','warning','N','0','admin','2022-04-08 14:52:39','',NULL,'导出操作'),
	 (6,'导入','6','sys_oper_type','','warning','N','0','admin','2022-04-08 14:52:40','',NULL,'导入操作'),
	 (7,'强退','7','sys_oper_type','','danger','N','0','admin','2022-04-08 14:52:40','',NULL,'强退操作'),
	 (8,'生成代码','8','sys_oper_type','','warning','N','0','admin','2022-04-08 14:52:40','',NULL,'生成操作'),
	 (9,'清空数据','9','sys_oper_type','','danger','N','0','admin','2022-04-08 14:52:40','',NULL,'清空操作'),
	 (1,'成功','0','sys_common_status','','primary','N','0','admin','2022-04-08 14:52:40','',NULL,'正常状态'),
	 (2,'失败','1','sys_common_status','','danger','N','0','admin','2022-04-08 14:52:40','',NULL,'停用状态'),
	 (12,'Datax','datax','sys_job_group',NULL,'default','N','0','admin','2022-04-26 16:42:47','admin','2022-04-26 16:43:02','自定义分组'),
	 (13,'FlinkX','flinkx','sys_job_group',NULL,'default','N','0','admin','2022-04-26 16:44:56','',NULL,NULL);
INSERT INTO public.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
	 (0,'Datax','datax','Actuator_type',NULL,'primary','N','0','admin','2022-04-29 14:07:20','admin','2022-07-20 09:40:01',NULL),
	 (1,'FlinkX','flinkx','Actuator_type',NULL,'success','N','0','admin','2022-04-29 14:07:40','admin','2022-07-20 09:40:05',NULL),
	 (0,'库名规则','DATABASE_RULE','DATA_ASSETS_TYPE',NULL,'primary','N','0','admin','2022-06-15 14:32:08','admin','2022-07-20 09:39:45',NULL),
	 (1,'表名规则','TABLE_RULE','DATA_ASSETS_TYPE',NULL,'success','N','0','admin','2022-06-15 14:33:14','admin','2022-07-20 09:39:49',NULL),
	 (0,'业务层','ODS','DATABASE_LAYER',NULL,'primary','N','0','admin','2022-06-16 17:04:58','admin','2022-07-20 09:38:55','数据运营层：Operation Data Store 数据准备区，也称为贴源层。数据源中的数据，经过抽取、洗净、传输，也就是ETL过程之后进入本层。'),
	 (0,'数据细节层','DWD','DATABASE_LAYER',NULL,'success','N','0','admin','2022-06-16 17:05:48','admin','2022-07-20 09:39:01','该层是业务层和数据仓库的隔离层，保持和ODS层一样的数据颗粒度；主要是对ODS数据层做一些数据的清洗和规范化的操作，比如去除空数据、脏数据、离群值等。'),
	 (0,'数据中间层','DWM','DATABASE_LAYER',NULL,'warning','N','0','admin','2022-06-16 17:06:32','admin','2022-07-20 09:39:12','该层是在DWD层的数据基础上，对数据做一些轻微的聚合操作，生成一些列的中间结果表，提升公共指标的复用性，减少重复加工的工作。'),
	 (0,'数据服务层','DWS','DATABASE_LAYER',NULL,'danger','N','0','admin','2022-06-16 17:07:12','admin','2022-07-20 09:39:17','该层是基于DWM上的基础数据，整合汇总成分析某一个主题域的数据服务层，一般是宽表，用于提供后续的业务查询，OLAP分析，数据分发等。'),
	 (0,'数据应用层','ADS','DATABASE_LAYER',NULL,'info','N','0','admin','2022-06-16 17:07:46','admin','2022-07-20 09:39:29','该层主要是提供给数据产品和数据分析使用的数据，一般会存放在ES、Redis、PostgreSql等系统中供线上系统使用；也可能存放在hive或者Druid中，供数据分析和数据挖掘使用，比如常用的数据报表就是存在这里的。'),
	 (0,'是','1','COLONY_STATE',NULL,'success','N','0','admin','2022-06-18 11:48:22','admin','2022-07-20 09:38:30',NULL);
INSERT INTO public.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
	 (0,'否','0','COLONY_STATE',NULL,'warning','N','0','admin','2022-06-18 11:48:34','admin','2022-07-20 09:38:40',NULL),
	 (0,'数仓建库','warehouse_building','source_group',NULL,'primary','N','0','admin','2022-08-22 17:30:42','',NULL,NULL),
	 (0,'数据源','source','source_group',NULL,'success','N','0','admin','2022-08-22 17:31:08','',NULL,NULL),
	 (0,'数据下沉','sink','source_group',NULL,'warning','N','0','admin','2022-08-22 17:32:37','admin','2022-08-22 17:32:44',NULL),
	 (0,'字段','field','data_table_field',NULL,'danger','N','0','admin','2022-08-23 11:23:26','admin','2022-08-23 11:23:31',NULL),
	 (0,'表','table','data_table_field',NULL,'success','N','0','admin','2022-08-23 11:23:45','',NULL,NULL),
	 (0,'数据','data','table_action_type',NULL,'primary','N','0','admin','2022-08-24 13:57:26','',NULL,NULL),
	 (1,'档案','archives','table_action_type',NULL,'success','N','0','admin','2022-08-24 13:57:53','',NULL,NULL),
	 (0,'指标','index','table_action_type',NULL,'warning','N','0','admin','2022-08-24 13:58:11','admin','2022-08-24 13:58:16',NULL),
	 (0,'零售','retail','table_theme',NULL,'primary','N','0','admin','2022-08-24 13:58:50','',NULL,NULL);
INSERT INTO public.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
	 (0,'商品','goods','table_theme',NULL,'success','N','0','admin','2022-08-24 13:59:05','',NULL,NULL),
	 (0,'会员','vip','table_theme',NULL,'warning','N','0','admin','2022-08-24 13:59:17','',NULL,NULL),
	 (0,'库存','stock','table_theme',NULL,'danger','N','0','admin','2022-08-24 13:59:50','',NULL,NULL),
	 (0,'POST','POST','sys_api_mode',NULL,'danger','N','0','admin','2022-09-27 10:53:00','',NULL,NULL),
	 (1,'GET','GET','sys_api_mode',NULL,'success','N','0','admin','2022-09-27 10:53:13','',NULL,NULL),
	 (0,'是','1','sys_api_id',NULL,'danger','N','0','admin','2022-09-27 10:53:44','',NULL,NULL),
	 (0,'否','0','sys_api_id',NULL,'success','N','0','admin','2022-09-27 10:54:12','',NULL,NULL),
	 (0,'启用','1','sys_data_status',NULL,'success','N','0','admin','2023-02-20 20:45:13','',NULL,NULL),
	 (0,'禁用','0','sys_data_status',NULL,'danger','N','0','admin','2023-02-20 20:45:24','',NULL,NULL),
	 (0,'MySql数据库','1','data_db_type',NULL,'default','N','0','admin','2023-02-20 20:59:51','',NULL,NULL);
INSERT INTO public.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
	 (1,'MariaDB数据库','2','data_db_type',NULL,'default','N','0','admin','2023-02-20 21:00:02','',NULL,NULL),
	 (2,'Oracle11g及以下数据库','3','data_db_type',NULL,'default','N','0','admin','2023-02-20 21:00:29','',NULL,NULL),
	 (3,'Oracle12c+数据库','4','data_db_type',NULL,'default','N','0','admin','2023-02-20 21:00:39','',NULL,NULL),
	 (4,'PostgreSql数据库','5','data_db_type',NULL,'default','N','0','admin','2023-02-20 21:00:46','',NULL,NULL),
	 (5,'SQLServer2008及以下数据库','6','data_db_type',NULL,'default','N','0','admin','2023-02-20 21:00:58','',NULL,NULL),
	 (6,'SQLServer2012+数据库','7','data_db_type',NULL,'default','N','0','admin','2023-02-20 21:01:09','',NULL,NULL),
	 (9,'其他数据库','8','data_db_type',NULL,'default','N','0','admin','2023-02-20 21:01:21','admin','2023-02-27 14:01:39',NULL),
	 (7,'Clickhouse数据库','9','data_db_type',NULL,'default','N','0','admin','2023-02-27 14:01:01','',NULL,NULL),
	 (8,'Hive数据库','10','data_db_type',NULL,'default','N','0','admin','2023-02-27 14:01:34','',NULL,NULL),
	 (0,'服务器资源','1','data_base_type',NULL,'warning','N','0','admin','2023-03-29 17:21:50','admin','2023-03-31 15:25:20',NULL);
INSERT INTO public.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
	 (0,'文件资源','2','data_base_type',NULL,'primary','N','0','admin','2023-03-31 15:25:34','',NULL,NULL),
	 (0,'已对照','1','data_contrast_status',NULL,'primary','N','0','admin','2023-04-27 16:29:23','',NULL,NULL),
	 (0,'未对照','0','data_contrast_status',NULL,'danger','N','0','admin','2023-04-27 16:29:38','',NULL,NULL),
	 (0,'启用','1','data_rule_statu',NULL,'primary','N','0','admin','2023-04-28 15:51:45','',NULL,NULL),
	 (0,'关闭','0','data_rule_statu',NULL,'danger','N','0','admin','2023-04-28 15:52:14','',NULL,NULL),
	 (0,'成功','1','task_log_status',NULL,'success','N','0','admin','2023-04-28 16:57:44','',NULL,NULL),
	 (0,'失败','0','task_log_status',NULL,'danger','N','0','admin','2023-04-28 16:57:54','',NULL,NULL),
	 (0,'启用','1','sys_api_status',NULL,'primary','N','0','admin','2023-05-09 15:56:19','',NULL,NULL),
	 (0,'关闭','0','sys_api_status',NULL,'danger','N','0','admin','2023-05-09 15:56:29','',NULL,NULL),
	 (0,'未发布','3','sys_api_status',NULL,'info','N','0','admin','2023-05-09 15:56:53','',NULL,NULL);
INSERT INTO public.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
	 (0,'发布','2','sys_api_status',NULL,'success','N','0','admin','2023-05-09 15:57:12','',NULL,NULL),
	 (0,'JSON','JSON','sys_api_return',NULL,'primary','N','0','admin','2023-05-09 16:13:25','',NULL,NULL),
	 (0,'否','0','sys_api_limit',NULL,'success','N','0','admin','2023-05-09 16:17:24','',NULL,NULL),
	 (0,'是','1','sys_api_limit',NULL,'primary','N','0','admin','2023-05-09 16:17:32','admin','2023-05-09 16:17:39',NULL),
	 (0,'表引导模式','1','sys_api_config_type',NULL,'primary','N','0','admin','2023-05-09 16:26:31','',NULL,NULL),
	 (0,'脚本模式','2','sys_api_config_type',NULL,'success','N','0','admin','2023-05-09 16:26:43','',NULL,NULL),
	 (0,'等于','1','data_where_type',NULL,'default','N','0','admin','2023-05-09 16:51:27','',NULL,NULL),
	 (0,'不等于','2','data_where_type',NULL,'default','N','0','admin','2023-05-09 16:51:37','admin','2023-05-09 16:52:08',NULL),
	 (0,'全模糊查询','3','data_where_type',NULL,'default','N','0','admin','2023-05-09 16:51:55','admin','2023-05-09 16:52:15',NULL),
	 (0,'左模糊查询','4','data_where_type',NULL,'default','N','0','admin','2023-05-09 16:52:39','',NULL,NULL);
INSERT INTO public.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
	 (0,'右模糊查询','5','data_where_type',NULL,'default','N','0','admin','2023-05-09 16:52:53','',NULL,NULL),
	 (0,'大于','6','data_where_type',NULL,'default','N','0','admin','2023-05-09 16:53:01','',NULL,NULL),
	 (0,'大于等于','7','data_where_type',NULL,'default','N','0','admin','2023-05-09 16:53:10','',NULL,NULL),
	 (0,'小于','8','data_where_type',NULL,'default','N','0','admin','2023-05-09 16:53:18','',NULL,NULL),
	 (0,'小于等于','9','data_where_type',NULL,'default','N','0','admin','2023-05-09 16:53:29','',NULL,NULL),
	 (0,'是否为空','10','data_where_type',NULL,'default','N','0','admin','2023-05-09 16:53:45','',NULL,NULL),
	 (0,'是否不为空','11','data_where_type',NULL,'default','N','0','admin','2023-05-09 16:53:56','',NULL,NULL),
	 (0,'包含(IN)','12','data_where_type',NULL,'default','N','0','admin','2023-05-09 16:54:20','',NULL,NULL),
	 (0,'字符串','1','data_param_type',NULL,'default','N','0','admin','2023-05-09 16:55:33','',NULL,NULL),
	 (0,'整型','2','data_param_type',NULL,'default','N','0','admin','2023-05-09 16:55:42','',NULL,NULL);
INSERT INTO public.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
	 (0,'浮点型','3','data_param_type',NULL,'default','N','0','admin','2023-05-09 16:55:55','',NULL,NULL),
	 (0,'时间型','4','data_param_type',NULL,'default','N','0','admin','2023-05-09 16:56:07','',NULL,NULL),
	 (0,'集合','5','data_param_type',NULL,'default','N','0','admin','2023-05-09 16:56:14','',NULL,NULL),
	 (0,'正则替换','1','data_cipher_type',NULL,'default','N','0','admin','2023-05-10 14:26:19','',NULL,NULL),
	 (0,'加密算法','2','data_cipher_type',NULL,'default','N','0','admin','2023-05-10 14:26:33','',NULL,NULL),
	 (0,'中文姓名','1','data_regex_crypto',NULL,'default','N','0','admin','2023-05-10 14:27:49','',NULL,NULL),
	 (0,'身份证号','2','data_regex_crypto',NULL,'default','N','0','admin','2023-05-10 14:28:01','',NULL,NULL),
	 (0,'固定电话','3','data_regex_crypto',NULL,'default','N','0','admin','2023-05-10 14:28:12','',NULL,NULL),
	 (0,'手机号码','4','data_regex_crypto',NULL,'default','N','0','admin','2023-05-10 14:28:21','',NULL,NULL),
	 (0,'地址','5','data_regex_crypto',NULL,'default','N','0','admin','2023-05-10 14:28:30','',NULL,NULL);
INSERT INTO public.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
	 (0,'电子邮箱','6','data_regex_crypto',NULL,'default','N','0','admin','2023-05-10 14:28:42','',NULL,NULL),
	 (0,'银行卡号','7','data_regex_crypto',NULL,'default','N','0','admin','2023-05-10 14:28:59','',NULL,NULL),
	 (0,'公司开户银行联号','8','data_regex_crypto',NULL,'default','N','0','admin','2023-05-10 14:29:18','',NULL,NULL),
	 (0,'BASE64加密','1','data_algorithm_crypto',NULL,'default','N','0','admin','2023-05-10 14:30:27','',NULL,NULL),
	 (0,'MD5加密','2','data_algorithm_crypto',NULL,'default','N','0','admin','2023-05-10 14:30:32','',NULL,NULL),
	 (0,'SHA_1加密','3','data_algorithm_crypto',NULL,'default','N','0','admin','2023-05-10 14:30:40','',NULL,NULL),
	 (0,'SHA_256加密','4','data_algorithm_crypto',NULL,'default','N','0','admin','2023-05-10 14:30:49','',NULL,NULL),
	 (0,'AES加密','5','data_algorithm_crypto',NULL,'default','N','0','admin','2023-05-10 14:30:57','',NULL,NULL),
	 (0,'DES加密','6','data_algorithm_crypto',NULL,'default','N','0','admin','2023-05-10 14:31:03','',NULL,NULL),
	 (0,'开启','1','data_cipher_status',NULL,'success','N','0','admin','2023-05-10 14:33:51','',NULL,NULL);
INSERT INTO public.sys_dict_data (dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) VALUES
	 (0,'关闭','0','data_cipher_status',NULL,'danger','N','0','admin','2023-05-10 14:34:05','',NULL,NULL),
	 (0,'SQL流任务','0','flink_job_type',NULL,'success','N','0','admin','2023-05-12 11:40:43','admin','2023-05-12 11:44:17',NULL),
	 (0,'JAR流任务','1','flink_job_type',NULL,'primary','N','0','admin','2023-05-12 11:41:36','admin','2023-05-12 11:44:40',NULL),
	 (0,'JAR批任务','3','flink_job_type',NULL,'warning','N','0','admin','2023-05-12 11:42:34','admin','2023-05-12 13:56:53',NULL),
	 (0,'SQL批任务','2','flink_job_type',NULL,'info','N','0','admin','2023-05-12 13:55:45','',NULL,NULL),
	 (0,'未同步','0','datasource_Synchronization_status',NULL,'info','N','0','admin','2023-06-29 14:52:18','admin','2023-06-29 14:52:38',NULL),
	 (0,'同步中','1','datasource_Synchronization_status',NULL,'warning','N','0','admin','2023-06-29 14:53:06','',NULL,NULL),
	 (0,'已同步','2','datasource_Synchronization_status',NULL,'success','N','0','admin','2023-06-29 14:53:22','',NULL,NULL),
	 (0,'同步失败','3','datasource_Synchronization_status',NULL,'danger','N','0','admin','2023-06-29 14:53:40','',NULL,NULL),
	 (9,'达梦8数据库','11','data_db_type',NULL,'default','N','0','admin','2023-02-27 14:01:34','',NULL,NULL);
