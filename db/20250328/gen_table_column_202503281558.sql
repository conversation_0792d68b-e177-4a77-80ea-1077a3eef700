INSERT INTO public.gen_table_column (table_id,column_name,column_comment,column_type,java_type,java_field,is_pk,is_increment,is_required,is_insert,is_edit,is_list,is_query,query_type,html_type,dict_type,sort,create_by,create_time,update_by,update_time) VALUES
	 ('8','id',NULL,'bigint(20)','Long','id','1','1',NULL,'1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-03-27 17:49:09','',NULL),
	 ('8','parent_id','父id','bigint(20)','Long','parentId','0','0',NULL,'1','1','1','1','EQ','input','',2,'admin','2025-03-27 17:49:09','',NULL),
	 ('8','parent_name','父名称','varchar(255)','String','parentName','0','0',NULL,'1','1','1','1','LIKE','input','',3,'admin','2025-03-27 17:49:10','',NULL),
	 ('8','ancestors','祖籍列表','varchar(255)','String','ancestors','0','0',NULL,'1','1','1','1','EQ','input','',4,'admin','2025-03-27 17:49:10','',NULL),
	 ('8','name','库/表/字段','varchar(255)','String','name','0','0',NULL,'1','1','1','1','LIKE','input','',5,'admin','2025-03-27 17:49:10','',NULL),
	 ('8','type','类型','varchar(255)','String','type','0','0',NULL,'1','1','1','1','EQ','select','',6,'admin','2025-03-27 17:49:10','',NULL),
	 ('8','lenth','长度','int(11)','Long','lenth','0','0',NULL,'1','1','1','1','EQ','input','',7,'admin','2025-03-27 17:49:10','',NULL),
	 ('8','comment','库/表/字段名','varchar(255)','String','comment','0','0',NULL,'1','1','1','1','EQ','input','',8,'admin','2025-03-27 17:49:10','',NULL),
	 ('8','createTableQuery','建表语句','longtext','String','createtablequery','0','0',NULL,'1','1','1','1','EQ','textarea','',9,'admin','2025-03-27 17:49:10','',NULL),
	 ('8','action_type','作用类型（档案,指标,数据）','varchar(255)','String','actionType','0','0',NULL,'1','1','1','1','EQ','select','',10,'admin','2025-03-27 17:49:10','',NULL);
INSERT INTO public.gen_table_column (table_id,column_name,column_comment,column_type,java_type,java_field,is_pk,is_increment,is_required,is_insert,is_edit,is_list,is_query,query_type,html_type,dict_type,sort,create_by,create_time,update_by,update_time) VALUES
	 ('8','theme','表主题（零售，会员，库存等）','varchar(255)','String','theme','0','0',NULL,'1','1','1','1','EQ','input','',11,'admin','2025-03-27 17:49:10','',NULL),
	 ('8','level_code','TABLE:表  DATABASE:库  FIELD:字段','varchar(255)','String','levelCode','0','0',NULL,'1','1','1','1','EQ','input','',12,'admin','2025-03-27 17:49:10','',NULL),
	 ('8','create_by','创建人','varchar(255)','String','createBy','0','0',NULL,'1',NULL,NULL,NULL,'EQ','input','',13,'admin','2025-03-27 17:49:10','',NULL),
	 ('8','create_time','创建时间','datetime','Date','createTime','0','0',NULL,'1',NULL,NULL,NULL,'EQ','datetime','',14,'admin','2025-03-27 17:49:10','',NULL),
	 ('8','update_by','更新人','varchar(255)','String','updateBy','0','0',NULL,'1','1',NULL,NULL,'EQ','input','',15,'admin','2025-03-27 17:49:10','',NULL),
	 ('8','update_time','更新时间','datetime','Date','updateTime','0','0',NULL,'1','1',NULL,NULL,'EQ','datetime','',16,'admin','2025-03-27 17:49:10','',NULL);
