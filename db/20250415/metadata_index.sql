CREATE TABLE metadata_index (
                                id VARCHAR(50) PRIMARY KEY,            -- 主键
                                source_id VARCHAR(50) NOT NULL,        -- 所属数据源，最大长度50个字符，不能为空
                                table_id VARCHAR(50) NOT NULL,         -- 所属数据表，最大长度50个字符，不能为空
                                index_name VARCHAR(100) NOT NULL,      -- 索引名称，最大长度100个字符，不能为空
                                index_type VARCHAR(50) NOT NULL,       -- 索引类型，最大长度50个字符，不能为空
                                column_name VARCHAR(100) NOT NULL      -- 索引字段名称，最大长度100个字符，不能为空
);

-- 添加列注释
COMMENT ON COLUMN metadata_index.id IS '主键';
COMMENT ON COLUMN metadata_index.source_id IS '所属数据源';
COMMENT ON COLUMN metadata_index.table_id IS '所属数据表';
COMMENT ON COLUMN metadata_index.index_name IS '索引名称';
COMMENT ON COLUMN metadata_index.index_type IS '索引类型';
COMMENT ON COLUMN metadata_index.column_name IS '索引字段名称';    
    