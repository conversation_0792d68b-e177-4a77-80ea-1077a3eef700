<?xml version="1.0" encoding="UTF-8"?>
<connection>
    <name>19</name>
    <label>达梦243</label>
    <server>**************</server>
    <port>30236</port>
    <type>11</type>
    <access>Native</access>
    <database>eip_dc</database>
    <username>SYSDBA</username>
    <password>SYSDBA001</password>
    <diagramId>f10a5a1d-8ad6-4179-b8a5-ac337eb3c215</diagramId>
</connection>
<step>
<name>f10a5a1d-8ad6-4179-b8a5-ac337eb3c215</name>
<connection>19</connection>
<table>t_read_conf</table>
<fields>
    <field>
        <name>id</name>
        <type>BIGINT</type>
        <length>8</length>
        <precision>null</precision>
    </field>
    <field>
        <name>extract_conf_id</name>
        <type>BIGINT</type>
        <length>8</length>
        <precision>null</precision>
    </field>
    <field>
        <name>groovy_name</name>
        <type>VARCHAR</type>
        <length>50</length>
        <precision>null</precision>
    </field>
    <field>
        <name>groovy_content</name>
        <type>TEXT</type>
        <length>2147483647</length>
        <precision>null</precision>
    </field>
    <field>
        <name>is_del</name>
        <type>BIT</type>
        <length>1</length>
        <precision>null</precision>
    </field>
    <field>
        <name>dt_upd_tm</name>
        <type>DATETIME</type>
        <length>8</length>
        <precision>null</precision>
    </field>
    <field>
        <name>dt_crt_tm</name>
        <type>DATETIME</type>
        <length>8</length>
        <precision>null</precision>
    </field>
    <field>
        <name>c_crt_usr</name>
        <type>VARCHAR</type>
        <length>50</length>
        <precision>null</precision>
    </field>
    <field>
        <name>c_upd_usr</name>
        <type>VARCHAR</type>
        <length>50</length>
        <precision>null</precision>
    </field>
    <field>
        <name>material_type</name>
        <type>VARCHAR</type>
        <length>255</length>
        <precision>null</precision>
    </field>
    <field>
        <name>read_type</name>
        <type>INTEGER</type>
        <length>4</length>
        <precision>null</precision>
    </field>
    <field>
        <name>status</name>
        <type>BIT</type>
        <length>1</length>
        <precision>null</precision>
    </field>
</fields>
<diagramId>f10a5a1d-8ad6-4179-b8a5-ac337eb3c215</diagramId>
</step><node>
<nodes>
    <id>3e9dfa9a-dfbc-4ecf-a51a-958aedeae3d6</id>
    <type>TableInput</type>
    <x>570</x>
    <y>201.5</y>
    <properties>
        <nodeId>f10a5a1d-8ad6-4179-b8a5-ac337eb3c215</nodeId>
        <width>100</width>
        <height>80</height>
    </properties>
    <zIndex>1005</zIndex>
</nodes>
</node>