<?xml version="1.0" encoding="UTF-8"?><transformation><connection><name>19</name><label>达梦243</label><server>**************</server><port>30236</port><type>DM8</type><access>Native</access><database>eip_dc</database><username>SYSDBA</username><password>SYSDBA001</password><diagramId>9077a5b9-b142-4480-86e9-e7f56952adbc</diagramId><attributes><attribute><code>SUPPORTS_BOOLEAN_DATA_TYPE</code><attribute>Y</attribute></attribute><attribute><code>SUPPORTS_TIMESTAMP_DATA_TYPE</code><attribute>Y</attribute></attribute><attribute><code>QUOTE_ALL_FIELDS</code><attribute>N</attribute></attribute><attribute><code>FORCE_IDENTIFIERS_TO_LOWERCASE</code><attribute>N</attribute></attribute><attribute><code>FORCE_IDENTIFIERS_TO_UPPERCASE</code><attribute>N</attribute></attribute></attributes></connection><connection><name>26</name><label>演示贴源库</label><server>**************</server><port>5432</port><type>POSTGRESQL</type><access>Native</access><database>postgres?currentSchema=idptest</database><username>postgres</username><password>Dib@123456</password><diagramId>210dbc3e-69fd-4b1f-ab74-864590e8b87b</diagramId><attributes><attribute><code>SUPPORTS_BOOLEAN_DATA_TYPE</code><attribute>Y</attribute></attribute><attribute><code>SUPPORTS_TIMESTAMP_DATA_TYPE</code><attribute>Y</attribute></attribute><attribute><code>QUOTE_ALL_FIELDS</code><attribute>N</attribute></attribute><attribute><code>FORCE_IDENTIFIERS_TO_LOWERCASE</code><attribute>N</attribute></attribute><attribute><code>FORCE_IDENTIFIERS_TO_UPPERCASE</code><attribute>N</attribute></attribute></attributes></connection><connection><name>19</name><label>达梦243</label><server>**************</server><port>30236</port><type>DM8</type><access>Native</access><database>eip_dc</database><username>SYSDBA</username><password>SYSDBA001</password><diagramId>8227a0f8-ce4d-400d-90e1-052b3b6a6974</diagramId><attributes><attribute><code>SUPPORTS_BOOLEAN_DATA_TYPE</code><attribute>Y</attribute></attribute><attribute><code>SUPPORTS_TIMESTAMP_DATA_TYPE</code><attribute>Y</attribute></attribute><attribute><code>QUOTE_ALL_FIELDS</code><attribute>N</attribute></attribute><attribute><code>FORCE_IDENTIFIERS_TO_LOWERCASE</code><attribute>N</attribute></attribute><attribute><code>FORCE_IDENTIFIERS_TO_UPPERCASE</code><attribute>N</attribute></attribute></attributes></connection><connection><name>26</name><label>演示贴源库</label><server>**************</server><port>5432</port><type>POSTGRESQL</type><access>Native</access><database>postgres?currentSchema=idptest</database><username>postgres</username><password>Dib@123456</password><diagramId>f248b603-cc6a-439f-b684-fd2aa7b251d6</diagramId><attributes><attribute><code>SUPPORTS_BOOLEAN_DATA_TYPE</code><attribute>Y</attribute></attribute><attribute><code>SUPPORTS_TIMESTAMP_DATA_TYPE</code><attribute>Y</attribute></attribute><attribute><code>QUOTE_ALL_FIELDS</code><attribute>N</attribute></attribute><attribute><code>FORCE_IDENTIFIERS_TO_LOWERCASE</code><attribute>N</attribute></attribute><attribute><code>FORCE_IDENTIFIERS_TO_UPPERCASE</code><attribute>N</attribute></attribute></attributes></connection><step><name>9077a5b9-b142-4480-86e9-e7f56952adbc</name><connection>19</connection><desc>bsr</desc><copies>1</copies><limit>0</limit><distribute>Y</distribute><execute_each_row>N</execute_each_row><variables_active>Y</variables_active><lazy_conversion_active>N</lazy_conversion_active><outFields><name>id</name><type>BIGINT</type><length>8</length><precision>null</precision><columnComment>null</columnComment><orgStep>9077a5b9-b142-4480-86e9-e7f56952adbc</orgStep></outFields><outFields><name>record_id</name><type>VARCHAR</type><length>100</length><precision>null</precision><columnComment>批次号</columnComment><orgStep>9077a5b9-b142-4480-86e9-e7f56952adbc</orgStep></outFields><outFields><name>type</name><type>VARCHAR</type><length>100</length><precision>null</precision><columnComment>类型</columnComment><orgStep>9077a5b9-b142-4480-86e9-e7f56952adbc</orgStep></outFields><outFields><name>contents</name><type>TEXT</type><length>2147483647</length><precision>null</precision><columnComment>描述内容</columnComment><orgStep>9077a5b9-b142-4480-86e9-e7f56952adbc</orgStep></outFields><outFields><name>c_role</name><type>VARCHAR</type><length>100</length><precision>null</precision><columnComment>角色</columnComment><orgStep>9077a5b9-b142-4480-86e9-e7f56952adbc</orgStep></outFields><outFields><name>c_crt_usr</name><type>VARCHAR</type><length>100</length><precision>null</precision><columnComment>创建人名称</columnComment><orgStep>9077a5b9-b142-4480-86e9-e7f56952adbc</orgStep></outFields><outFields><name>dt_crt_tm</name><type>DATETIME</type><length>8</length><precision>null</precision><columnComment>创建时间</columnComment><orgStep>9077a5b9-b142-4480-86e9-e7f56952adbc</orgStep></outFields><outFields><name>c_upd_usr</name><type>VARCHAR</type><length>100</length><precision>null</precision><columnComment>更新人名称</columnComment><orgStep>9077a5b9-b142-4480-86e9-e7f56952adbc</orgStep></outFields><outFields><name>dt_upd_tm</name><type>DATETIME</type><length>8</length><precision>null</precision><columnComment>更新时间</columnComment><orgStep>9077a5b9-b142-4480-86e9-e7f56952adbc</orgStep></outFields><outFields><name>is_done</name><type>BIT</type><length>1</length><precision>null</precision><columnComment>是否完成</columnComment><orgStep>9077a5b9-b142-4480-86e9-e7f56952adbc</orgStep></outFields><outFields><name>is_del</name><type>BIT</type><length>1</length><precision>null</precision><columnComment>是否删除</columnComment><orgStep>9077a5b9-b142-4480-86e9-e7f56952adbc</orgStep></outFields><table>t_element_feedback</table><type>TableInput</type><fields><field><name>id</name><type>Integer</type><length>8</length></field><field><name>record_id</name><type>String</type><length>100</length><columnComment>批次号</columnComment></field><field><name>type</name><type>String</type><length>100</length><columnComment>类型</columnComment></field><field><name>contents</name><type>String</type><length>2147483647</length><columnComment>描述内容</columnComment></field><field><name>c_role</name><type>String</type><length>100</length><columnComment>角色</columnComment></field><field><name>c_crt_usr</name><type>String</type><length>100</length><columnComment>创建人名称</columnComment></field><field><name>dt_crt_tm</name><type>Timestamp</type><length>8</length><columnComment>创建时间</columnComment></field><field><name>c_upd_usr</name><type>String</type><length>100</length><columnComment>更新人名称</columnComment></field><field><name>dt_upd_tm</name><type>Timestamp</type><length>8</length><columnComment>更新时间</columnComment></field><field><name>is_done</name><type>Boolean</type><length>1</length><columnComment>是否完成</columnComment></field><field><name>is_del</name><type>Boolean</type><length>1</length><columnComment>是否删除</columnComment></field></fields><diagramId>9077a5b9-b142-4480-86e9-e7f56952adbc</diagramId><sql>SELECT id, record_id, type, contents, c_role, c_crt_usr, dt_crt_tm, c_upd_usr, dt_upd_tm, is_done, is_del FROM t_element_feedback</sql></step><step><name>210dbc3e-69fd-4b1f-ab74-864590e8b87b</name><type>TableOutput</type><desc>bsc</desc><distribute>Y</distribute><copies>1</copies><connection>26</connection><table>zzbstone</table><schema/><commit>5000</commit><truncate>Y</truncate><ignore_errors>N</ignore_errors><specify_fields>Y</specify_fields><use_batch>Y</use_batch><partitioning_enabled>N</partitioning_enabled><partitioning_field/><partitioning_daily>N</partitioning_daily><partitioning_monthly>N</partitioning_monthly><tablename_in_field>N</tablename_in_field><tablename_field/><tablename_in_table>N</tablename_in_table><return_keys>N</return_keys><return_field/><updateType>append</updateType><insertUpsert>false</insertUpsert><directInsert>false</directInsert><extraProperties>EXTRA_TABLE_DEIFNE=stored as kudu</extraProperties><fields><field><column_name>id</column_name><stream_name>id</stream_name></field><field><column_name>record_id</column_name><stream_name>record_id</stream_name></field><field><column_name>type</column_name><stream_name>type</stream_name></field><field><column_name>contents</column_name><stream_name>contents</stream_name></field><field><column_name>c_role</column_name><stream_name>c_role</stream_name></field><field><column_name>c_crt_usr</column_name><stream_name>c_crt_usr</stream_name></field><field><column_name>dt_crt_tm</column_name><stream_name>dt_crt_tm</stream_name></field><field><column_name>c_upd_usr</column_name><stream_name>c_upd_usr</stream_name></field><field><column_name>dt_upd_tm</column_name><stream_name>dt_upd_tm</stream_name></field><field><column_name>is_done</column_name><stream_name>is_done</stream_name></field><field><column_name>is_del</column_name><stream_name>is_del</stream_name></field></fields><diagramId>210dbc3e-69fd-4b1f-ab74-864590e8b87b</diagramId></step><step><name>8227a0f8-ce4d-400d-90e1-052b3b6a6974</name><connection>19</connection><desc>bsr</desc><copies>1</copies><limit>0</limit><distribute>Y</distribute><execute_each_row>N</execute_each_row><variables_active>Y</variables_active><lazy_conversion_active>N</lazy_conversion_active><outFields><name>id</name><type>BIGINT</type><length>8</length><precision>null</precision><columnComment>null</columnComment><orgStep>8227a0f8-ce4d-400d-90e1-052b3b6a6974</orgStep></outFields><outFields><name>record_id</name><type>VARCHAR</type><length>100</length><precision>null</precision><columnComment>批次号</columnComment><orgStep>8227a0f8-ce4d-400d-90e1-052b3b6a6974</orgStep></outFields><outFields><name>type</name><type>VARCHAR</type><length>100</length><precision>null</precision><columnComment>类型</columnComment><orgStep>8227a0f8-ce4d-400d-90e1-052b3b6a6974</orgStep></outFields><outFields><name>contents</name><type>TEXT</type><length>2147483647</length><precision>null</precision><columnComment>描述内容</columnComment><orgStep>8227a0f8-ce4d-400d-90e1-052b3b6a6974</orgStep></outFields><outFields><name>c_role</name><type>VARCHAR</type><length>100</length><precision>null</precision><columnComment>角色</columnComment><orgStep>8227a0f8-ce4d-400d-90e1-052b3b6a6974</orgStep></outFields><outFields><name>c_crt_usr</name><type>VARCHAR</type><length>100</length><precision>null</precision><columnComment>创建人名称</columnComment><orgStep>8227a0f8-ce4d-400d-90e1-052b3b6a6974</orgStep></outFields><outFields><name>dt_crt_tm</name><type>DATETIME</type><length>8</length><precision>null</precision><columnComment>创建时间</columnComment><orgStep>8227a0f8-ce4d-400d-90e1-052b3b6a6974</orgStep></outFields><outFields><name>c_upd_usr</name><type>VARCHAR</type><length>100</length><precision>null</precision><columnComment>更新人名称</columnComment><orgStep>8227a0f8-ce4d-400d-90e1-052b3b6a6974</orgStep></outFields><outFields><name>dt_upd_tm</name><type>DATETIME</type><length>8</length><precision>null</precision><columnComment>更新时间</columnComment><orgStep>8227a0f8-ce4d-400d-90e1-052b3b6a6974</orgStep></outFields><outFields><name>is_done</name><type>BIT</type><length>1</length><precision>null</precision><columnComment>是否完成</columnComment><orgStep>8227a0f8-ce4d-400d-90e1-052b3b6a6974</orgStep></outFields><outFields><name>is_del</name><type>BIT</type><length>1</length><precision>null</precision><columnComment>是否删除</columnComment><orgStep>8227a0f8-ce4d-400d-90e1-052b3b6a6974</orgStep></outFields><table>t_element_feedback</table><type>TableInput</type><fields><field><name>id</name><type>Integer</type><length>8</length></field><field><name>record_id</name><type>String</type><length>100</length><columnComment>批次号</columnComment></field><field><name>type</name><type>String</type><length>100</length><columnComment>类型</columnComment></field><field><name>contents</name><type>String</type><length>2147483647</length><columnComment>描述内容</columnComment></field><field><name>c_role</name><type>String</type><length>100</length><columnComment>角色</columnComment></field><field><name>c_crt_usr</name><type>String</type><length>100</length><columnComment>创建人名称</columnComment></field><field><name>dt_crt_tm</name><type>Timestamp</type><length>8</length><columnComment>创建时间</columnComment></field><field><name>c_upd_usr</name><type>String</type><length>100</length><columnComment>更新人名称</columnComment></field><field><name>dt_upd_tm</name><type>Timestamp</type><length>8</length><columnComment>更新时间</columnComment></field><field><name>is_done</name><type>Boolean</type><length>1</length><columnComment>是否完成</columnComment></field><field><name>is_del</name><type>Boolean</type><length>1</length><columnComment>是否删除</columnComment></field></fields><diagramId>8227a0f8-ce4d-400d-90e1-052b3b6a6974</diagramId><sql>SELECT id, record_id, type, contents, c_role, c_crt_usr, dt_crt_tm, c_upd_usr, dt_upd_tm, is_done, is_del FROM t_element_feedback</sql></step><step><name>45392144-ebab-4b61-9641-eeff3232614b</name><type>Formula</type><desc>bds</desc><distribute>Y</distribute><copies>1</copies><outFields><name>dt_upd_tm</name><type>DATETIME</type><length>8</length><precision>null</precision><columnComment>更新时间</columnComment><orgStep>45392144-ebab-4b61-9641-eeff3232614b</orgStep></outFields><outFields><name>is_done</name><type>BIT</type><length>1</length><precision>null</precision><columnComment>是否完成</columnComment><orgStep>45392144-ebab-4b61-9641-eeff3232614b</orgStep></outFields><formula><field_name>dt_upd_tm</field_name><formula_string>NOW()</formula_string><value_type>Timestamp</value_type><value_length>8</value_length><replace_field>dt_upd_tm</replace_field></formula><formula><field_name>is_done</field_name><formula_string>TRUE()</formula_string><value_type>Boolean</value_type><value_length>1</value_length><replace_field>is_done</replace_field></formula><diagramId>45392144-ebab-4b61-9641-eeff3232614b</diagramId></step><step><name>f248b603-cc6a-439f-b684-fd2aa7b251d6</name><type>TableOutput</type><desc>bsc</desc><distribute>Y</distribute><copies>1</copies><connection>26</connection><table>zzbstone</table><schema/><commit>5000</commit><truncate>Y</truncate><ignore_errors>N</ignore_errors><specify_fields>Y</specify_fields><use_batch>Y</use_batch><partitioning_enabled>N</partitioning_enabled><partitioning_field/><partitioning_daily>N</partitioning_daily><partitioning_monthly>N</partitioning_monthly><tablename_in_field>N</tablename_in_field><tablename_field/><tablename_in_table>N</tablename_in_table><return_keys>N</return_keys><return_field/><updateType>append</updateType><insertUpsert>false</insertUpsert><directInsert>false</directInsert><extraProperties>EXTRA_TABLE_DEIFNE=stored as kudu</extraProperties><fields><field><column_name>id</column_name><stream_name>id</stream_name></field><field><column_name>record_id</column_name><stream_name>record_id</stream_name></field><field><column_name>type</column_name><stream_name>type</stream_name></field><field><column_name>contents</column_name><stream_name>contents</stream_name></field><field><column_name>c_role</column_name><stream_name>c_role</stream_name></field><field><column_name>c_crt_usr</column_name><stream_name>c_crt_usr</stream_name></field><field><column_name>dt_crt_tm</column_name><stream_name>dt_crt_tm</stream_name></field><field><column_name>c_upd_usr</column_name><stream_name>c_upd_usr</stream_name></field><field><column_name>dt_upd_tm</column_name><stream_name>dt_upd_tm</stream_name></field><field><column_name>is_done</column_name><stream_name>is_done</stream_name></field><field><column_name>is_del</column_name><stream_name>is_del</stream_name></field></fields><diagramId>f248b603-cc6a-439f-b684-fd2aa7b251d6</diagramId></step><order><hop><id>cb1f2549-77db-4d7f-8c96-52fba1e3fe8d</id><from>9077a5b9-b142-4480-86e9-e7f56952adbc</from><to>210dbc3e-69fd-4b1f-ab74-864590e8b87b</to><enabled>Y</enabled></hop><hop><id>af67a563-f2ae-41e8-b822-0d804df7d892</id><from>8227a0f8-ce4d-400d-90e1-052b3b6a6974</from><to>45392144-ebab-4b61-9641-eeff3232614b</to><enabled>Y</enabled></hop><hop><id>70d573a8-1159-4312-bef2-b3b85b445c4a</id><from>45392144-ebab-4b61-9641-eeff3232614b</from><to>f248b603-cc6a-439f-b684-fd2aa7b251d6</to><enabled>Y</enabled></hop></order><node><nodes><id>ee63a7b9-acf4-4436-820e-cead2b4311df</id><type>TableInput</type><x>269</x><y>256</y><properties><nodeId>8227a0f8-ce4d-400d-90e1-052b3b6a6974</nodeId><width>100</width><height>80</height></properties><zIndex>1083</zIndex></nodes><nodes><id>04ee1020-9b1c-42d2-8e7d-49ffc80d1d56</id><type>Formula</type><x>521</x><y>263</y><properties><nodeId>45392144-ebab-4b61-9641-eeff3232614b</nodeId><width>100</width><height>80</height></properties><zIndex>1091</zIndex></nodes><nodes><id>0a613528-1996-4673-9434-c3b3bbf0e662</id><type>TableOutput</type><x>723</x><y>273</y><properties><nodeId>f248b603-cc6a-439f-b684-fd2aa7b251d6</nodeId><width>100</width><height>80</height></properties><zIndex>1094</zIndex></nodes><edges><id>af67a563-f2ae-41e8-b822-0d804df7d892</id><type>polyline</type><properties></properties><sourceNodeId>ee63a7b9-acf4-4436-820e-cead2b4311df</sourceNodeId><targetNodeId>04ee1020-9b1c-42d2-8e7d-49ffc80d1d56</targetNodeId><sourceAnchorId>ee63a7b9-acf4-4436-820e-cead2b4311df_1</sourceAnchorId><targetAnchorId>04ee1020-9b1c-42d2-8e7d-49ffc80d1d56_3</targetAnchorId><startPoint><x>319</x><y>256</y></startPoint><endPoint><x>471</x><y>263</y></endPoint><zIndex>1084</zIndex><pointsList><x>319</x><y>256</y></pointsList><pointsList><x>395</x><y>256</y></pointsList><pointsList><x>395</x><y>263</y></pointsList><pointsList><x>471</x><y>263</y></pointsList></edges><edges><id>70d573a8-1159-4312-bef2-b3b85b445c4a</id><type>polyline</type><properties></properties><sourceNodeId>04ee1020-9b1c-42d2-8e7d-49ffc80d1d56</sourceNodeId><targetNodeId>0a613528-1996-4673-9434-c3b3bbf0e662</targetNodeId><sourceAnchorId>04ee1020-9b1c-42d2-8e7d-49ffc80d1d56_1</sourceAnchorId><targetAnchorId>0a613528-1996-4673-9434-c3b3bbf0e662_3</targetAnchorId><startPoint><x>571</x><y>263</y></startPoint><endPoint><x>673</x><y>273</y></endPoint><zIndex>1092</zIndex><pointsList><x>571</x><y>263</y></pointsList><pointsList><x>622</x><y>263</y></pointsList><pointsList><x>622</x><y>273</y></pointsList><pointsList><x>673</x><y>273</y></pointsList></edges></node></transformation>