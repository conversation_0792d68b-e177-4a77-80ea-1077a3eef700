package com.dib.model.controller;

import com.dib.common.core.domain.entity.ReturnT;
import com.dib.model.domain.TModelMappingTable;
import com.dib.model.service.TModelMappingTableService;
import com.dib.model.vo.TModelMappingTableVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模型数据映射表管理
 */
@RestController
@RequestMapping("/model-mapping")
public class ModelMappingTableController {

    @Autowired
    private TModelMappingTableService modelMappingTableService;

    /**
     * 创建模型映射信息的接口
     *
     * @param modelMappingTable 模型映射信息实体
     * @return 创建后的模型映射信息实体及 HTTP 状态码
     */
    @PostMapping
    public ReturnT<TModelMappingTableVo> create(@RequestBody TModelMappingTableVo modelMappingTable) {
        try {
            TModelMappingTableVo createdModelMappingTable = modelMappingTableService.create(modelMappingTable);
            return ReturnT.success(createdModelMappingTable);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("创建模型映射信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据模型库ID获取所有模型映射信息的接口
     *
     * @return 模型映射信息列表及 HTTP 状态码
     */
    @GetMapping("/list/{modelLibNo}")
    public ReturnT<List<TModelMappingTable>> getModelMappingTables(@PathVariable Long modelLibNo) {
        try {
            List<TModelMappingTable> modelMappingTables = modelMappingTableService.getModelMappingTable(modelLibNo);
            return ReturnT.success(modelMappingTables);
        } catch (Exception e) {
            e.printStackTrace();
            return ReturnT.fail("根据模型库ID获取所有模型映射信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据 ID 获取模型映射信息的接口
     *
     * @param id 模型映射信息的 ID
     * @return 对应的模型映射信息实体及 HTTP 状态码
     */
    @GetMapping("/{id}")
    public ReturnT<TModelMappingTable> getById(@PathVariable Long id) {
        try {
            TModelMappingTable modelMappingTable = modelMappingTableService.getModelMappingTableById(id);
            return ReturnT.success(modelMappingTable);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("根据 ID 获取模型映射信息失败: " + e.getMessage());
        }

    }

    /**
     * 更新模型映射信息的接口
     *
     * @param modelMappingTable 模型映射信息实体
     * @return 更新后的模型映射信息实体及 HTTP 状态码
     */
    @PutMapping
    public ReturnT<TModelMappingTable> update(@RequestBody TModelMappingTable modelMappingTable) {
        try {
            TModelMappingTable updatedModelMappingTable = modelMappingTableService.update(modelMappingTable);
            return ReturnT.success(updatedModelMappingTable);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("更新模型映射信息失败: " + e.getMessage());
        }

    }

    /**
     * 根据 ID 删除模型映射信息的接口
     *
     * @param id 模型映射信息的 ID
     * @return HTTP 状态码
     */
    @DeleteMapping("/{id}")
    public ReturnT<Boolean> delete(@PathVariable Long id) {
        try {
            Boolean result = modelMappingTableService.delete(id);
            return ReturnT.success(result);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return ReturnT.fail("根据 ID 删除模型映射信息失败: " + e.getMessage());
        }

    }
}
