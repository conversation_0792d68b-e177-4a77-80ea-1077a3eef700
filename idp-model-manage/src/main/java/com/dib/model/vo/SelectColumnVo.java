package com.dib.model.vo;

import jakarta.validation.constraints.NotNull;
import java.util.List;

public class SelectColumnVo {

    /**数据源ID**/
    private Integer sourceId;

    /**数据表ID**/
    @NotNull.List({@NotNull})
    private List<String> tableId;

    /**字段名称**/
    @NotNull.List(@NotNull)
    private List<String> columnlist;

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public List<String> getTableId() {
        return tableId;
    }

    public void setTableId(List<String> tableId) {
        this.tableId = tableId;
    }

    public List<String> getColumnlist() {
        return columnlist;
    }

    public void setColumnlist(List<String> columnlist) {
        this.columnlist = columnlist;
    }
}
