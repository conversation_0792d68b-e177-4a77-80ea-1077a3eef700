package com.dib.model.vo;

import com.dib.common.utils.DateUtils;
import com.dib.model.domain.TModelLibInfo;
import org.apache.commons.lang3.RandomUtils;
import jakarta.validation.constraints.NotBlank;

public class TModelLibInfoVo {

    /**
     * 序号
     */
    @NotBlank(message = "模型库ID不能为空")
    private Long id;

    /**
     * 模型库标题
     */
    private String modelLibName;

    /**
     * 模型数
     */
    private Integer modelCount;

    /**
     * 资源 ID
     */
    private String sourceId;

    /**
     * 数据存储连接池 ID
     */
    private Integer databasePoolId;

    /**
     * 描述信息
     */
    private String mark;

    /**
     * 最后修改时间
     */
    private String modifyDate;

    public TModelLibInfoVo(TModelLibInfo modelLibInfo) {
        this.id = modelLibInfo.getId();
        this.modelLibName = modelLibInfo.getModelLibName();
        this.sourceId = modelLibInfo.getSourceId();
        this.databasePoolId = modelLibInfo.getDatabasePoolId();
        this.mark = modelLibInfo.getMark();
        this.databasePoolId = modelLibInfo.getDatabasePoolId();
        this.modelCount = RandomUtils.nextInt(1,20);
        this.modifyDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,modelLibInfo.getModifyDate());
    }

    public TModelLibInfoVo() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getModelLibName() {
        return modelLibName;
    }

    public void setModelLibName(String modelLibName) {
        this.modelLibName = modelLibName;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public Integer getDatabasePoolId() {
        return databasePoolId;
    }

    public void setDatabasePoolId(Integer databasePoolId) {
        this.databasePoolId = databasePoolId;
    }

    public String getMark() {
        return mark;
    }

    public void setMark(String mark) {
        this.mark = mark;
    }

    public Integer getModelCount() {
        //通过库ID, 查询模型数
        return modelCount;
    }

    public void setModelCount(Integer modelCount) {
        this.modelCount = modelCount;
    }

    public String getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }
}
