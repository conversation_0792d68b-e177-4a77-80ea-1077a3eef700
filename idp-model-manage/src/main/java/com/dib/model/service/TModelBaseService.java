package com.dib.model.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dib.metadata.vo.MetadataColumnVo;
import com.dib.metadata.vo.SqlConsoleVo;
import com.dib.model.ModelTableVo;
import com.dib.model.domain.TModelBase;
import com.dib.model.vo.*;

import java.sql.SQLException;
import java.util.List;

public interface TModelBaseService extends IService<TModelBase> {
    TModelBase create(TModelBaseAddVo modelBase) throws SQLException;

    TModelBase getModelBaseInfo(Long id);

    TModelBase update(TModelBase modelBase);

    boolean delete(Long id);

    List<TModelBaseVo> getModelBaseList(Long modelLibNo);

    boolean importModelList(List<TModelBaseVo> modelBasesList);

    List<MetadataColumnVo> queryColList(SelectColumnVo selectColumnVo);

    List<TModelBase> queryAllModelList();

    IndictorDimVo queryIndicAndDimList(String modelNo);

    List<SqlConsoleVo> queryModelData(List<String> indictorAttrs,String modelNo) throws SQLException;

    Boolean tableModelStructSync(String modelNo);

    SqlConsoleVo lockTableByModelNo(String modelNo,String isLock)throws SQLException;

    List<ModelTableVo> modelAllTabByModelNo();

    //ChartViewDTO getChartViewDTOData(ChartViewDTO view);
}
