package com.dib.model.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.common.utils.DateUtils;
import com.dib.common.utils.StringUtils;
import com.dib.common.utils.bean.BeanUtils;
import com.dib.common.utils.uuid.UUID;
import com.dib.metadata.dto.MetadataColumnMarketDto;
import com.dib.metadata.dto.MetadataCreateSqlDto;
import com.dib.metadata.dto.SqlConsoleDto;
import com.dib.metadata.entity.MetadataSourceEntity;
import com.dib.metadata.service.*;
import com.dib.metadata.vo.MetadataColumnVo;
import com.dib.metadata.vo.SqlConsoleVo;
import com.dib.model.ModelTableVo;
import com.dib.model.domain.TModelBase;
import com.dib.model.domain.TModelColumn;
import com.dib.model.mapper.TModelBaseMapper;
import com.dib.model.mapper.TModelColumnMapper;
import com.dib.model.service.TModelBaseService;
import com.dib.model.utils.SnowflakeUtils;
import com.dib.model.vo.*;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TModelBaseServiceImpl extends ServiceImpl<TModelBaseMapper, TModelBase> implements TModelBaseService {

    @Autowired
    private TModelColumnMapper tModelColumnMapper;

    @Autowired
    private MetadataColumnService metadataColumnService;

    @Autowired
    private MetadataSourceService metadataSourceService;

    @Autowired
    private SqlConsoleService sqlConsoleService;

    @Autowired
    private MetadataCreateTableService metadataCreateTableService;


    @Override
    @Transactional
    public TModelBase create(TModelBaseAddVo modelBase) throws SQLException {
        //保存模型
        TModelBase tModelBase = new TModelBase();
        tModelBase.setModelName(modelBase.getModelName());
        tModelBase.settName(modelBase.gettName());
        tModelBase.setTitle(modelBase.getTitle());
        tModelBase.setSourceId(new BigDecimal(26).toString());
        if (modelBase.getTableIds() != null && modelBase.getTableIds().length > 0) {
            tModelBase.setTableId(Arrays.stream(modelBase.getTableIds()).collect(Collectors.joining(",")));
        }
        tModelBase.setRemark(modelBase.getRemark());
        tModelBase.setModelUuid(UUID.randomUUID().toString());
        tModelBase.setCodeNo("DIB" + RandomUtils.nextInt(1000, 9999));
        tModelBase.setModelLibNo(modelBase.getModelLibNo());
        tModelBase.setModelType(4);
        save(tModelBase);
        //保存模型字段
        TModelColumn[] tModelColumns = modelBase.gettModelColumns();
        if (tModelColumns != null && tModelColumns.length > 0) {
            for (TModelColumn tModelColumn : modelBase.gettModelColumns()) {
                tModelColumn.setModelNo(tModelBase.getId());
                tModelColumn.setId(SnowflakeUtils.nextId());
                tModelColumnMapper.insert(tModelColumn);
            }
        }

        //将模型字段转换成表结构字段
        List<MetadataColumnMarketDto> metadataColumnEntities = new ArrayList<>();
        for (TModelColumn tModelColumn : tModelColumns) {
            MetadataColumnMarketDto metadataColumnEntity = new MetadataColumnMarketDto();
            if (tModelColumn.getTitle().equals("id")) {
                metadataColumnEntity.setColumnKey("1");
            }
            metadataColumnEntity.setSourceId(tModelBase.getSourceId());
            metadataColumnEntity.setTableName(tModelBase.gettName());
            metadataColumnEntity.setColumnName(tModelColumn.gettName());
            metadataColumnEntity.setColumnComment(tModelColumn.getRemark());
            metadataColumnEntity.setDataLength(new BigDecimal(ObjectUtil.defaultIfNull(tModelColumn.gettLength(), 0)).toString());
            metadataColumnEntity.setDataPrecision(new BigDecimal(ObjectUtil.defaultIfNull(tModelColumn.getDecimalDigits(), 0)).toString());
            metadataColumnEntity.setColumnNullable(tModelColumn.getIsNull() == 1 ? "Y" : "N");
            metadataColumnEntity.setActionType("addColumn");
            metadataColumnEntities.add(metadataColumnEntity);
        }
        MetadataCreateSqlDto metadataCreateSqlDto = new MetadataCreateSqlDto();
        metadataCreateSqlDto.setTargetTableName(tModelBase.gettName());
        MetadataSourceEntity metadataSourceEntity = metadataSourceService.getBaseMapper().selectById(tModelBase.getSourceId());
        metadataCreateSqlDto.setTargetId(metadataSourceEntity.getId());
        metadataCreateSqlDto.setTargetSchemaName(metadataSourceEntity.getDbSchema().getSid());
        metadataCreateSqlDto.setMetadataColumnMarketDtoList(metadataColumnEntities);
        metadataCreateSqlDto.setSourceTableRemarks(tModelBase.getRemark());
        // metadataCreateSqlDto.setTableSql(sqlCreatTable);
        metadataCreateSqlDto.setSqlType("createTable");
        String sqlCreatTable = metadataCreateTableService.createSql(metadataCreateSqlDto);
        metadataCreateSqlDto.setTableSql(sqlCreatTable);
        log.debug("sqlCreateTable:" + sqlCreatTable);
        String tableId = metadataCreateTableService.runSql(metadataCreateSqlDto);
        tModelBase.setTableId(tableId);
        updateById(tModelBase);
        return tModelBase;
    }

    @Override
    public List<TModelBaseVo> getModelBaseList(Long modelLibNo) {

        // 使用 QueryWrapper 构建查询条件
        QueryWrapper<TModelBase> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("model_lib_no", modelLibNo);
        // 调用 Mapper 的 selectList 方法查询数据
        List<TModelBase> modelBaseList = baseMapper.selectList(queryWrapper);

        List<TModelBaseVo> voList = modelBaseList.stream().map(modelBase -> {
            TModelBaseVo vo = new TModelBaseVo();
            BeanUtils.copyBeanProp(vo, modelBase);
            vo.setIsLock(modelBase.getIsLock());
            vo.setModifyDate(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", modelBase.getModifyDate()));
            return vo;
        }).collect(Collectors.toList());
        return voList;
    }


    @Override
    @Transactional
    public boolean importModelList(List<TModelBaseVo> modelBasesList) {

        List<TModelBase> modelBases = modelBasesList.stream().map(modelBaseVo -> {
            // 实现从 ModelBaseVo 到 TModelBase 的转换逻辑
            TModelBase modelBase = new TModelBase();
            modelBase.setId(SnowflakeUtils.nextId());
            modelBase.settName(modelBaseVo.gettName());
            modelBase.setModelName(modelBaseVo.getTitle());
            modelBase.setTitle(modelBaseVo.getTitle());
            modelBase.setModelUuid(modelBaseVo.getModelUuid());
            modelBase.setCodeNo(modelBaseVo.getCodeNo());
            if (modelBaseVo.getModelLibNo() == null) {
                throw new RuntimeException("模型库编号不能为空");
            }
            modelBase.setModelLibNo(new BigDecimal(modelBaseVo.getModelLibNo()).longValue());
            modelBase.setRemark(modelBaseVo.getRemark());
            return modelBase;
        }).collect(Collectors.toList());
        return saveOrUpdateBatch(modelBases, modelBases.size());
    }

    @Override
    public List<MetadataColumnVo> queryColList(SelectColumnVo selectColumnVo) {

        Integer sourceId = selectColumnVo.getSourceId();
        List<String> tableIds = selectColumnVo.getTableId();
        List<String> columnNames = selectColumnVo.getColumnlist();
        List<MetadataColumnVo> metadataColumnEntityList = metadataColumnService.getColumnsByTableIds(tableIds);
        metadataColumnEntityList.removeIf(column -> !columnNames.contains(column.getColumnName()));
        return metadataColumnEntityList;
    }

    @Override
    public List<TModelBase> queryAllModelList() {
        QueryWrapper<TModelBase> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("model_type", 4);
        List<TModelBase> modelBaseList = baseMapper.selectList(queryWrapper);
        return modelBaseList;
    }

    @Override
    public IndictorDimVo queryIndicAndDimList(String modelNo) {
        IndictorDimVo indictorDimVo = new IndictorDimVo();
        if (StringUtils.isNotBlank(modelNo)) {
            Long modelID = Long.parseLong(modelNo);
            TModelBase modelBase = baseMapper.selectById(modelID);
            if (modelBase != null) {
                List<IndictorAssetVo> indictorAssetVoList = new ArrayList<>();
                List<DimAssetVo> dimAssetVoList = new ArrayList<>();
                List<TModelColumn> modelColumnList = tModelColumnMapper.selectByModelNo(modelID);

                for (TModelColumn modelColumn : modelColumnList) {

                    if (modelColumn.getIsDim() == 1) {
                        //维度
                        DimAssetVo dimAssetVo = new DimAssetVo();
                        dimAssetVo.setName(modelColumn.getTitle());
                        dimAssetVo.setId(new BigDecimal(modelColumn.getId()).toString());
                        dimAssetVo.setColumnIndex(modelColumn.getFieldSortNum());
                        dimAssetVo.setType(modelColumn.getDataLevel());
                        dimAssetVo.setDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
                        dimAssetVo.setDateStyle(modelColumn.getDataLevel());
                        dimAssetVo.setFieldShortName(modelColumn.getAliasName());
                        dimAssetVo.setDeType(0);
                        dimAssetVo.setExtField(0);
                        dimAssetVo.setGroupType("d");
                        dimAssetVoList.add(dimAssetVo);
                    } else {
                        //指标
                        IndictorAssetVo indictorAssetVo = new IndictorAssetVo();
                        indictorAssetVo.setId(new BigDecimal(modelColumn.getId()).toString());
                        indictorAssetVo.setName(modelColumn.gettName());
                        indictorAssetVo.setColumnIndex(modelColumn.getFieldSortNum());
                        indictorAssetVo.setCustomSort(modelColumn.getFieldSortNum());
                        indictorAssetVo.setChecked(true);
                        indictorAssetVo.setDataeaseName(modelColumn.gettName());
                        indictorAssetVo.setId(new BigDecimal(modelColumn.getId()).toString());
                        indictorAssetVo.setType(modelColumn.getDataLevel());
                        indictorAssetVo.setDateFormat(DateUtils.YYYY_MM_DD_HH_MM_SS);
                        indictorAssetVo.setDateStyle(modelColumn.getDataLevel());
                        indictorAssetVo.setFieldShortName(modelColumn.getAliasName());
                        indictorAssetVo.setGroupType("q");
                        indictorAssetVo.setDeType(2);
                        indictorAssetVo.setExtField(1);
                        indictorAssetVo.setChartType("bar");
                        indictorAssetVoList.add(indictorAssetVo);
                    }
                }
                indictorDimVo.setDimAssetVoList(dimAssetVoList);
                indictorDimVo.setIndictorAssetVoList(indictorAssetVoList);
            }
        }
        return indictorDimVo;
    }

    @Override
    public List<SqlConsoleVo> queryModelData(List<String> indictorAttrs,String modelNo) throws SQLException {
        SqlConsoleDto sqlConsoleDto = new SqlConsoleDto();
        sqlConsoleDto.setSqlKey(new BigDecimal(DateTime.now().getTime()).toString());
        TModelBase tModelBase = getModelBaseInfo(new BigDecimal(modelNo).longValue());
        sqlConsoleDto.setSourceId(tModelBase.getSourceId());
        sqlConsoleDto.setTableName(tModelBase.gettName());

        MetadataSourceEntity metadataSourceEntity = metadataSourceService.getMetadataSourceById(tModelBase.getSourceId());
        String DbName = metadataSourceEntity.getDbSchema().getDbName();
        String sid = metadataSourceEntity.getDbSchema().getSid();
        if(indictorAttrs !=null && indictorAttrs.size() > 0){
            String queryColumn =  String.join(",", indictorAttrs);
            sqlConsoleDto.setSqlText("select "+queryColumn+" from " + DbName + "." + sid + "." + tModelBase.gettName() + ";");
        }else {
            sqlConsoleDto.setSqlText("select * from " + DbName + "." + sid + "." + tModelBase.gettName() + ";");
        }
        List<SqlConsoleVo> sqlConsoleVos = sqlConsoleService.sqlRun(sqlConsoleDto);
        return sqlConsoleVos;
    }

    @Override
    public Boolean tableModelStructSync(String modelNo) {
        TModelBase modelBase = getModelBaseInfo(new BigDecimal(modelNo).longValue());
        //查询最新表结构
        List<TModelColumn> tModelColumns = tModelColumnMapper.selectByModelNo(new BigDecimal(modelNo).longValue());
        //查询表结构
        List<MetadataColumnVo> metadataColumnVos = metadataColumnService.getColumnsByTableIds(Arrays.asList(modelBase.getTableId()));

        MetadataCreateSqlDto metadataCreateSqlDto = new MetadataCreateSqlDto();
        metadataCreateSqlDto.setTargetTableName(modelBase.gettName());
        MetadataSourceEntity metadataSourceEntity = metadataSourceService.getBaseMapper().selectById(modelBase.getSourceId());
        //重新创建表
        List<MetadataColumnMarketDto> columnMarketDtos = new ArrayList<>();
        for (TModelColumn tModelColumn : tModelColumns) {
            MetadataColumnMarketDto metadataColumnMarketDto = new MetadataColumnMarketDto();
            metadataColumnMarketDto.setTableId(modelBase.getTableId());
            metadataColumnMarketDto.setColumnName(tModelColumn.gettName());
            metadataColumnMarketDto.setColumnComment(tModelColumn.getRemark());
            metadataColumnMarketDto.setDataType(tModelColumn.getDataLevel());
            metadataColumnMarketDto.setDataLength(new BigDecimal(tModelColumn.gettLength() == null ? 0 : tModelColumn.gettLength()).toString());
            metadataColumnMarketDto.setSourceId(modelBase.getSourceId());
            metadataColumnMarketDto.setTableName(modelBase.gettName());
            String columnName = tModelColumn.gettName();
            Boolean isExist = metadataColumnVos != null && columnName != null && metadataColumnVos.stream().filter(Objects::nonNull).map(MetadataColumnVo::getColumnName).anyMatch(columnName::equals);
            if (!isExist) {
                metadataColumnMarketDto.setActionType("addColumn");
            }
//            else {
//                metadataColumnMarketDto.setActionType("addColumn");
//            }
            //列的操作
            columnMarketDtos.add(metadataColumnMarketDto);
        }
        metadataCreateSqlDto.setMetadataColumnMarketDtoList(columnMarketDtos);
        metadataCreateSqlDto.setTargetSchemaName(metadataSourceEntity.getDbSchema().getSid());
        metadataCreateSqlDto.setSqlType("updateTable");
        metadataCreateSqlDto.setTargetId(metadataSourceEntity.getId());
        String sqlCreatTable = metadataCreateTableService.createSql(metadataCreateSqlDto);
        metadataCreateSqlDto.setTableSql(sqlCreatTable);
        log.debug("updateTable:" + sqlCreatTable);
        metadataCreateTableService.runSql(metadataCreateSqlDto);
        return true;
    }

    @Override
    public SqlConsoleVo lockTableByModelNo(String modelNo, String isLock) throws SQLException {

        TModelBase tModelBase = getModelBaseInfo(new BigDecimal(modelNo).longValue());
        MetadataSourceEntity metadataSourceEntity = metadataSourceService.getMetadataSourceById(tModelBase.getSourceId());
        if (metadataSourceEntity == null) {
            throw new SQLException("数据源不存在");
        }
        String dbName = metadataSourceEntity.getDbSchema().getDbName();
        String sid = metadataSourceEntity.getDbSchema().getSid();
        SqlConsoleVo sqlConsoleVo = new SqlConsoleVo();
        sqlConsoleVo.setSuccess(true);
        sqlConsoleVo.setTime(new Date().getTime());
        if (isLock.equals("1")) {
            tModelBase.setIsLock(1);
            sqlConsoleVo.setSql("LOCK TABLES " + dbName + "." + sid + "." + tModelBase.gettName() + "READ");
        } else {
            tModelBase.setIsLock(0);
            //Todo  解锁
            sqlConsoleVo.setSql("UNLOCK TABLES " + dbName + "." + sid + "." + tModelBase.gettName());
        }
        updateById(tModelBase);
        return sqlConsoleVo;
    }

    @Override
    public List<ModelTableVo> modelAllTabByModelNo() {

        List<ModelTableVo> modelTableVos = new ArrayList<>();

        //查询模型管理模块中所有的表包含（数据源id,表id,字段列表）
        List<TModelBase> modelBases = list();

        //查询所有模型列表
        List<TModelColumn> tModelColumns = tModelColumnMapper.selectList(new QueryWrapper<TModelColumn>().isNotNull("model_no"));

        if (modelBases != null && modelBases.size() > 0) {
            modelTableVos = modelBases.stream().map(modelBase -> {
                ModelTableVo modelTableVo = new ModelTableVo();
                modelTableVo.setModelId(modelBase.getId());
                modelTableVo.setTabName(modelBase.gettName());
                modelTableVo.setTableId(modelBase.getTableId());
                modelTableVo.setSourceId(modelBase.getSourceId());
                if (tModelColumns != null && tModelColumns.size() > 0){
                    modelTableVo.setModelColumns(tModelColumns.stream().filter(tModelColumn -> tModelColumn.getModelNo().equals(modelBase.getId())).map(TModelColumn::gettName).toList());
                }
                return modelTableVo;
            }).collect(Collectors.toList());
        }
        return modelTableVos;
    }



    @Override
    public TModelBase getModelBaseInfo(Long id) {
        return getById(id);
    }

    @Override
    public TModelBase update(TModelBase modelBase) {
        updateById(modelBase);
        return modelBase;
    }

    @Override
    public boolean delete(Long id) {
        return removeById(id);
    }
}