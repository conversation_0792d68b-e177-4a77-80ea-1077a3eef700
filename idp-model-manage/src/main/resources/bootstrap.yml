server:
  port: 8091

spring:
  application:
    name: idp-model-manage
  profiles:
    active: local
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      server-addr: 192.168.10.49:8848
      username: admin
      password: nacos
      discovery:
        namespace: local
        group: DEFAULT_GROUP
      config:
        namespace: local
        file-extension: yaml
        shared-configs:
          - data-id: common-druid.yaml
            group: DEFAULT_GROUP
            refresh: true