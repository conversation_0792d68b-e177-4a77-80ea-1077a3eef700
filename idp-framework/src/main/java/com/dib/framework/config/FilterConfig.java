//package com.dib.framework.config;
//
//import jakarta.servlet.DispatcherType;
//import org.springframework.boot.web.servlet.FilterRegistrationBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import com.dib.common.filter.RepeatableFilter;
//
///**
// * Filter配置
// *
// * <AUTHOR>
// */
//@Configuration
//public class FilterConfig
//{
//    @SuppressWarnings({ "rawtypes", "unchecked" })
//    @Bean
//    public FilterRegistrationBean someFilterRegistration()
//    {
//        FilterRegistrationBean registration = new FilterRegistrationBean();
//        registration.setFilter(new RepeatableFilter());
//        registration.addUrlPatterns("/*");
//        registration.setName("repeatableFilter");
//        registration.setOrder(FilterRegistrationBean.LOWEST_PRECEDENCE);
//        return registration;
//    }
//}
