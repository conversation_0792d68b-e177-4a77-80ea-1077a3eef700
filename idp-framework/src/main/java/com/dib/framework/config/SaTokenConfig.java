package com.dib.framework.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
public class SaTokenConfig implements WebMvcConfigurer {


    @Value("${dibase.login-exclude-url:}")
    private String[] loginExcludeUrl;

    /**
     * 拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        List<String> list = new ArrayList<>(Arrays.asList(loginExcludeUrl));


        // 注册 Sa-Token 拦截器，校验规则为 StpUtil.checkLogin() 登录校验。
        registry.addInterceptor(new SaInterceptor(handle -> {
                    // 获取当前请求方法
                    String method = cn.dev33.satoken.SaManager.getSaTokenContext().getRequest().getMethod();
                    // 如果是OPTIONS请求，直接放行
                    if ("OPTIONS".equalsIgnoreCase(method)) {
                        return;
                    }
                    // 否则进行登录校验
                    StpUtil.checkLogin();
                }))
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(list); // 放行登录接口和其他排除的路径
    }
}
