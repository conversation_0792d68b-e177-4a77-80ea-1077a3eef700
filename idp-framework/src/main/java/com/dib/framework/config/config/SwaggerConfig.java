package com.dib.framework.config.config;

import com.dib.common.config.IdpConfig;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springdoc.core.models.GroupedOpenApi;
import org.springdoc.core.customizers.OpenApiCustomizer;

/**
 * OpenAPI 3.0 配置
 * 
 * <AUTHOR>
 */
@Configuration
@OpenAPIDefinition(
    info = @Info(
        title = "IDP管理系统API",
        version = "3.8.2",
        description = "用于管理集团旗下公司的人员信息,具体包括XXX,XXX模块..."
    )
)
public class SwaggerConfig
{
    /** 系统基础配置 */
    @Autowired
    private IdpConfig idpConfig;

    /** 是否开启swagger */
    @Value("${swagger.enabled:true}")
    private boolean enabled;

    /** 设置请求的统一前缀 */
    @Value("${swagger.pathMapping:/}")
    private String pathMapping;

    /**
     * 创建API分组
     */
    @Bean
    public GroupedOpenApi createRestApi() {
        return GroupedOpenApi.builder()
                .group("默认接口")
                .pathsToMatch("/**")
                .addOpenApiCustomizer(openApiCustomizer())
                .build();
    }

    /**
     * 自定义OpenAPI配置
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .openapi("3.0.1") // 明确指定OpenAPI版本
                .schemaRequirement("Authorization", securityScheme());
    }

    /**
     * 自定义OpenAPI定制器
     */
    @Bean
    public OpenApiCustomizer openApiCustomizer() {
        return openApi -> {
            if (!enabled) {
                openApi.paths(null); // 如果不启用，则清空所有路径
            }
            
            // 添加安全要求
            openApi.addSecurityItem(new SecurityRequirement().addList("Authorization"));
        };
    }

    /**
     * 安全模式，这里指定token通过Authorization头请求头传递
     */
    private SecurityScheme securityScheme() {
        return new SecurityScheme()
                .name("Authorization")
                .type(SecurityScheme.Type.HTTP)
                .scheme("bearer")
                .bearerFormat("JWT")
                .in(SecurityScheme.In.HEADER);
    }
}
