package com.dib.index.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dib.common.database.utils.SecurityUtil;
import com.dib.index.domain.TIndicatorTemplate;
import com.dib.index.service.TIndicatorTemplateService;
import com.dib.index.utils.ReturnT;
import com.dib.index.vo.TIndicatorTemplateVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 指标模板管理接口
 */
@Tag(name = "指标模板管理")
@RestController
@Slf4j
@RequestMapping("/templates")
public class TIndicatorTemplateController {
    @Autowired
    private TIndicatorTemplateService tIndicatorTemplateService;

    /**
     * 根据指标库id获取当前库下指标模板
     * @param indicatorNo 指定的指标库id
     * @return 指标模板视图对象列表
     */
    @Operation(summary = "根据指标库ID获取指标模板列表")
    @GetMapping("/{indicatorNo}")
    public List<TIndicatorTemplateVo> getTemplatesByDimNo(@PathVariable Long indicatorNo) {
        return tIndicatorTemplateService.getTemplatesByIndicatorNo(indicatorNo);
    }

    /**
     * 创建对应指标库下指标模板
     * @param indicatorNo 指定的指标库id
     * @param templateVo 指标模板实体
     * @return 创建结果
     */
    @Operation(summary = "创建指标模板")
    @PostMapping("/{indicatorNo}/create")
    public ReturnT<Boolean> createTemplate(@PathVariable Long indicatorNo, @RequestBody TIndicatorTemplateVo templateVo) {
        TIndicatorTemplate template = new TIndicatorTemplate();
        template.setAttributeName(templateVo.getAttributeName());
        template.setAttributeTitle(templateVo.getAttributeTitle());
        template.setAttributeCategory(templateVo.getAttributeCategory());
        template.setFieldLength(templateVo.getFieldLength());
        template.setIsNullAllowed(templateVo.getIsNullAllowed());

        Date date = new Date();
        // 确保 id 字段为 null
        template.setId(null);
        template.setCreatorId(SecurityUtil.getUserId());
        template.setCreatorName(SecurityUtil.getUserName());
        template.setModifierId(SecurityUtil.getUserId());
        template.setModifier(SecurityUtil.getUserName());
        template.setCreateDate(date);
        template.setModifyDate(date);
        template.setIndicatorNo(indicatorNo);

        try {
            boolean result = tIndicatorTemplateService.save(template);
            return ReturnT.success(result);
        } catch (Exception e) {
            return ReturnT.fail("创建指标模板失败: " + e.getMessage());
        }
    }

    /**
     * 更新对应指标库下指定的指标模板
     * @param indicatorNo 指定的指标库id
     * @param id 指定需要更新的指标模板id
     * @param templateVo 指标模板实体
     * @return 更新结果
     */
    @Operation(summary = "更新指标模板")
    @PutMapping("/{indicatorNo}/update/{id}")
    public ReturnT<Boolean> updateTemplate(@PathVariable Long indicatorNo, @PathVariable Long id, @RequestBody TIndicatorTemplateVo templateVo) {
        TIndicatorTemplate template = new TIndicatorTemplate();
        template.setAttributeName(templateVo.getAttributeName());
        template.setAttributeTitle(templateVo.getAttributeTitle());
        template.setAttributeCategory(templateVo.getAttributeCategory());
        template.setFieldLength(templateVo.getFieldLength());
        template.setIsNullAllowed(templateVo.getIsNullAllowed());

        template.setModifierId(SecurityUtil.getUserId());
        template.setModifier(SecurityUtil.getUserName());
        template.setId(id);
        template.setModifyDate(new Date());
        template.setIndicatorNo(indicatorNo);
        try {
            boolean result = tIndicatorTemplateService.updateById(template);
            return ReturnT.success(result);
        } catch (Exception e) {
            return ReturnT.fail("更新指标模板失败: " + e.getMessage());
        }
    }

    /**
     * 删除对应指标库下指定的指标模板
     * @param indicatorNo 指定的指标库id
     * @param id 指定需要删除的指标模板id
     * @return 删除结果
     */
    @Operation(summary = "删除指定指标模板")
    @DeleteMapping("/{indicatorNo}/delete/{id}")
    public boolean deleteTemplate(@PathVariable Long indicatorNo, @PathVariable Long id) {
        LambdaQueryWrapper<TIndicatorTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TIndicatorTemplate::getIndicatorNo, indicatorNo).eq(TIndicatorTemplate::getId, id);
        return tIndicatorTemplateService.remove(wrapper);
    }

    /**
     * 删除对应指标库下所有的指标模板
     * @param indicatorNo 指定的指标库id
     * @return 删除结果
     */
    @Operation(summary = "删除指标库下所有模板")
    @DeleteMapping("/{indicatorNo}/delete")
    public boolean deleteTemplatesByDimNo(@PathVariable Long indicatorNo) {
        return tIndicatorTemplateService.deleteTemplatesByIndicatorNo(indicatorNo);
    }
}
