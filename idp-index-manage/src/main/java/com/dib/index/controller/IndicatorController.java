package com.dib.index.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dib.index.domain.TIndicatorInfo;
import com.dib.index.service.*;
import com.dib.index.utils.ReturnT;
import com.dib.index.vo.*;
import com.dib.metadata.entity.MetadataTableEntity;
import com.dib.metadata.service.MetadataColumnService;
import com.dib.metadata.service.MetadataSourceService;
import com.dib.metadata.service.MetadataTableService;
import com.dib.metadata.vo.MetadataColumnVo;
import com.dib.metadata.vo.MetadataSourceVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 指标的管理接口
 */
@Tag(name = "指标管理")
@RestController
@Slf4j
@RequestMapping("/indicator")
public class IndicatorController {

    @Autowired
    private TIndicatorService indicatorInfoService;

    @Autowired
    private MetadataSourceService metadataSourceService;

    @Autowired
    private MetadataTableService metadataTableService;

    @Autowired
    private MetadataColumnService metadataColumnService;

    @Autowired
    private TColAttrUnionService tColAttrUnionService;

    /**
     * 创建指标信息
     * @param indicatorInfo 指标信息实体
     * @return 创建结果
     */
    @Operation(summary = "创建指标信息")
    @PostMapping("/create")
    public ReturnT<Boolean> createIndicatorInfo(@RequestBody TIndicatorInfo indicatorInfo) {
        try {
            boolean result = indicatorInfoService.save(indicatorInfo);
            return ReturnT.success(result);
        } catch (Exception e) {
            return ReturnT.fail("创建指标信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询指标信息
     * @param id 指标信息ID
     * @return 指标信息实体
     */
    @Operation(summary = "根据ID查询指标信息")
    @GetMapping("/{id}")
    public ReturnT<TIndicatorInfoVo> getIndicatorInfoById(@PathVariable Long id) {
        try {
            TIndicatorInfoVo tIndicatorInfoVo = indicatorInfoService.getIndicatorInfoById(id);
            return ReturnT.success(tIndicatorInfoVo);
        } catch (Exception e) {
            log.error("根据ID查询指标信息失败", e);
            return ReturnT.fail("查询指标信息失败: " + e.getMessage());
        }
    }



    /**
     * 根据指标库id查询指标信息
     * @param indexLibId  指标库ID或者分组ID
     * @return
     */
    @Operation(summary = "根据指标库id查询指标信息")
    @GetMapping("/listByIndictorLibId/{indexLibId}")
    public ReturnT<List<TIndicatorInfo>> listByIndicatorLibId(@PathVariable Long indexLibId) {
        try {
            List<TIndicatorInfo> indicatorInfos = indicatorInfoService.ListByIndexLibId(indexLibId);
            return ReturnT.success(indicatorInfos);
        } catch (Exception e) {
            return ReturnT.fail("根据指标库id查询指标信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据指标库id查询指标和指标分组信息
     * @param indexLibId  指标库ID或者分组ID
     * @return
     */
    @Operation(summary = "根据指标库id查询指标和指标分组信息")
    @GetMapping("/groupAndIndexBylibId/{indexLibId}")
    public ReturnT<List<TIndicatorInfoVo>> groupAndIndexByLibId(@PathVariable Long indexLibId) {
        try {
            List<TIndicatorInfoVo> indicatorInfos = indicatorInfoService.groupAndIndexBylibId(indexLibId);
            return ReturnT.success(indicatorInfos);
        } catch (Exception e) {
            return ReturnT.fail("根据指标库id查询指标和指标分组信息失败: " + e.getMessage());
        }
    }


    /**
     * 根据指标类型查询指标信息
     * @param indexType  指标类型 1=派生指标2=原子指标3=复合指标
     * @return
     */
    @Operation(summary = "根据指标类型查询指标信息")
    @GetMapping("/listByindexType/{indexType}")
    public ReturnT<List<TIndicatorInfo>> listByIndexType(@PathVariable Integer indexType) {
        try {
            List<TIndicatorInfo> indicatorInfos = indicatorInfoService.ListByIndexType(indexType);
            return ReturnT.success(indicatorInfos);
        } catch (Exception e) {
            return ReturnT.fail("根据指标类型查询指标信息失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询指标信息
     * @param current 当前页码，默认值为1
     * @param size 每页数量，默认值为10
     * @return 分页后的指标信息
     */
    @Operation(summary = "分页查询指标信息")
    @GetMapping("/page")
    public ReturnT<Page<TIndicatorInfo>> getIndicatorInfoPage(@RequestParam(defaultValue = "1") long current,
                                                              @RequestParam(defaultValue = "10") long size) {
        try {
            Page<TIndicatorInfo> page = new Page<>(current, size);
            Page<TIndicatorInfo> resultPage = indicatorInfoService.page(page);
            return ReturnT.success(resultPage);
        } catch (Exception e) {
            return ReturnT.fail("分页查询指标信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID更新指标信息
     * @param id 指标信息ID
     * @param indicatorInfo 指标信息实体
     * @return 更新结果
     */
    @Operation(summary = "根据ID更新指标信息")
    @PutMapping("/{id}")
    public ReturnT<Boolean> updateIndicatorInfo(@PathVariable Long id, @RequestBody TIndicatorInfo indicatorInfo) {
        try {
            indicatorInfo.setId(id);
            boolean result = indicatorInfoService.updateById(indicatorInfo);
            return ReturnT.success(result);
        } catch (Exception e) {
            return ReturnT.fail("更新指标信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID删除指标信息
     * @param id 指标信息ID
     * @return 删除结果
     */
    @Operation(summary = "根据ID删除指标信息")
    @DeleteMapping("/{id}")
    public ReturnT<Boolean> deleteIndicatorInfo(@PathVariable Long id) {
        try {
            return indicatorInfoService.removeIndicatorInfoById(id);
        } catch (Exception e) {
            return ReturnT.fail("删除指标信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据库连接池的接口
     * @return List<MetadataSourceVo>
     */
    @Operation(summary = "获取数据库连接池列表")
    @GetMapping("/metadata-sources")
    public ReturnT<List<MetadataSourceVo>> listMetadataSources() {
        try {
            return ReturnT.success(metadataSourceService.listMetadataSources());
        } catch (Exception e) {
            return ReturnT.fail("获取数据库连接池的数据失败: " + e.getMessage());
        }
    }

    /**
     * 根据数据库连接池的ID获取数据库表信息
     * @param sourceId 数据库连接池的ID
     * @return List<MetadataTable>
     */
    @Operation(summary = "根据数据库连接池ID获取表信息")
    @GetMapping("/metadata-tables")
    public ReturnT<List<MetadataTableEntity>> getTablesBySourceId(@RequestParam String sourceId) {

        try {
            return ReturnT.success(metadataTableService.getMetadataTableBySourceId(sourceId));
        } catch (Exception e) {
            return ReturnT.fail("根据数据库连接池的ID获取数据库表信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据数据库表ID获取数据库列信息
     * @param tableId1 数据库表ID1
     * @param tableId2 数据库表ID2
     * @return List<MetadataColumn>
     */
    @Operation(summary = "根据表ID获取列信息")
    @GetMapping
    public ReturnT<List<MetadataColumnVo>> getColumnsByTableIds(@RequestParam String tableId1, @RequestParam String tableId2) {

        try {
            return ReturnT.success(metadataColumnService.getColumnsByTableIds(CollectionUtil.newArrayList(tableId1, tableId2)));
        } catch (Exception e) {
            return ReturnT.fail("根据数据库表ID获取数据库列信息失败: " + e.getMessage());
        }
    }

    /**
     * 批量写入字段属性和指标属性
     * @param alllist
     * @return
     */
    @Operation(summary = "批量保存字段属性和指标属性")
    @PostMapping("/batch-save")
    public ReturnT<Boolean> batchSave(@RequestBody List<TColAttrUnionVo> alllist) {
        try {
            return ReturnT.success(tColAttrUnionService.batchSave(alllist));
        } catch (Exception e) {
            return ReturnT.fail("批量写入字段属性和指标属性: " + e.getMessage());
        }
    }

    /**
     * 批量写入原子指标
     * @param metadataEntityVo
     * @return
     */
    @Operation(summary = "批量写入原子指标")
    @PostMapping("/batchSaveAtom")
    public ReturnT<Boolean> batchSaveAtom(@RequestBody MetadataEntityVo metadataEntityVo) {
        try {
            return indicatorInfoService.batchSaveAtom(metadataEntityVo);
        } catch (Exception e) {
            log.error("批量写入原子指标失败", e);
            return ReturnT.fail("批量写入原子指标: " + e.getMessage());
        }
    }

    @Operation(summary = "添加派生指标")
    @PostMapping("/saveDerived")
    public ReturnT<Boolean> saveDerived(@RequestBody TIndicatorInfoVo indicatorInfoVo) {
        try {
            return ReturnT.success(indicatorInfoService.saveDerived(indicatorInfoVo));
        } catch (Exception e) {
            log.error("添加派生指标失败", e);
            return ReturnT.fail("添加派生指标: " + e.getMessage());
        }
    }

    @Operation(summary = "添加复合指标")
    @PostMapping("/saveComplex")
    public ReturnT<Boolean> saveComplex(@RequestBody TIndicatorInfoVo indicatorInfoVo) {
        try {
            return ReturnT.success(indicatorInfoService.saveComplex(indicatorInfoVo));
        } catch (Exception e) {
            log.error("添加复合指标失败", e);
            return ReturnT.fail("添加复合指标: " + e.getMessage());
        }
    }


    /**
     * 根据指标ID集合查询维度列
     * @param indicatorIds 指标ID集合
     * @return
     */
    @PostMapping("/queryDimColumnInfo")
    public ReturnT<List<TIndicatorInfo>> queryDimColumnInfo(@RequestBody List<String> indicatorIds) {
        try {
            List<TIndicatorInfo> tDimBaseInfos = indicatorInfoService.queryDimColumnInfo(indicatorIds);
            return ReturnT.success(tDimBaseInfos);
        } catch (Exception e) {
            log.error("根据指标ID集合查询维度列失败", e);
            return ReturnT.fail("根据指标ID集合查询维度列失败: " + e.getMessage());
        }
    }


    /**
     * 根据指标ID和维度列ID集合查询维度列和指标列
     * @param Ids
     * @return
     */
    @PostMapping("/queryDimIndictorList")
    public ReturnT<List<TIndicatorInfo>> queryDimIndictorList(@RequestBody List<String> Ids) {
        try {
            List<TIndicatorInfo> tDimBaseInfos = indicatorInfoService.queryDimIndictorInfo(Ids);
            return ReturnT.success(tDimBaseInfos);
        } catch (Exception e) {
            log.error("根据指标ID集合查询维度列和指标列失败", e);
            return ReturnT.fail("根据指标ID集合查询维度列和指标列失败: " + e.getMessage());
        }
    }


}




