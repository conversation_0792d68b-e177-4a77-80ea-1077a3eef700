package com.dib.index.controller;

import com.dib.index.service.TIndicatorManageService;
import com.dib.index.utils.ReturnT;
import com.dib.index.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 指标库管理接口
 */
@Tag(name = "指标库管理")
@RestController
@Slf4j
@RequestMapping("/indicator-manage")
public class TreeController {

    @Autowired
    private TIndicatorManageService tIndicatorManageService;

    /**
     * 获取树结构数据的接口
     *
     * @return 树节点列表，包含维度分组和指标域作为顶级节点
     */
    @Operation(summary = "获取指标库树形结构")
    @PostMapping("/tree")
    public ReturnT<List<TreeNode>> getTreeStructure(@RequestParam(value = "id", required = false) Long id,
                                                    @RequestParam(value = "dataLevel", required = false) Integer dataLevel) {
        return new ReturnT<>(tIndicatorManageService.buildTreeStructure(id, dataLevel));
    }

    @Operation(summary = "获取指标树形结构")
    @PostMapping("/indicator-tree")
    public ReturnT<List<TreeNode>> getIndicatorTree(@RequestBody IndicatorTreeVo indicatorTreeVo) {
        return new ReturnT<>(tIndicatorManageService.getIndicatorTree(indicatorTreeVo.getId(), indicatorTreeVo.getIndTypeList()));
    }

    @Operation(summary = "获取指标库下所有的分组信息")
    @PostMapping("/indicator-group")
    public ReturnT<List<TIndicatorManageVo>> getIndicatorGroupList(@RequestParam("id") Long id) {
        return new ReturnT<>(tIndicatorManageService.getIndicatorGroupList(id));
    }

    /**
     * 创建指标库或者指标分组接口
     *
     * @param vo
     * @return
     */
    @Operation(summary = "创建指标库或指标分组")
    @PostMapping("/create")
    public ReturnT<CreateIndicatorDomainVo> createIndicatorLibrary(@RequestBody CreateIndicatorDomainVo vo) {
        return new ReturnT<>(tIndicatorManageService.createIndicatorDomain(vo));
    }

    /**
     * 获取指标库列表数据接口
     *
     * @param pid 父节点ID(传0查询所有)
     * @return
     */
    @Operation(summary = "获取指标库列表")
    @GetMapping("/list/{pid}")
    public ReturnT<List<TIndicatorManageVo>> getIndicatorManageLis(@PathVariable Long pid) {
        return new ReturnT<>(tIndicatorManageService.getIndicatorManageList(pid));
    }

    /**
     * 根据id删除指标管理数据接口
     *
     * @param id 要删除的数据的id
     * @return 删除操作是否成功
     */
    @Operation(summary = "删除指标库")
    @DeleteMapping("/delete/{id}")
    public ReturnT<Boolean> deleteIndicatorManageById(@PathVariable Long id) {
        return tIndicatorManageService.deleteIndicatorManageById(id);
    }

    /**
     * 修改指标库数据接口
     *
     * @param updateIndicatorLibVo
     * @return
     */
    @Operation(summary = "修改指标库信息")
    @PostMapping("/editor")
    public boolean updateIndicatorManageById(@RequestBody UpdateIndicatorLibVo updateIndicatorLibVo) {
        return tIndicatorManageService.saveOrUpdateIndexLib(updateIndicatorLibVo);
    }

    @Operation(summary = "根据id查询具体的指标/维度信息")
    @PostMapping("/getInfo")
    public ReturnT<Object> getInfoById(@RequestParam("id") Long id,@RequestParam("dataLevel") Integer dataLevel) {
        return tIndicatorManageService.getInfoById(id, dataLevel);
    }
}
