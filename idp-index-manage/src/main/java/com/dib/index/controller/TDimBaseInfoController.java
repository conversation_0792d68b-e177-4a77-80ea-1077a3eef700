package com.dib.index.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.common.utils.StringUtils;
import com.dib.index.service.TDimBaseInfoService;
import com.dib.index.utils.ReturnT;
import com.dib.index.vo.DimDataContentVo;
import com.dib.index.vo.DimDataTreeVo;
import com.dib.index.vo.TDimBaseInfoVo;
import com.dib.index.vo.TDimBaseRelaColVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 维度基础信息管理
 */
@Tag(name = "维度基础信息管理")
@RestController
@RequestMapping("/dim-base-info")
@Slf4j
public class TDimBaseInfoController {

    @Autowired
    private TDimBaseInfoService tDimBaseInfoService;

    /**
     * 根据维度分组ID获取维度基础信息列表接口
     *
     * @param dimGroupId 维度所属分组ID(传0时表示查询所有)
     * @return
     */
    @Operation(summary = "根据维度分组ID获取维度基础信息列表")
    @GetMapping("/list/{dimGroupId}")
    public ReturnT<List<TDimBaseInfoVo>> listAllDimBaseInfo(@PathVariable String dimGroupId) {
        if (StringUtils.isEmpty(dimGroupId)) {
            return ReturnT.fail("请输入正确的维度分组ID");
        }
        try {
            List<TDimBaseInfoVo> tDimBaseInfoVos = tDimBaseInfoService.listAllDimBaseInfo(Long.parseLong(dimGroupId));
            return ReturnT.success(tDimBaseInfoVos);
        } catch (Exception e) {
            log.error("根据维度分组ID获取维度基础信息列表失败", e);
            return ReturnT.fail("根据维度分组ID获取维度基础信息列表失败" + e.getMessage());
        }
    }

    /**
     * 根据ID获取维度分组信息
     *
     * @param id id
     * @return
     */
    @Operation(summary = "根据ID获取维度分组信息")
    @GetMapping("/{id}")
    public TDimBaseInfoVo getDimBaseInfoById(@PathVariable Long id) {
        return tDimBaseInfoService.getDimGroupInfo(id);
    }

    /**
     * 新建维度分组
     *
     * @param tDimBaseInfoVo 分组信息
     * @return 新建操作是否成功，true 表示成功，false 表示失败
     */
    @Operation(summary = "新建维度分组")
    @PostMapping("/createOrUpdate")
    public ReturnT<Boolean> createOrUpdateDimGroup(@RequestBody TDimBaseInfoVo tDimBaseInfoVo) {
        return new ReturnT<>(tDimBaseInfoService.createOrUpdateDimGroup(tDimBaseInfoVo));
    }

    /**
     * 删除维度分组或基础信息
     *
     * @param id 要删除的维度基础信息的编号，通过路径变量传递
     * @return 删除操作是否成功，true 表示成功，false 表示失败
     */
    @Operation(summary = "删除维度分组或基础信息")
    @DeleteMapping("/{id}")
    public ReturnT<Boolean> deleteDimBaseInfo(@PathVariable Long id) {
        try {
            return tDimBaseInfoService.deleteDimBaseInfo(id);
        } catch (Exception e) {
            log.error("删除维度基础信息失败", e);
            return ReturnT.fail("删除维度基础信息失败: " + e.getMessage());
        }
    }

    /**
     * 新增维度基础信息及相关从表信息接口
     *
     * @param tDimBaseInfoVo 维度信息
     * @return 新增操作是否成功
     */
    @Operation(summary = "新增维度基础信息及相关从表信息")
    @PostMapping("/DimDetailInfo/createOrUpdate")
    public ReturnT<String> createOrUpdateDimBaseTooInfo(@RequestBody TDimBaseInfoVo tDimBaseInfoVo) {
        try {
            return tDimBaseInfoService.createOrUpdateDimBaseInfo(tDimBaseInfoVo);
        } catch (Exception e) {
            log.error("新增维度基础信息及相关从表信息失败", e);
            return ReturnT.fail("新增维度基础信息及相关从表信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据id查询维度基础信息及相关从表信息接口
     *
     * @param id 维度基础信息的编号
     * @return 包含主表及关联从表数据的维度基础信息实体
     */
    @Operation(summary = "根据ID查询维度基础信息及相关从表信息")
    @GetMapping("/DimDetailInfo/{id}")
    public TDimBaseInfoVo getDimBaseInfoTooById(@PathVariable Long id) {
        return tDimBaseInfoService.getDimBaseInfoTooById(id);
    }

    @Operation(summary = "查询维表数据")
    @GetMapping("/getDimData")
    public ReturnT<DimDataContentVo> getDimData(@RequestParam("id") Long id) {
        try {
            return tDimBaseInfoService.getDimData(id);
        } catch (Exception e) {
            log.error("查询维表数据失败", e);
            return ReturnT.fail("查询维表数据失败: " + e.getMessage());
        }
    }

    @Operation(summary = "查询维表数据生成树形结构")
    @GetMapping("/getDimDataTree")
    public ReturnT<List<DimDataTreeVo>> getDimDataTree(@RequestParam("id") Long id) {
        try {
            return tDimBaseInfoService.getDimDataTree(id);
        } catch (Exception e) {
            log.error("查询维表数据生成树形结构失败", e);
            return ReturnT.fail("查询维表数据生成树形结构失败: " + e.getMessage());
        }
    }

    @Operation(summary = "编辑维表数据")
    @PostMapping("/editDimData")
    public ReturnT<Boolean> editDimData(@RequestBody DimDataContentVo editContentVo) {
        try {
            return tDimBaseInfoService.editDimData(editContentVo);
        } catch (Exception e) {
            log.error("编辑维表数据失败", e);
            return ReturnT.fail("编辑维表数据失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取维度列表")
    @PostMapping("/getAllDimList")
    public ReturnT<List<TDimBaseInfoVo>> getAllDimList(@RequestParam(value = "ids", required = false) List<Long> ids, @RequestParam(value = "needData", required = false, defaultValue = "false") Boolean needData) {
        return ReturnT.success(tDimBaseInfoService.getAllDimList(ids, needData));
    }

    /**
     * 查询所有系统维表和指标管理模块中所有的维表信息
     *
     * @param dimType 1=查询所有系统维表接口（维表列表） 2=指标管理模块中所有的维表
     * @return 维表
     */
    @PostMapping("/queryDimTabList")
    public AjaxResult queryDimTabList(@RequestParam String dimType) {
        try {
            List<TDimBaseRelaColVo> tDimBaseInfos = tDimBaseInfoService.queryDimAllList(dimType);
            return AjaxResult.success(tDimBaseInfos);
        } catch (Exception e) {
            log.error("查询所有系统维表接口（维表列表）失败", e);
            return AjaxResult.error("查询所有系统维表接口（维表列表）失败: " + e.getMessage());
        }
    }

    /**
     * 查询指定系统维表数据接口（维表中具体的数据）
     *
     * @param dimNo 系统维表编号
     * @return 系统维表数据
     */
    @PostMapping("/queryDimDataByDimNo")
    public AjaxResult queryDimDataByDimNo(@RequestParam("dimNo") Long dimNo) {
        try {
            return tDimBaseInfoService.getDimDataList(dimNo);
        } catch (Exception e) {
            log.error("查询指定系统维表数据失败", e);
            return AjaxResult.error("查询指定系统维表数据失败: " + e.getMessage());
        }
    }

}
