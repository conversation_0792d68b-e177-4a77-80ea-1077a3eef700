package com.dib.index.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "表字段信息实体")
public class TableEntityVo {
    @Schema(description = "指标ID")
    private Long id;

    @Schema(description = "表ID")
    private String tableId;

    @Schema(description = "表名")
    private String tableName;

    @Schema(description = "字段名")
    private String name;

    @Schema(description = "字段别名")
    private String alias;

    @Schema(description = "字段标题")
    private String title;

    @Schema(description = "字段类型")
    private String type;

    @Schema(description = "字段长度")
    private Long length;

    @Schema(description = "是否可为空")
    private Boolean nullable;

    @Schema(description = "字段位置")
    private Integer position;

    @Schema(description = "字段类型分类")
    private String columnType;

    @Schema(description = "内部域表")
    private String innerdomaintable;

    @Schema(description = "数据日期类型")
    private String dataDateType;

    @Schema(description = "描述")
    private String mark;
}