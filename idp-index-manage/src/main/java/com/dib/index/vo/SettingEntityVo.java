package com.dib.index.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "连接设置实体")
public class SettingEntityVo {
    @Schema(description = "左表名")
    private String leftTable;

    @Schema(description = "左表字段")
    private String leftColumn;

    @Schema(description = "右表名")
    private String rightTable;

    @Schema(description = "右表字段")
    private String rightColumn;
}