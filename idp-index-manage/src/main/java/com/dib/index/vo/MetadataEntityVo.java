package com.dib.index.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "元数据实体")
public class MetadataEntityVo {
    @Schema(description = "编辑指标id")
    private Long oldRowId;

    @Schema(description = "分组id")
    private Long parentId;

    @Schema(description = "连接池id")
    private String sourceId;

    @Schema(description = "表字段信息列表")
    private List<TableEntityVo> tables;

    @Schema(description = "表连接关系列表")
    private List<ConnectionEntityVo> connections;
}