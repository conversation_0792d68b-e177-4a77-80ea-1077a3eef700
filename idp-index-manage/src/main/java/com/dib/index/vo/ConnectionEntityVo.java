package com.dib.index.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "表连接关系实体")
public class ConnectionEntityVo {
    @Schema(description = "源表ID")
    private String sourceTableId;

    @Schema(description = "目标表ID")
    private String targetTableId;

    @Schema(description = "连接类型")
    private String joinType;

    @Schema(description = "连接设置")
    private List<SettingEntityVo> settings;
}