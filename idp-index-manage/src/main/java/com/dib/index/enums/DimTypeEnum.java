package com.dib.index.enums;

import lombok.Getter;

/**
 * 维度分组 1=维度分组、0=普通维度、6=维度管理
 */
@Getter
public enum DimTypeEnum {
    NORMAL_DIM(0, "普通维度"),
    DIM_GROUP(1, "维度分组"),
    SYSTEM_DIM(2,"系统维度"),
    DIM_MANAGE(6, "维度管理"),
    ;
    //类型
    private final Integer type;
    //描述
    private final String desc;
    DimTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getDesc(Integer type) {
        for (DimTypeEnum dimTypeEnum : DimTypeEnum.values()) {
            if (dimTypeEnum.getType().equals(type)) {
                return dimTypeEnum.getDesc();
            }
        }
        return null;
    }

    public static DimTypeEnum getByType(Integer type) {
        for (DimTypeEnum dimTypeEnum : DimTypeEnum.values()) {
            if (dimTypeEnum.getType().equals(type)) {
                return dimTypeEnum;
            }
        }
        return null;
    }
}
