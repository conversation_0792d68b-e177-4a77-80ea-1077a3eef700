package com.dib.index.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dib.common.utils.uuid.UUID;
import com.dib.index.domain.*;
import com.dib.index.enums.DataLevelEnum;
import com.dib.index.enums.DimTypeEnum;
import com.dib.index.enums.IndTypeEnum;
import com.dib.index.mapper.TIndicatorManageMapper;
import com.dib.index.service.*;
import com.dib.index.utils.ReturnT;
import com.dib.index.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 指标管理服务实现类，继承 ServiceImpl 实现业务逻辑
 */
@Service
public class TIndicatorManageServiceImpl extends ServiceImpl<TIndicatorManageMapper, TIndicatorManage> implements TIndicatorManageService {

    @Autowired
    private TDimBaseInfoService tDimBaseInfoService;

    @Autowired
    private TIndicatorService tIndicatorService;

    @Autowired
    private TIndicatorManageTableService tIndicatorManageTableService;

    @Autowired
    private TIndicatorConnectionService tIndicatorConnectionService;

    @Override
    public List<TIndicatorManage> getAllIndicatorManages() {
        return list();
    }

    @Override
    public List<TreeNode> buildTreeStructure(Long id, Integer dataLevel) {
        List<TreeNode> treeNodes = new ArrayList<>();
        // 获取指标管理数据
        List<TIndicatorManage> indicatorManageList = null;
        // 获取指标信息数据
        List<TIndicatorInfo> indicatorInfoList = null;
        // 获取维度基础信息数据
        List<TDimBaseInfo> dimBaseInfoList = null;

        boolean isAll = true;
        if(id != null && dataLevel != null) {
            DataLevelEnum dataLevelEnum = DataLevelEnum.getByDataLevel(dataLevel);
            if(dataLevelEnum != null) {
                //判断查询的是指标还是维度
                if (dataLevelEnum == DataLevelEnum.INDICATOR_DOMAIN
                        ||dataLevelEnum == DataLevelEnum.INDICATOR_LIBRARY) {
                    //指标库/指标分组
                    LambdaQueryWrapper<TIndicatorManage> manageWrapper = new LambdaQueryWrapper<>();
                    manageWrapper.eq(TIndicatorManage::getId, id).or().eq(TIndicatorManage::getPid, id);
                    indicatorManageList = list(manageWrapper);
                    if(indicatorManageList != null
                            && indicatorManageList.stream()
                            .noneMatch(info -> ObjectUtil.equals(info.getId(), id))){
                        return treeNodes;
                    }
                    isAll = false;
                }else if(dataLevelEnum == DataLevelEnum.INDICATOR_GROUP) {
                    //查询对应指标列表
                    TIndicatorManage tIndicatorManage = getById(id);
                    if(tIndicatorManage == null) {
                        return treeNodes;
                    }
                    indicatorManageList = CollectionUtil.newArrayList(tIndicatorManage);
                    LambdaQueryWrapper<TIndicatorInfo> infoWrapper = new LambdaQueryWrapper<>();
                    infoWrapper.eq(TIndicatorInfo::getIndicatorLibNo, id);
                    indicatorInfoList = tIndicatorService.list(infoWrapper);
                    isAll = false;
                }else if(dataLevelEnum == DataLevelEnum.DIM_GROUP
                        || dataLevelEnum == DataLevelEnum.DIM
                        || dataLevelEnum == DataLevelEnum.DIM_MANAGE) {
                    //查询维度管理数据
                    LambdaQueryWrapper<TDimBaseInfo> dimWrapper = new LambdaQueryWrapper<>();
                    dimWrapper.eq(TDimBaseInfo::getId, id).or().eq(TDimBaseInfo::getDimGroupId, id);
                    dimBaseInfoList = tDimBaseInfoService.list(dimWrapper);
                    if(CollectionUtil.isEmpty(dimBaseInfoList)) {
                        return treeNodes;
                    }
                    isAll = false;
                }
            }
        }else {
            //查询全部
            indicatorManageList = list();
            indicatorInfoList = tIndicatorService.list();
            dimBaseInfoList = tDimBaseInfoService.list();
        }

        //过滤调维度列和数据期列的指标
        indicatorInfoList = Optional.ofNullable(indicatorInfoList).orElse(new ArrayList<>()).stream()
                .filter(info -> !"数据期列".equals(info.getFieldType()) && !"维度列".equals(info.getFieldType()))
                .toList();

        // 构建指标管理树
        Map<Long, TreeNode> indicatorManageNodeMap = buildIndicatorManageTree(indicatorManageList, indicatorInfoList, isAll);
        // 构建维度管理树
        Map<Long, TreeNode> dimBaseInfoNodeMap = buildDimBaseInfoTree(dimBaseInfoList, isAll);

        // 找出指标管理树的根节点
        TreeNode indicatorRoot = findRoot(indicatorManageNodeMap, id);
        // 找出维度管理树的根节点
        TreeNode dimRoot = findRoot(dimBaseInfoNodeMap, id);

        List<TreeNode> rootNodes = new ArrayList<>();
        if (dimRoot != null) {
            rootNodes.add(dimRoot);
        }
        if (indicatorRoot != null) {
            rootNodes.add(indicatorRoot);
        }

        return rootNodes;
    }

    @Override
    public List<TreeNode> getIndicatorTree(Long id, List<Integer> indTypeList) {
        List<TreeNode> treeNodeList = new ArrayList<>();

        if(id == null) {
            return treeNodeList;
        }

        //查询库里所有的指标库和指标分组信息
        LambdaQueryWrapper<TIndicatorManage> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TIndicatorManage::getDataLevel, DataLevelEnum.INDICATOR_LIBRARY.getDataLevel(),
                DataLevelEnum.INDICATOR_GROUP.getDataLevel());
        List<TIndicatorManage> manageList = list(wrapper);
        //按id分组
        Map<Long, TIndicatorManage> manageMap = manageList.stream().collect(Collectors.toMap(TIndicatorManage::getId, Function.identity()));
        //按pid分组
        Map<Long, List<TIndicatorManage>> manageGroupPidMap = manageList.stream()
                .filter(manage -> manage.getPid() != null)
                .collect(Collectors.groupingBy(TIndicatorManage::getPid));
        TIndicatorManage root = manageMap.get(id);
        if(root == null) {
            return treeNodeList;
        }

        if(Objects.equals(root.getDataLevel(), DataLevelEnum.INDICATOR_GROUP.getDataLevel())) {
            //如果当前查询的id是指标分组，则需要向上查询指标库，然后往下获取所有的指标分组信息
            Long pid = root.getPid();
            while (true) {
                TIndicatorManage parent = null;
                if(pid == null || (parent = manageMap.get(pid)) == null) {
                    break;
                }
                if(Objects.equals(parent.getDataLevel(), DataLevelEnum.INDICATOR_LIBRARY.getDataLevel())) {
                    root = parent;
                    break;
                }
                pid = parent.getPid();
            }
        }

        //获取根节点下面所有指标库/分组id
        Set<Long> manageIds = new HashSet<>();
        loopGetId(CollectionUtil.newArrayList(root), manageGroupPidMap, manageIds);

        //查询指标库下所有的指标
        LambdaQueryWrapper<TIndicatorInfo> infoWrapper = new LambdaQueryWrapper<>();
        infoWrapper.in(TIndicatorInfo::getIndicatorLibNo, manageIds)
                .notIn(TIndicatorInfo::getFieldType, "数据期列","维度列");
        if(CollectionUtil.isNotEmpty(indTypeList)) {
            infoWrapper.in(TIndicatorInfo::getIndType, indTypeList);
        }
        List<TIndicatorInfo> tIndicatorInfoList = tIndicatorService.list(infoWrapper);

        //组装数据
        TreeNode rootNode = new TreeNode();
        rootNode.setId(root.getId());
        rootNode.setUuid(UUID.randomUUID().toString());
        rootNode.setParentId(root.getPid());
        rootNode.setDataLevel(root.getDataLevel());
        rootNode.setTitleName(root.getTitleName());
        if(CollectionUtil.isNotEmpty(tIndicatorInfoList)) {
            Map<Integer, List<TreeNode>> typeMap = new HashMap<>();
            tIndicatorInfoList.stream()
                    .collect(Collectors.groupingBy(TIndicatorInfo::getIndType))
                    .forEach((indType, infoList) -> {
                        IndTypeEnum indTypeEnum = IndTypeEnum.getByType(indType);
                        if (indTypeEnum != null) {
                            List<TreeNode> list = infoList.stream().map(info -> {
                                TreeNode treeNode = new TreeNode();
                                treeNode.setId(info.getId());
                                treeNode.setParentId(info.getIndicatorLibNo());
                                treeNode.setUuid(UUID.randomUUID().toString());
                                treeNode.setTitleName(info.getTitle());
                                treeNode.setDataLevel(DataLevelEnum.INDICATOR.getDataLevel());
                                return treeNode;
                            }).collect(Collectors.toList());
                            typeMap.put(indTypeEnum.getType(), list);
                        }
                    });
            rootNode.setTypeMap(typeMap);
        }

        treeNodeList.add(rootNode);

        return treeNodeList;
    }

    private void loopGetId(List<TIndicatorManage> indicatorManageList, Map<Long, List<TIndicatorManage>> pidMap, Set<Long> ids) {
        if(CollectionUtil.isEmpty(indicatorManageList)) {
            return;
        }
        for (TIndicatorManage tIndicatorManage : indicatorManageList) {
            ids.add(tIndicatorManage.getId());
            List<TIndicatorManage> children = pidMap.get(tIndicatorManage.getId());
            loopGetId(children, pidMap, ids);
        }
    }

    @Override
    public List<TIndicatorManageVo> getIndicatorGroupList(Long id) {
        List<TIndicatorManageVo> indicatorManageVoList = new ArrayList<>();
        if(id == null) {
            return indicatorManageVoList;
        }

        //查询库里所有的指标库和指标分组信息
        LambdaQueryWrapper<TIndicatorManage> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TIndicatorManage::getDataLevel, DataLevelEnum.INDICATOR_LIBRARY.getDataLevel(),
                DataLevelEnum.INDICATOR_GROUP.getDataLevel());
        List<TIndicatorManage> manageList = list(wrapper);
        //按id分组
        Map<Long, TIndicatorManage> manageMap = manageList.stream().collect(Collectors.toMap(TIndicatorManage::getId, Function.identity()));
        TIndicatorManage root = manageMap.get(id);

        if (root == null) {
            return indicatorManageVoList;
        }

        DataLevelEnum dataLevelEnum = null;
        if((dataLevelEnum = DataLevelEnum.getByDataLevel(root.getDataLevel())) == null) {
            return indicatorManageVoList;
        }

        if(dataLevelEnum == DataLevelEnum.INDICATOR_GROUP) {
            //id为指标分组id，需要往上查询指标库
            Long pid = root.getPid();
            while (true) {
                TIndicatorManage parent = null;
                if(pid == null || (parent = manageMap.get(pid)) == null) {
                    break;
                }
                if(Objects.equals(parent.getDataLevel(), DataLevelEnum.INDICATOR_LIBRARY.getDataLevel())) {
                    root = parent;
                    break;
                }
                pid = parent.getPid();
            }
        }

        //按pid分组
        Map<Long, List<TIndicatorManage>> manageGroupPidMap = manageList.stream()
                .filter(manage -> manage.getPid() != null)
                .collect(Collectors.groupingBy(TIndicatorManage::getPid));

        //获取指标库下所有的分组信息
        Set<Long> manageIds = new HashSet<>();
        loopGetId(CollectionUtil.newArrayList(root), manageGroupPidMap, manageIds);

        manageIds.remove(root.getId());
        //获取分组列表
        for (Long manageId : manageIds) {
            TIndicatorManage tIndicatorManage = manageMap.get(manageId);
            TIndicatorManageVo vo = new TIndicatorManageVo();
            vo.setId(tIndicatorManage.getId());
            vo.setTitleName(tIndicatorManage.getTitleName());
            indicatorManageVoList.add(vo);
        }

        return indicatorManageVoList;
    }

    private Map<Long, TreeNode> buildIndicatorManageTree(List<TIndicatorManage> indicatorManageList, List<TIndicatorInfo> indicatorInfoList, boolean isAll) {
        Map<Long, TreeNode> nodeMap = new HashMap<>();

        if(CollectionUtil.isEmpty(indicatorManageList)) {
            return nodeMap;
        }

        //查询关联的模板数据
        LambdaQueryWrapper<TIndicatorManageTable> tableWrapper = new LambdaQueryWrapper<>();
        tableWrapper.in(TIndicatorManageTable::getPid, indicatorManageList.stream().map(TIndicatorManage::getId).collect(Collectors.toList()));
        List<TIndicatorManageTable> templateList = tIndicatorManageTableService.list(tableWrapper);

        Map<Long, List<TIndicatorManageTable>> templateMap = null;
        if(CollectionUtil.isNotEmpty(templateList)) {
            templateMap = templateList.stream().collect(Collectors.groupingBy(TIndicatorManageTable::getPid));
        }
        // 处理指标管理数据
        for (TIndicatorManage indicatorManage : indicatorManageList) {
            TIndicatorDetailTreeNode node = new TIndicatorDetailTreeNode();
            node.setId(indicatorManage.getId());
            node.setUuid(UUID.randomUUID().toString());
            node.setParentId(indicatorManage.getPid());
            node.setTitleName(indicatorManage.getTitleName());
            node.setDataLevel(indicatorManage.getDataLevel());
            node.setSourceId(indicatorManage.getSourceId());
            node.setConPool(indicatorManage.getConPool());
            node.setNumIndicFunc(indicatorManage.getNumIndicFunc());
            node.setCharacterIndicFunc(indicatorManage.getCharacterIndicFunc());
            if(indicatorManage.getModifyDate() != null) {
                node.setUpdateTime(DateUtil.format(indicatorManage.getModifyDate(), "yyyy-MM-dd HH:mm:ss"));
            }
            if(CollectionUtil.isNotEmpty(templateMap)
                    && templateMap.containsKey(indicatorManage.getId())) {
                List<TIndicatorManageTable> list = templateMap.get(indicatorManage.getId());
                node.setFromTableData(list);
            }
            if(!isAll) {
                node.setMark(indicatorManage.getMark());
            }
            nodeMap.put(indicatorManage.getId(),
                    isAll ? BeanUtil.copyProperties(node, TreeNode.class) : node);
        }

        // 构建维度管理树的父子关系
        for (TreeNode node : nodeMap.values()) {
            Long parentId = node.getParentId();
            if (parentId != null && nodeMap.containsKey(parentId)) {
                nodeMap.get(parentId).getChildren().add(node);
            }
        }

        if(CollectionUtil.isEmpty(indicatorInfoList)) {
            return nodeMap;
        }

        // 处理指标信息数据，关联到指标管理节点
        for (TIndicatorInfo indicatorInfo : indicatorInfoList) {
            TIndicatorDetailTreeNode node = new TIndicatorDetailTreeNode();
            node.setId(indicatorInfo.getId());
            Long parentId = indicatorInfo.getIndicatorLibNo();
            if (parentId != null && nodeMap.containsKey(parentId)) {
                node.setParentId(parentId);
                node.setUuid(UUID.randomUUID().toString());
                node.setTitleName(StrUtil.isNotBlank(indicatorInfo.getTitle()) ? indicatorInfo.getTitle() : indicatorInfo.getAlias());
                node.setDataLevel(DataLevelEnum.INDICATOR.getDataLevel());
                if(!isAll) {
                    node.setMark(indicatorInfo.getMark());
                    node.setGroup(indicatorInfo.getIndGroup());
                    node.setDataType(indicatorInfo.getDataLevel());
                    node.setExpress(indicatorInfo.getExpress());
                    node.setLength(indicatorInfo.getIndLength()+ "");
                    node.setBusinessCaliber(indicatorInfo.getBusinessCaliber());
                }
                nodeMap.get(parentId).getChildren().add(isAll ? BeanUtil.copyProperties(node, TreeNode.class) : node);
            }
        }

        return nodeMap;
    }


    private Map<Long, TreeNode> buildDimBaseInfoTree(List<TDimBaseInfo> dimBaseInfoList, boolean isAll) {
        Map<Long, TreeNode> nodeMap = new HashMap<>();

        if(CollectionUtil.isEmpty(dimBaseInfoList)) {
            return nodeMap;
        }

        // 处理维度基础信息数据
        for (TDimBaseInfo dimBaseInfo : dimBaseInfoList) {
            TDimBaseInfoTreeNode node = new TDimBaseInfoTreeNode();
            node.setId(dimBaseInfo.getId());
            node.setParentId(dimBaseInfo.getDimGroupId());
            node.setUuid(UUID.randomUUID().toString());
            node.setTitleName(dimBaseInfo.getDimName());
            node.setMark(dimBaseInfo.getMark());
            DimTypeEnum dimTypeEnum = DimTypeEnum.getByType(dimBaseInfo.getDimType());
            if(dimTypeEnum != null) {
                if (dimTypeEnum == DimTypeEnum.DIM_GROUP) {
                    node.setDimType(DimTypeEnum.DIM_GROUP.getDesc());
                    node.setDataLevel(DataLevelEnum.DIM_GROUP.getDataLevel());
                }else if(dimTypeEnum == DimTypeEnum.NORMAL_DIM) {
                    node.setDimType(DimTypeEnum.NORMAL_DIM.getDesc());
                    node.setDataLevel(DataLevelEnum.DIM.getDataLevel());
                }else if(dimTypeEnum == DimTypeEnum.DIM_MANAGE) {
                    node.setDimType(DimTypeEnum.DIM_MANAGE.getDesc());
                    node.setDataLevel(DataLevelEnum.DIM_MANAGE.getDataLevel());
                }
            }
            if(!isAll) {
                node.setDimType(DimTypeEnum.getDesc(dimBaseInfo.getDimType()));
                node.setDatabasePool(dimBaseInfo.getDatabasePool());
                node.setTableName(dimBaseInfo.getTableName());
                node.setDimName(dimBaseInfo.getDimName());
                node.setCacheTo(dimBaseInfo.getCacheTo());
            }
            nodeMap.put(dimBaseInfo.getId(),
                    isAll ? BeanUtil.copyProperties(node, TreeNode.class) : node);
        }

        // 构建维度管理树的父子关系
        for (TreeNode node : nodeMap.values()) {
            Long parentId = node.getParentId();
            if (parentId != null && nodeMap.containsKey(parentId)) {
                nodeMap.get(parentId).getChildren().add(node);
            }
        }

        return nodeMap;
    }

    private TreeNode findRoot(Map<Long, TreeNode> nodeMap, Long pid) {
        if(nodeMap == null || nodeMap.isEmpty()) {
            return null;
        }else if(nodeMap.size() == 1) {
            return nodeMap.values().iterator().next();
        }else {
            for (TreeNode node : nodeMap.values()) {
                if ((pid != null && Objects.equals(node.getId(), pid)) || node.getParentId() == null) {
                    return node;
                }
            }
        }
        return null;
    }

    @Override
    public List<TIndicatorManageVo> getIndicatorManageList(Long pid) {
        LambdaQueryWrapper<TIndicatorManage> queryWrapper = new LambdaQueryWrapper<>();

        // 添加根据父 ID 进行查询的条件
        queryWrapper.eq(TIndicatorManage::getDataLevel, DataLevelEnum.INDICATOR_LIBRARY.getDataLevel());
        if (pid != 0) {
            queryWrapper.eq(TIndicatorManage::getPid, pid);
        }

        // 根据条件查询指标管理列表
        List<TIndicatorManage> tIndicatorManages = list();
        List<TIndicatorManageVo> voList = new ArrayList<>();
        for (TIndicatorManage indicatorManage : tIndicatorManages) {
            TIndicatorManageVo tIndicatorManageVo = new TIndicatorManageVo();
            tIndicatorManageVo.setApprovalProcessEnable(indicatorManage.getApprovalProcessEnable());
            tIndicatorManageVo.setCharacterIndicFunc(indicatorManage.getCharacterIndicFunc());
            tIndicatorManageVo.setConPool(indicatorManage.getConPool());
            tIndicatorManageVo.setDataLevel(indicatorManage.getDataLevel());
            tIndicatorManageVo.setEnableVersion(indicatorManage.getEnableVersion());
            tIndicatorManageVo.setId(indicatorManage.getId());
            tIndicatorManageVo.setNumIndicFunc(indicatorManage.getNumIndicFunc());
            tIndicatorManageVo.setOrderNo(indicatorManage.getOrderNo());
            tIndicatorManageVo.setSourceId(indicatorManage.getSourceId());
            Long id = indicatorManage.getId();
            if (id != null) {
                List<TIndicatorManageTable> tables = tIndicatorManageTableService.list(new LambdaQueryWrapper<TIndicatorManageTable>().eq(TIndicatorManageTable::getPid, id));
                tIndicatorManageVo.setFromTableData(tables);
            }
            tIndicatorManageVo.setTitleName(indicatorManage.getTitleName());
            tIndicatorManageVo.setMark(indicatorManage.getMark());
            tIndicatorManageVo.setVersionManageEnable(indicatorManage.getVersionManageEnable());
            voList.add(tIndicatorManageVo);
        }
        voList = voList.stream().filter(t-> Objects.equals(t.getDataLevel(), DataLevelEnum.INDICATOR_LIBRARY.getDataLevel())).collect(Collectors.toList());
        return voList;
    }

    @Override
    public List<TIndicatorManageVo> getIndicatorManageByTitle(String title) {
        return null;
    }

    @Override
    @Transactional
    public CreateIndicatorDomainVo createIndicatorDomain(CreateIndicatorDomainVo vo) {
        DataLevelEnum dataLevelEnum = null;
        if(vo == null || vo.getDataLevel() == null ||
                (dataLevelEnum = DataLevelEnum.getByDataLevel(vo.getDataLevel())) == null) {
            return null;
        }

        TIndicatorManage indicatorManage = new TIndicatorManage();
        indicatorManage.setId(vo.getId());
        if(DataLevelEnum.INDICATOR_GROUP == dataLevelEnum) {
            indicatorManage.setTitleName(vo.getTitleName());
            indicatorManage.setMark(vo.getMark());
            indicatorManage.setDataLevel(DataLevelEnum.INDICATOR_GROUP.getDataLevel());
            indicatorManage.setPid(vo.getPid());
        }else if(DataLevelEnum.INDICATOR_LIBRARY == dataLevelEnum) {
            indicatorManage.setTitleName(vo.getTitleName());
            indicatorManage.setSourceId(vo.getSourceId());
            indicatorManage.setConPool(vo.getConPool());
            indicatorManage.setMark(vo.getMark());
            indicatorManage.setPid(vo.getPid());
            indicatorManage.setNumIndicFunc(vo.getNumIndicFunc());
            indicatorManage.setCharacterIndicFunc(vo.getCharacterIndicFunc());
            indicatorManage.setApprovalProcessEnable(vo.getApprovalProcessEnable());
            indicatorManage.setVersionManageEnable(vo.getVersionManageEnable());
            indicatorManage.setEnableVersion(vo.getEnableVersion());
            // 设置数据级别为指标库
            indicatorManage.setDataLevel(DataLevelEnum.INDICATOR_LIBRARY.getDataLevel());

            List<TIndicatorManageTable> tIndicatorManageTableList = vo.getFromTableData();
            if (CollUtil.isNotEmpty(tIndicatorManageTableList)) {
                if(indicatorManage.getId() != null) {
                    //id不为空，则更新，需要删除旧模板
                    tIndicatorManageTableService.remove(new LambdaQueryWrapper<TIndicatorManageTable>().eq(TIndicatorManageTable::getPid, indicatorManage.getId()));
                }

                List<TIndicatorManageTable> templateList = new ArrayList<>();
                for (TIndicatorManageTable tIndicatorManageTable : tIndicatorManageTableList) {
                    tIndicatorManageTable.setPid(indicatorManage.getId());
                    tIndicatorManageTable.setCreateDate(new Date());
                    tIndicatorManageTable.setModifyDate(new Date());
                    tIndicatorManageTable.setAllownull(vo.hashCode() == 0 ? 0 : 1);
                    templateList.add(tIndicatorManageTable);
                }
                tIndicatorManageTableService.saveBatch(templateList);
                List<String> templateIds = templateList.stream().map(TIndicatorManageTable::getId).collect(Collectors.toList());
                indicatorManage.setTableDataIds(templateIds);
            }
        }

        // 插入数据库
        saveOrUpdate(indicatorManage);
        return vo;
    }

    /**
     * 递归获取指标管理数据
     * @param id id
     * @return  列表
     */
    private List<TIndicatorManage> loopGetManageById(Long id) {
        List<TIndicatorManage> list = new ArrayList<>();
        if(id == null) {
            return list;
        }
        List<TIndicatorManage> tIndicatorManage = list(new LambdaQueryWrapper<TIndicatorManage>()
                .eq(TIndicatorManage::getId, id).or().eq(TIndicatorManage::getPid, id));

        if(CollectionUtil.isEmpty(tIndicatorManage)) {
            return list;
        }

        for (TIndicatorManage indicatorManage : tIndicatorManage) {
            if(Objects.equals(indicatorManage.getId(), id)) {
                list.add(indicatorManage);
            }else {
                list.addAll(loopGetManageById(indicatorManage.getId()));
            }
        }

        return list;
    }

    @Override
    @Transactional
    public ReturnT<Boolean> deleteIndicatorManageById(Long id) {
        ReturnT<Boolean> returnT = new ReturnT<>();
        //获取与该指标库关联的指标库/分组
        List<TIndicatorManage> manageList = loopGetManageById(id);
        if(CollectionUtil.isEmpty(manageList)) {
            returnT.setMsg("指标库或指标分组不存在");
            returnT.setData(false);
            return returnT;
        }
        //指标库/分组id列表
        List<Long> manageIds = manageList.stream().map(TIndicatorManage::getId).distinct().collect(Collectors.toList());
        //根据类型进行分组
        Map<Integer, List<TIndicatorManage>> map = manageList.stream().collect(Collectors.groupingBy(TIndicatorManage::getDataLevel));
        //指标库列表
        List<TIndicatorManage> libraryList = map.get(DataLevelEnum.INDICATOR_LIBRARY.getDataLevel());

        //查询指标列表
        LambdaQueryWrapper<TIndicatorInfo> indicatorWrapper = new LambdaQueryWrapper<>();
        indicatorWrapper.in(TIndicatorInfo::getIndicatorLibNo, manageIds);
        List<TIndicatorInfo> tIndicatorInfoList = tIndicatorService.list(indicatorWrapper);

        if(CollectionUtil.isNotEmpty(tIndicatorInfoList)) {
            //查询关联指标
            List<Long> ids = tIndicatorInfoList.stream().map(TIndicatorInfo::getId).collect(Collectors.toList());
            List<TIndicatorInfo> relateList = tIndicatorService.list(new LambdaQueryWrapper<TIndicatorInfo>()
                    .in(TIndicatorInfo::getRelatedIndicatorId,ids));
            if(CollectionUtil.isNotEmpty(relateList)) {
                List<Long> relateIds = relateList.stream().map(TIndicatorInfo::getId).collect(Collectors.toList());
                if(!CollectionUtil.containsAll(ids, relateIds)) {
                    //如果关联的指标列表并没有全部包含在即将进行删除的指标中，则不允许删除
                    returnT.setMsg("其他指标库或分组存在关联的指标信息，不允许删除！");
                    returnT.setData(false);
                    return returnT;
                }
            }
            //筛选原子指标，删除表关联信息
            List<TIndicatorInfo> atomInfoList = tIndicatorInfoList.stream()
                    .filter(info -> ObjectUtil.equal(info.getIndType(), IndTypeEnum.ATOMIC_INDEX.getType())).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(atomInfoList)) {
                List<String> connectionIds = atomInfoList.stream().map(TIndicatorInfo::getConnectionId)
                        .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
                tIndicatorConnectionService.remove(new LambdaQueryWrapper<TIndicatorConnection>().in(TIndicatorConnection::getConnectionId, connectionIds));
            }
            //删除指标信息
            tIndicatorService.removeByIds(ids);
        }

        if(CollectionUtil.isNotEmpty(libraryList)) {
            //指标库存在，需要查询关联的模板信息
            List<String> templateIds = libraryList.stream()
                    .map(TIndicatorManage::getTableDataIds)
                    .filter(CollectionUtil::isNotEmpty)
                    .flatMap(Collection::stream).distinct()
                    .collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(templateIds)) {
                //删除模板信息
                tIndicatorManageTableService.removeByIds(templateIds);
            }
        }

        removeByIds(manageIds);
        return ReturnT.success(true);
    }

    @Override
    public boolean saveOrUpdateIndexLib(UpdateIndicatorLibVo updateIndicatorLibVo) {

        if (updateIndicatorLibVo.getId() == null) {
            return false;
        }
        TIndicatorManage indicatorManage = new TIndicatorManage();
        indicatorManage.setId(updateIndicatorLibVo.getId());
        indicatorManage.setPid(updateIndicatorLibVo.getPid());
        indicatorManage.setTitleName(updateIndicatorLibVo.getTitleName());
        indicatorManage.setConPool(updateIndicatorLibVo.getConPool());
        indicatorManage.setMark(updateIndicatorLibVo.getMark());
        indicatorManage.setNumIndicFunc(updateIndicatorLibVo.getNumIndicFunc());
        indicatorManage.setCharacterIndicFunc(updateIndicatorLibVo.getCharacterIndicFunc());
        indicatorManage.setApprovalProcessEnable(updateIndicatorLibVo.getApprovalProcessEnable());
        indicatorManage.setVersionManageEnable(updateIndicatorLibVo.getVersionManageEnable());
        indicatorManage.setEnableVersion(updateIndicatorLibVo.getEnableVersion());
        indicatorManage.setDataLevel(updateIndicatorLibVo.getDataLevel());
        updateById(indicatorManage);
        return true;
    }

    @Override
    public ReturnT<Object> getInfoById(Long id, Integer dataLevel) {
        ReturnT<Object> returnT = new ReturnT<>();
        DataLevelEnum byDataLevel = DataLevelEnum.getByDataLevel(dataLevel);
        if(byDataLevel == null) {
            return null;
        }

        Object data = null;
        switch (byDataLevel) {
            //维度
            case DIM: data = tDimBaseInfoService.getById(id); break;
            //维度分组
            case DIM_GROUP:
            case INDICATOR_DOMAIN:
            case INDICATOR_LIBRARY:
            case INDICATOR_GROUP:
                data = getById(id);
                break;
            case INDICATOR:
                data = tIndicatorService.getIndicatorInfoById(id);
                break;
        }

        returnT.setData(data);
        return returnT;
    }
}