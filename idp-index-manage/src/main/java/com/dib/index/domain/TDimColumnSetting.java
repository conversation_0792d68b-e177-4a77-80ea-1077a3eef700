package com.dib.index.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 维度字段设置实体类，对应数据库表 t_dim_column_setting
 */

@TableName("t_dim_column_setting")
@Data
public class TDimColumnSetting {
    /**
     * 维度字段设置的唯一编号，作为主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 字段名称
     */
    @TableField("column_name")
    private String columnName;
    /**
     * 字段描述
     */
    @TableField("column_comment")
    private String columnComment;
    /**
     * 字段长度
     */
    @TableField("length")
    private Integer length;
    /**
     * 小数位数
     */
    @TableField("decimal_digits")
    private Integer decimalDigits;
    /**
     * 字段类型
     */
    @TableField("column_type")
    private String columnType;
    /**
     * 是否为ID主键字段
     */
    @TableField("enable_pk")
    private Boolean enablePk;
    /**
     * 是否为文字类型字段
     */
    @TableField("enable_text")
    private String enableText;
    /**
     * 创建该字段设置信息的用户编号
     */
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    private String creatorId;
    /**
     * 创建该字段设置信息的用户姓名
     */
    @TableField(value = "creator_name", fill = FieldFill.INSERT)
    private String creatorName;
    /**
     * 创建该字段设置信息的日期
     */
    @TableField(value = "create_date", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    /**
     * 最后修改该字段设置信息的用户
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private String modifier;
    /**
     * 最后修改该字段设置信息的用户编号
     */
    @TableField(value = "modifier_id", fill = FieldFill.INSERT_UPDATE)
    private String modifierId;
    /**
     * 最后修改该字段设置信息的日期
     */
    @TableField(value = "modify_date", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyDate;
    /**
     * 关联的维度表编号
     */
    @TableField("dim_no")
    private Long dimNo;
}