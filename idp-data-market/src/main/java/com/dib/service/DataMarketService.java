package com.dib.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dib.domin.dto.DataSetAndColumnAndIndexDto;
import com.dib.domin.dto.DataSetDto;
import com.dib.domin.entity.DataSetEntity;

import java.util.List;
import java.util.Map;

public interface DataMarketService extends IService<DataSetEntity> {

    boolean saveDataSetDirectory(DataSetAndColumnAndIndexDto dataSetAndColumnAndIndexDto);

    boolean updateDataSetDirectory(DataSetAndColumnAndIndexDto dataSetAndColumnAndIndexDto);

    boolean deleteDataSetDirectory(List<String> ids);

    List<DataSetDto> getTreeDataSet(DataSetEntity dataSetDirectory, String parentId);

    DataSetAndColumnAndIndexDto getDataSet(String id);

    Map<String, List<? extends Object>> queryRelatedRelationships(DataSetEntity dataSetEntity);


    List<DataSetEntity> getDataLayerDataSet(DataSetEntity dataSetEntity, String parentId);

    String getCreateSql(DataSetAndColumnAndIndexDto dataSetAndColumnAndIndexDto);

    boolean updateDataSetList(List<DataSetAndColumnAndIndexDto> dataSetAndColumnAndIndexDtoList);
}
