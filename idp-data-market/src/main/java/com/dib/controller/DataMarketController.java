package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.dto.DataSetAndColumnAndIndexDto;
import com.dib.domin.entity.DataSetEntity;
import com.dib.domin.enums.DataHierarchy;
import com.dib.service.DataMarketService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "数仓集市")
@RestController
@RequestMapping("/api/dataMarket")
public class DataMarketController {

    @Autowired
    private DataMarketService dataMarketService;

    @Operation(summary = "新增数据集", description = "新增数据集")
    @PostMapping("saveDataSet")
    public AjaxResult saveDataSet(@RequestBody DataSetAndColumnAndIndexDto dataSetAndColumnAndIndexDto) {
        return AjaxResult.success(dataMarketService.saveDataSetDirectory(dataSetAndColumnAndIndexDto));
    }

    @Operation(summary = "获取sql", description = "根据id修改数据集")
    @PostMapping("getCreateSql")
    public AjaxResult getCreateSql(@RequestBody DataSetAndColumnAndIndexDto dataSetAndColumnAndIndexDto){
        String createSql = dataMarketService.getCreateSql(dataSetAndColumnAndIndexDto);
        if (StringUtils.isNotBlank(createSql)){
            return AjaxResult.success("创建sql成功",createSql);
        }else {
            return AjaxResult.error("创建sql失败");
        }
    }

    @Operation(summary = "修改数据集", description = "根据id修改数据集")
    @PostMapping("updateDataSet")
    public AjaxResult updateDataSet(@RequestBody DataSetAndColumnAndIndexDto dataSetAndColumnAndIndexDto) {
        if (dataSetAndColumnAndIndexDto.getDataHierarchy().equals("ODS")){
            return AjaxResult.error("ODS层不支持修改！！！");
        }
        return AjaxResult.success(dataMarketService.updateDataSetDirectory(dataSetAndColumnAndIndexDto));
    }

    @Operation(summary = "修改数据集", description = "根据id修改数据集")
    @PostMapping("updateDataSetList")
    public AjaxResult updateDataSetList(@RequestBody List<DataSetAndColumnAndIndexDto> dataSetAndColumnAndIndexDtoList) {
        if (dataSetAndColumnAndIndexDtoList == null || dataSetAndColumnAndIndexDtoList.isEmpty()) {
            return AjaxResult.success(); // 或根据实际需求处理空列表情况
        }

        for (DataSetAndColumnAndIndexDto dto : dataSetAndColumnAndIndexDtoList) {
            if (DataHierarchy.ODS.getCode().equals(dto.getDataHierarchy())) {
                return AjaxResult.error("ODS层不支持修改！");
            }
        }

        return AjaxResult.success(dataMarketService.updateDataSetList(dataSetAndColumnAndIndexDtoList));
    }

    @Operation(summary = "删除数据集", description = "根据id删除数据集")
    @PostMapping("deleteDataSet")
    public AjaxResult deleteDataSet(@RequestBody List<String> ids) {
        return AjaxResult.success(dataMarketService.deleteDataSetDirectory(ids));
    }


    @Operation(summary = "获取目录树", description = "获取目录树")
    @PostMapping("getTreeDataSet")
    public AjaxResult getTreeDataSet(@RequestBody DataSetEntity dataSetEntity) {
        return AjaxResult.success(dataMarketService.getTreeDataSet(dataSetEntity,null));
    }

    @Operation(summary = "获取数据层下所有数据集", description = "获取数据层下所有数据集")
    @PostMapping("getDataLayerDataSet")
    public AjaxResult getDataLayerDataSet(@RequestBody DataSetEntity dataSetEntity) {
        return AjaxResult.success(dataMarketService.getDataLayerDataSet(dataSetEntity,null));
    }

    @Operation(summary = "获取事实表详细信息", description = "获取事实表详细信息")
    @PostMapping("getDataSet")
    public AjaxResult getDataSet(@RequestParam String id) {
        return AjaxResult.success(dataMarketService.getDataSet(id));
    }

    @Operation(summary = "获取数据集下关联关系", description = "获取数据集下关联关系")
    @PostMapping("queryRelatedRelationships")
    public AjaxResult queryRelatedRelationships(@RequestBody DataSetEntity dataSetEntity) {
        return AjaxResult.success(dataMarketService.queryRelatedRelationships(dataSetEntity));
    }


}
