package com.dib.controller;

import com.dib.common.core.domain.AjaxResult;
import com.dib.domin.entity.TableRelation;
import com.dib.service.TableRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@Tag(name = "数仓集市-关联关系")
@RestController
@RequestMapping("/api/tableRelation")
public class TableRelationController {

    @Autowired
    private TableRelationService tableRelationService;

    @Operation(summary = "创建表关系")
    @PostMapping("/createTableRelation")
    public AjaxResult createTableRelation(@RequestBody TableRelation tableRelation) {
        return AjaxResult.success(tableRelationService.createTableRelation(tableRelation));
    }


    @Operation(summary = "根据ID删除表之间的关系")
    @PostMapping("/delTableRelationById")
    public AjaxResult delTableRelationById(@RequestParam String id) {
        return AjaxResult.success(tableRelationService.delTableRelation(id));
    }

    @Operation(summary = "查看属性关联关系", description = "查看属性关联关系")
    @PostMapping("/selectTableColumnRelation")
    public AjaxResult selectTableColumnRelation(@RequestBody TableRelation tableRelation) {
        return AjaxResult.success(tableRelationService.selectTableColumnRelation(tableRelation));
    }

    @Operation(summary = "根据id修改表关系")
    @PostMapping("/updateTableRelation")
    public AjaxResult updateTableRelation(@RequestBody TableRelation tableRelation) {
        return AjaxResult.success(tableRelationService.updateTableRelation(tableRelation));
    }

}
