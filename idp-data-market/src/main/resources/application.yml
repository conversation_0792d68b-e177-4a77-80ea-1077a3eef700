# 项目相关配置
idp:
  # 名称
  name: ${spring.application.name}
  # 版本
  version: 3.8.2
  # 版权年份
  copyrightYear: 2022
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoshui/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /Users/<USER>/work/bigdata/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

dibase:
  login-exclude-url:

# 开发环境配置
server:
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100
spring:
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
      - org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration
      - org.springframework.boot.autoconfigure.mongo.MongoReactiveAutoConfiguration
      - org.springframework.boot.autoconfigure.data.mongo.MongoRepositoriesAutoConfiguration
      - org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration
      - org.springframework.boot.autoconfigure.mail.MailSenderAutoConfiguration
# 日志配置
logging:
  level:
    com.dib: info
    org.springframework: info

mybatis-plus:
  # mapper.xml文件扫描
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  type-aliases-package: com.dib.**.domain
  global-config:
    # 数据库相关配置
    db-config:
      # 主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      # 字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: NOT_NULL
      # 驼峰下划线转换
      column-underline: true
      # 逻辑删除
      logic-delete-value: 0
      logic-not-delete-value: 1
      # 数据库类型
      db-type: mysql
    banner: false
  # mybatis原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    type-handlers-package: com.dib.core.handler

sa-token:
  token-name: Authorization
  timeout: 2592000
  maxLoginCount: -1
  active-timeout: 14400  # 4小时无操作失效
  is-concurrent: true
  is-share: false
  token-style: simple-uuid
  is-log: true
  token-prefix: Bearer
  is-read-cookie: false
  # 配置Sa-Token单独使用的rendis连接
  alone-redis :
    host: *************
    password: Dib123#@!
    port: 6379
    database: 0
    timeout: 10s

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping:
